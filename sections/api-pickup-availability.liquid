{% comment %}theme-check-disable UndefinedObject{% endcomment %}
{%- assign pick_up_availabilities = product_variant.store_availabilities | where: 'pick_up_enabled', true -%}

{%- if pick_up_availabilities.size > 0 -%}
  <popup-component>
    <div class="pickup__preview pickup__preview--primary">
      {%- liquid
        assign closest_location = pick_up_availabilities.first

        if closest_location.available
          render 'icon-check'
        else
          render 'icon-cancel'
        endif
      -%}

      <div class="pickup__info">
        {%- if closest_location.available -%}
          <p class="pickup__info__text">
            {{
              'products.product.pickup_availability.pick_up_available_at_html'
              | t: location_name: closest_location.location.name
            }}
          </p>
          <p class="pickup__info__timing small">
            {{ closest_location.pick_up_time }}
          </p>
          <button
            id="ShowPickupAvailabilityDrawer"
            class="pickup__button text-link small"
            aria-haspopup="dialog"
            data-popup-open
          >
            {%- if pick_up_availabilities.size == 1 -%}
              {{ 'products.product.pickup_availability.view_store_info' | t }}
            {%- else -%}
              {{ 'products.product.pickup_availability.check_other_stores' | t }}
            {%- endif -%}
          </button>
        {%- else -%}
          <p class="pickup__info__text">
            {{
              'products.product.pickup_availability.pick_up_unavailable_at_html'
              | t: location_name: closest_location.location.name
            }}
          </p>
          {%- if pick_up_availabilities.size > 1 -%}
            <button
              id="ShowPickupAvailabilityDrawer"
              class="pickup__button text-link small"
              aria-haspopup="dialog"
              data-popup-open
            >
              {{ 'products.product.pickup_availability.check_other_stores' | t }}
            </button>
          {%- endif -%}
        {%- endif -%}
      </div>
    </div>

    <dialog
      class="drawer"
      aria-modal="true"
      aria-labelledby="PickupAvailabilityHeading"
      data-pickup-drawer
      data-scroll-lock-required
    >
      <form method="dialog">
        <button class="visually-hidden no-js" aria-label="{{ 'general.accessibility.close' | t }}"></button>
      </form>

      <div class="drawer__inner" data-scroll-lock-scrollable>
        <div class="drawer__head">
          <h3 class="caps" id="PickupAvailabilityHeading">
            {{- 'products.product.pickup_availability.pickup_options' | t -}}
          </h3>

          <button
            type="button"
            class="drawer__close"
            data-popup-close
            autofocus
            aria-label="{{ 'general.accessibility.close' | t }}"
          >
            {%- render 'icon-cancel' -%}
          </button>
        </div>

        <div class="drawer__body" data-pickup-drawer-body data-scroll-lock-scrollable>
          <div class="pickup__product__wrap">
            <p class="pickup__product__title">
              {{- product_variant.product.title | escape -}}
            </p>
            {%- unless product_variant.product.has_only_default_variant -%}
              <p class="pickup__variant">
                {%- for product_option in product_variant.product.options_with_values -%}
                  <span class="badge">
                    <span class="text-light">{{ product_option.name | escape }}</span
                    ><span class="divide">&nbsp;|&nbsp;</span>
                    {%- for value in product_option.values -%}
                      {%- if product_option.selected_value == value -%}
                        {{ value | escape }}
                      {%- endif -%}
                    {%- endfor -%}
                  </span>
                {%- endfor -%}
              </p>
            {%- endunless -%}
          </div>

          <ul
            class="pickup__list"
            role="list"
            aria-label="{{ 'products.product.pickup_availability.pickup_options' | t }}"
            data-store-availability-drawer-content
            tabindex="0"
          >
            {%- for availability in pick_up_availabilities -%}
              <li class="pickup__list__item">
                <p class="strong">
                  {%- if availability.available -%}
                    {%- render 'icon-check' -%}
                  {%- else -%}
                    {%- render 'icon-cancel' -%}
                  {%- endif -%}
                  {{ availability.location.name | escape }}
                </p>

                {%- if availability.available -%}
                  <p class="small">
                    <em>
                      {{- 'products.product.pickup_availability.pick_up_available' | t }},
                      {{ availability.pick_up_time | downcase -}}
                    </em>
                  </p>
                {%- endif -%}

                {%- assign address = availability.location.address -%}
                <address class="pickup__address">
                  {{ address | format_address }}

                  {%- if address.phone.size > 0 -%}
                    {%- liquid
                      assign address_phone = address.phone
                      assign phone_ally = address_phone | split: '' | join: ' '
                    -%}
                    <a aria-label="{{ phone_ally }}" href="tel:{{ address_phone }}">{{ address_phone }}</a>
                  {%- endif -%}
                </address>
              </li>
            {%- endfor -%}
          </ul>
        </div>
      </div>
    </dialog>
  </popup-component>
{%- endif -%}
