<!-- /sections/section-blog.liquid -->
{%- liquid
  assign blog = blogs[section.settings.blog_name]
  assign blog_title = section.settings.title
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign heading_tag = 'h2'
  assign articles_per_row = section.settings.grid
  assign columns_medium = 3
  if articles_per_row == 2 or articles_per_row == 4
    assign columns_medium = 2
  endif

  unless section.settings.heading_tag == 'automatic' or section.settings.heading_tag == blank
    assign heading_tag = section.settings.heading_tag
  endunless
-%}

{%- style -%}
  #Blog--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;

    --COLUMNS: {{ articles_per_row }};
    --COLUMNS-MEDIUM: {{ columns_medium }};
  }
{%- endstyle -%}

<section
  id="Blog--{{ section.id }}"
  class="{{ section.settings.width }} blog-section section-padding {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="blog"
>
  {% if blog_title != blank %}
    <{{ heading_tag }} class="page__heading blog__title {{ section.settings.heading_font_size }}"
      ><a href="{{ blog.url | default: '#' }}">{{ blog_title }}</a></{{ heading_tag }}
    >
  {% endif %}

  {%- render 'blog-posts', blog_articles: blog.articles, is_blog_page: false -%}
</section>

{% schema %}
{
  "name": "Blog posts",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "blog",
      "id": "blog_name",
      "label": "Blog"
    },
    {
      "type": "range",
      "id": "aspect_ratio",
      "min": 0.5,
      "max": 1.5,
      "step": 0.1,
      "unit": ":1",
      "label": "Article image aspect ratio",
      "info": "Wide to tall",
      "default": 1
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Blog posts"
    },
    {
      "type": "select",
      "id": "heading_font_size",
      "label": "Heading size",
      "default": "heading-medium",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "select",
      "id": "title_font_size",
      "label": "Title size",
      "default": "heading-small",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "header",
      "content": "Articles"
    },
    {
      "type": "checkbox",
      "id": "show_tags",
      "label": "Show tags",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "label": "Show author",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "label": "Show date",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_button",
      "label": "Show 'Read more' button",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_comments",
      "label": "Show comment count",
      "default": false
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full-padded",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"}
      ]
    },
    {
      "type": "range",
      "id": "grid",
      "min": 1,
      "max": 4,
      "step": 1,
      "label": "Articles per row",
      "default": 4
    },
    {
      "type": "range",
      "id": "rows",
      "min": 1,
      "max": 5,
      "step": 1,
      "label": "Number of rows",
      "default": 1
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 32
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "automatic",
      "options": [
        {"value": "automatic", "label": "Automatic"},
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    },
    {
      "type": "select",
      "id": "title_tag",
      "label": "Title SEO tag",
      "default": "automatic",
      "options": [
        {"value": "automatic", "label": "Automatic"},
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    }
  ],
  "presets": [
    {
      "name": "Blog posts"
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "footer"]
  }
}
{% endschema %}
