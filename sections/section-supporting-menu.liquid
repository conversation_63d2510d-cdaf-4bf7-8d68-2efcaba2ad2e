<!-- /sections/section-supporting-menu.liquid -->

{%- liquid
  assign languages = false
  assign countries = false
  assign show_locale_selector = section.settings.show_locale_selector
  assign show_globe_icon = section.settings.show_globe_icon
  assign show_country_selector = section.settings.show_country_selector
  assign show_country_name = section.settings.show_country_name
  assign show_country_flag = section.settings.show_country_flag
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign selected_color_scheme = section.settings.color_scheme | strip
  assign lines_and_border = settings.color_schemes[selected_color_scheme].settings.lines_and_border

  if show_locale_selector and localization.available_languages.size > 1
    assign languages = true
  endif

  if show_country_selector and localization.available_countries.size > 1
    assign countries = true
  endif

  capture localization_class
    echo 'supporting-menu__item supporting-menu__item--localization'

    if lines_and_border.alpha == 0 or lines_and_border == blank
      echo ' supporting-menu__item--no-borders'
    endif
  endcapture
-%}

{%- style -%}
  #SupportingMenu--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;
  }
{%- endstyle -%}

<section
  id="SupportingMenu--{{ section.id }}"
  class="supporting-menu section-padding {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="supporting-menu"
>
  <div class="supporting-menu__inner wrapper--full-padded">
    {%- if languages or countries -%}
      <div class="{{ localization_class }}">
        {%- render 'localization',
          unique: section.id,
          show_locale_selector: show_locale_selector,
          show_globe_icon: show_globe_icon,
          show_country_selector: show_country_selector,
          show_country_flag: show_country_flag,
          show_country_name: show_country_name,
          class: 'footer',
          show_icon: true
        -%}
      </div>
    {%- endif -%}

    <div class="supporting-menu__item supporting-menu__item--copyright">
      <ul class="supporting-menu__copyright inline-list {{ section.settings.text_font_size }}">
        <li>
          <a href="{{ shop.secure_url }}"
            >&copy; {{ shop.name }}
            {{ 'now' | date: '%Y' -}}
          </a>
        </li>
        {% for link in linklists[section.settings.linklist].links %}
          <li>
            <a href="{{ link.url }}">{{ link.title }}</a>
          </li>
        {% endfor %}

        <!-- The following lines fix Lighthouse security warnings on the Shopify link. -->
        {%- liquid
          assign powered_by_link_html = powered_by_link
          if powered_by_link_html contains 'rel="'
            assign powered_link_rel_text = powered_by_link_html | split: 'rel="'
            assign powered_link_rel_text = powered_link_rel_text[1] | split: '"' | first
            assign powered_link_rel = 'rel="' | append: powered_link_rel_text | append: '"'
            assign powered_by_link_html = powered_by_link_html | replace_first: powered_link_rel, ''
          endif
          if powered_by_link_html contains '<a '
            assign powered_by_link_html = powered_by_link_html | replace: '<a ', '<a rel="noopener" '
          endif
          assign powered_by_link = powered_by_link_html
        -%}

        <!-- Remove the following line to delete 'Powered by Shopify' from your footer -->
        <li data-powered-link>{{ powered_by_link }}</li>
        <!-- Do not delete below this line -->
      </ul>
    </div>

    {%- if section.settings.footer_payment_enable -%}
      {%- unless shop.enabled_payment_types == empty -%}
        <div class="supporting-menu__item supporting-menu__item--payment">
          <ul class="supporting-menu__payment payment-icons inline-list">
            {%- for type in shop.enabled_payment_types -%}
              <li>{{ type | payment_type_svg_tag: class: 'payment-icon' }}</li>
            {%- endfor -%}
          </ul>
        </div>
      {%- endunless -%}
    {%- endif -%}
  </div>
</section>

{% schema %}
{
  "name": "Supporting menu",
  "limit": 1,
  "settings": [
    {
      "type": "header",
      "content": "Bottom bar"
    },
    {
      "type": "link_list",
      "id": "linklist",
      "label": "Link List",
      "info": "This menu won't show dropdown items."
    },
    {
      "type": "select",
      "id": "text_font_size",
      "label": "Text size",
      "info": "Automatically generated by the base size.",
      "default": "body-small",
      "options": [
        {"value": "body-x-small", "label": "Extra small"},
        {"value": "body-small", "label": "Small"},
        {"value": "body-medium", "label": "Medium"},
        {"value": "body-large", "label": "Large"},
        {"value": "body-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "checkbox",
      "id": "footer_payment_enable",
      "label": "Show payment types",
      "default": true
    },
    {
      "type": "header",
      "content": "Language selector",
      "info": "To add a language, go to your [language settings.](/admin/settings/languages)"
    },
    {
      "type": "checkbox",
      "id": "show_locale_selector",
      "label": "Show language selector",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_globe_icon",
      "label": "Show globe icon",
      "default": true
    },
    {
      "type": "header",
      "content": "Country/Region selector",
      "info": "To add a country/region, go to your [markets settings](/admin/settings/markets)."
    },
    {
      "type": "checkbox",
      "id": "show_country_selector",
      "label": "Show country/region selector",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_country_name",
      "label": "Show country name",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_country_flag",
      "label": "Show country flag",
      "default": true
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 30
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 30
    }
  ],
  "presets": [
    {
      "name": "Supporting menu"
    }
  ],
  "enabled_on": {
    "groups": ["footer"]
  }
}
{% endschema %}
