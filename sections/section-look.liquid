<!-- /sections/section-look.liquid -->

{%- liquid
  assign look_dots = ''
  assign look_slider = ''
  assign enable_aspect_ratio = section.settings.enable_aspect_ratio
  assign image_aspect_ratio = 1 | divided_by: section.settings.image_aspect_ratio
  assign image = section.settings.image
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign dot_color = section.settings.dot_color
  assign dot_background = section.settings.dot_background
  assign show_dots = section.settings.show_dots

  assign button_style = section.settings.button_style
  if button_style == 'btn--text' and section.settings.show_arrow
    assign button_style = button_style | append: ' btn--text-no-underline'
  endif

  unless enable_aspect_ratio
    if image
      assign image_aspect_ratio = image.aspect_ratio
    else
      assign image_aspect_ratio = 1
    endif
  endunless
-%}

{%- style -%}
  #IndexLook--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;
  }

  {%- if dot_background != 'rgba(0,0,0,0)' and dot_background != '' -%}
    #IndexLook--{{ section.id }} .look__dot { --bg: {{ dot_background }}; }
  {%- endif -%}

  {%- if dot_color != 'rgba(0,0,0,0)' and dot_color != '' -%}
    #IndexLook--{{ section.id }} .look__dot__button { --accent: {{ dot_color }}; }
  {%- endif -%}

  #IndexLook--{{ section.id }} .look__image-container {
    --aspect-ratio: {{ image.aspect_ratio }};
  }
{%- endstyle -%}

<script src="{{ 'look.js' | asset_url }}" defer="defer"></script>

<section
  id="IndexLook--{{ section.id }}"
  class="index-look section-padding {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="look"
>
  <div class="wrapper--full">
    {%- if section.blocks.size > 0 -%}
      {%- for block in section.blocks -%}
        {%- capture style -%}
          --dot-top: {{ block.settings.position_y }}%;
          --dot-left: {{ block.settings.position_x }}%;
        {%- endcapture -%}

        {%- capture look_dots -%}
          {{ look_dots }}

          <button
            type="button"
            class="look__dot__button"
            style="{{ style }}"
            aria-label="{{ 'general.accessibility.scroll_to_product' | t }}"
            data-slider-thumb="{{ forloop.index0 }}"
          >
            <span class="look__dot">
              <span class="look__dot__icon"></span>
            </span>
          </button>
        {%- endcapture -%}

        {%- capture look_slider -%}
          {{ look_slider }}

          {%- assign product = all_products[block.settings.product] -%}

          <div class="look__slide look__slide--{{ block.id }}" data-slide="{{ block.id }}" {{ block.shopify_attributes }}>
            {%- if product != blank -%}
              {%- render 'product-grid-item', product: product, index: forloop.index, show_product_card: block.settings.show_product_card -%}
            {%- else -%}
              {%- assign placeholder = 'product-' | append: forloop.index -%}
              {%- render 'onboarding-product-grid-item', title: 'Product Title', show_product_card: block.settings.show_product_card, placeholder: placeholder, index: forloop.index, animation_delay: forloop.index0 -%}
            {%- endif -%}
          </div>
        {%- endcapture -%}
      {%- endfor -%}
    {%- else -%}
      {%- for i in (1..2) -%}
        {%- capture style -%}
          --dot-top: {{ forloop.index | times: 30 }}%;
          --dot-left: {{ forloop.index | times: 30 }}%;
        {%- endcapture -%}

        {%- capture look_dots -%}
          {{ look_dots }}

          <button type="button" class="look__dot__button" style="{{ style }}" aria-label="{{ 'general.accessibility.scroll_to_product' | t }}" data-slider-thumb="{{ forloop.index0 }}">
            <span class="look__dot">
              <span class="look__dot__icon"></span>
            </span>
          </button>
        {%- endcapture -%}

        {%- capture look_slider -%}
          {{ look_slider }}

          <div class="look__slide look__slide--{{ section.id }}-{{ i }}" data-slide="{{ section.id }}-{{ i }}" data-slide-index="{{ forloop.index0 }}">
            {%- liquid
              assign product_index = forloop.index | plus: 2
              assign placeholder = 'product-' | append: product_index
              capture title
                cycle 'Nomad X Sunglasses', 'Nomad X Hat'
              endcapture
              render 'onboarding-product-grid-item', title: title, placeholder: placeholder, index: forloop.index, animation_delay: forloop.index0
            -%}
          </div>
        {%- endcapture -%}
      {%- endfor -%}
    {%- endif -%}

    <look-component class="look{% if section.settings.layout != '' %} {{ section.settings.layout }}{% endif %}">
      <div class="look__image look__image--fullheight">
        <div class="look__image-container">
          {%- render 'image',
            image: section.settings.image,
            sizes: '(min-width: 750px) 50vw, 100vw',
            aspect_ratio: image_aspect_ratio,
            modifier: 'look__image-bg',
            placeholder: 'collection-1'
          -%}

          {%- if show_dots and look_dots != blank -%}
            <div class="look__dots desktop">
              {{ look_dots }}
            </div>
          {%- endif -%}
        </div>

        <div class="look__actions mobile">
          <button
            type="button"
            class="btn {{ button_style }} {{ section.settings.button_size }} {{ section.settings.button_type }}"
            aria-label="{{ 'general.accessibility.scroll_to_product' | t }}"
            data-slider-thumb="0"
          >
            {%- render 'icon-bag' -%}

            <span>{{ 'general.shop_the_look.button' | t }}</span>

            {%- if section.settings.show_arrow -%}
              {%- render 'icon-arrow-right' -%}
            {%- endif -%}
          </button>
        </div>
      </div>

      <div class="look__aside" data-popup-container>
        <div class="underlay mobile look__underlay" data-popup-close></div>

        <div class="look__content" data-popup-content data-scroll-lock-scrollable>
          {%- if section.settings.title != blank -%}
            <p class="look__title text-center subheading">
              {{ section.settings.title }}
            </p>
          {%- endif -%}

          {%- if section.settings.heading != blank -%}
            {%- liquid
              assign heading_tag = 'h2'

              unless section.settings.heading_tag == 'automatic' or section.settings.heading_tag == blank
                assign heading_tag = section.settings.heading_tag
              endunless
            -%}

            <{{ heading_tag }} class="look__title text-center {{ section.settings.heading_font_size }}">
              {{ section.settings.heading }}
            </{{ heading_tag }}>
          {%- endif -%}

          <button
            type="button"
            data-popup-close
            class="look__close mobile"
            aria-label="{{ 'general.accessibility.close' | t }}"
          >
            {%- render 'icon-cancel' -%}
          </button>

          <div
            class="look__slider{% if section.settings.products_per_row == '2' and section.blocks.size != 1 %} look__slider--grid{% endif %}"
            data-slider-mobile="{{ section.id }}"
            data-arrow-position-middle
            data-scroll-lock-scrollable
            {% if request.design_mode %}
              data-block-scroll
            {% endif %}
          >
            {{ look_slider }}
          </div>
        </div>
      </div>
    </look-component>
  </div>
</section>

{% schema %}
{
  "name": "Shop the look",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "default": "",
      "options": [
        {"value": "", "label": "Image left, products right"},
        {"value": "is-reversed", "label": "Products left, image right"}
      ]
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Subheading",
      "default": "Shop the look"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_font_size",
      "label": "Heading size",
      "default": "heading-large",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "id": "show_dots",
      "type": "checkbox",
      "label": "Show dots",
      "default": true,
      "info": "Desktop only"
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "products_per_row",
      "label": "Products per row",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "header",
      "content": "Button",
      "info": "Applies on mobile only"
    },
    {
      "type": "select",
      "id": "button_type",
      "label": "Color",
      "default": "btn--primary",
      "options": [
        {"value": "btn--black", "label": "Black"},
        {"value": "btn--white", "label": "White"},
        {"value": "btn--primary", "label": "Primary"},
        {"value": "btn--secondary", "label": "Secondary"}
      ]
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Size",
      "default": "",
      "options": [
        {"label": "Small", "value": "btn--small"},
        {"label": "Medium", "value": ""},
        {"label": "Large", "value": "btn--large"}
      ]
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Style",
      "default": "btn--outline",
      "options": [
        {"label": "Solid", "value": "btn--solid"},
        {"label": "Outline", "value": "btn--outline"},
        {"label": "Text", "value": "btn--text"}
      ]
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "Show arrow",
      "default": false
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "id": "enable_aspect_ratio",
      "type": "checkbox",
      "label": "Set custom aspect ratio",
      "default": true
    },
    {
      "type": "range",
      "id": "image_aspect_ratio",
      "label": "Image aspect ratio",
      "min": 0.5,
      "max": 1.5,
      "step": 0.1,
      "unit": ":1",
      "default": 1.0
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "color",
      "id": "dot_color",
      "label": "Dot icon"
    },
    {
      "type": "color",
      "id": "dot_background",
      "label": "Dot background"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "automatic",
      "options": [
        {"value": "automatic", "label": "Automatic"},
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    }
  ],
  "max_blocks": 6,
  "blocks": [
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Product",
          "info": "Product displayed in the photo"
        },
        {
          "type": "checkbox",
          "id": "show_product_card",
          "label": "Show product card",
          "default": false
        },
        {
          "type": "header",
          "content": "Dot position"
        },
        {
          "type": "range",
          "id": "position_x",
          "min": 0,
          "max": 100,
          "step": 1,
          "label": "Horizontal",
          "unit": "%",
          "default": 50
        },
        {
          "type": "range",
          "id": "position_y",
          "min": 0,
          "max": 100,
          "step": 1,
          "label": "Vertical",
          "unit": "%",
          "default": 50
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Shop the look",
      "blocks": [
        {
          "type": "product",
          "settings": {
            "position_x": 14,
            "position_y": 37
          }
        },
        {
          "type": "product",
          "settings": {
            "position_x": 45,
            "position_y": 60
          }
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "custom.pre_footer", "footer"]
  }
}
{% endschema %}
