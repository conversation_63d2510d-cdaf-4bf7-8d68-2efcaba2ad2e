<!-- /sections/section-custom-code.liquid -->

{%- assign color_scheme = 'color-' | append: section.settings.color_scheme -%}

{%- style -%}
  #CustomCode--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;
  }
{%- endstyle -%}

<section
  id="CustomCode--{{ section.id }}"
  class="custom-code section-padding {{ section.settings.width }} {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="custom-code"
>
  {{ section.settings.code }}
</section>

{% schema %}
{
  "name": "Custom code",
  "settings": [
    {
      "type": "liquid",
      "id": "code",
      "label": "Custom code",
      "info": "Add app snippets or other Liquid code to create advanced customizations."
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"},
        {"value": "wrapper--narrow", "label": "Narrow"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Custom code"
    }
  ]
}
{% endschema %}
