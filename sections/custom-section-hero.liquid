{%- liquid
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign show_text_background = section.settings.show_text_background
  assign overlay_opacity = section.settings.overlay_opacity | times: 0.01
  assign show_overlay_text = section.settings.show_overlay_text
  assign animation_anchor = '#Hero--' | append: section.id
  assign desktop_height = section.settings.height
  assign mobile_height = section.settings.mobile_height
  assign image_1 = section.settings.image_1
  assign image_2 = section.settings.image_2
  assign show_placeholder = section.settings.show_placeholder
  assign mobile_image = section.settings.mobile_image
  assign banner_link = section.settings.link
  assign product = all_products[section.settings.product]
  assign has_product = has_product | default: false
  assign color_product_bg = section.settings.color_product_bg
  assign color_product_text = section.settings.color_product_text
  assign bg_color = color_product_bg | default: settings.section_bg

  capture sizes
    if section.settings.width == 'wrapper--full'
      if image_1 and image_2
        echo '(min-width: 750px) 50vw, 100vw'
      else
        echo '100vw'
      endif
    else
      if image_1 and image_2
        echo '(min-width: 990px) calc((100vw - 100px) / 2), (min-width: 750px) calc((100vw - 60px) / 2), calc(100vw - 32px)'
      else
        echo '(min-width: 990px) calc(100vw - 100px), (min-width: 750px) calc(100vw - 60px), calc(100vw - 32px)'
      endif
    endif
  endcapture

  capture sizes_mobile
    if section.settings.width == 'wrapper--full'
      echo '100vw'
    else
      echo 'calc(100vw - 32px)'
    endif
  endcapture

  assign selected_color_scheme = section.settings.color_scheme | strip
  assign scheme_bg_color = settings.color_schemes[selected_color_scheme].settings.section_bg

  assign hero_transparent = true
  if show_text_background and scheme_bg_color.alpha != 0.0 or scheme_bg_color != blank
    assign hero_transparent = false
  endif

  assign show_header_backdrop = false
  if hero_transparent and show_overlay_text
    assign show_header_backdrop = true
  endif

  assign animation_order = 1
-%}

{%- capture block_classes -%}
  {%- render 'block-classes' -%}
{%- endcapture -%}

{%- capture section_classes -%}
  {%- render 'section-classes' -%}
{%- endcapture -%}

{%- style -%}
  #Hero--{{ section.id }} {
    --PT: {{ section.settings.padding_top | default: 0 }}px;
    --PB: {{ section.settings.padding_bottom | default: 0 }}px;

    {% if section.settings.content_width %}
      --hero-content-width: {{ section.settings.content_width }}%;
    {% endif %}
  }

  {% if color_product_text.alpha != 0.0 and color_product_text != blank or color_product_bg.alpha != 0.0 and color_product_bg != blank %}
    #Hero--{{ section.id }} .product-upsell {
      {% if color_product_text.alpha != 0.0 and color_product_text != blank %}
        --text: {{ color_product_text }};
        --text-light: {{ color_product_text | color_mix: bg_color, 70 }};
      {% endif %}

      {% if color_product_bg.alpha != 0.0 and color_product_bg != blank %}
        --bg: {{ color_product_bg }};
      {% endif %}
    }
  {% endif %}
{%- endstyle -%}

<div
  class="custom-index-hero index-hero section-padding {{ color_scheme }} {{ section_classes }}"
  id="Hero--{{ section.id }}"
  data-section-id="{{ section.id }}"
  data-section-type="hero"
  data-overlay-header
>
  {%- if image_1 != blank or image_2 != blank or mobile_image != blank or show_placeholder or request.design_mode -%}
    <div class="hero__wrapper frame {{ section.settings.width }}">
      {%- if banner_link != blank -%}
        <a class="hero__images frame__item {{ block_classes }}" href="{{ banner_link }}">
      {%- else -%}
        <div class="hero__images frame__item {{ block_classes }}">
      {%- endif -%}

      {% if section.settings.video != blank and section.settings.enable_video == true %}
        
        <script src="{{ 'video-background.js' | asset_url }}" defer="defer"></script>

        {%- assign poster = image_1 -%}

        <div
          class="video-background {{ desktop_height }} {{ mobile_height }}"
          style="--aspect-ratio: {{ section.settings.video.aspect_ratio | default: 1 }};"
        >
          {%- if poster -%}
            <div class="video__poster">
              {%- render 'image-hero' image: poster, sizes: sizes, desktop_height: desktop_height, mobile_height: mobile_height -%}
            </div>
          {%- endif -%}
          <video-background
            class="video__player is-loading"
            data-video-player
            data-video-id="{{ section.id }}-video-background"
          >
            <template data-video-template>
              {{ section.settings.video | video_tag: autoplay: true, image_size: '1085x', loop: true, muted: true, controls: false }}
            </template>
          </video-background>
        </div>

      {% else %}

        {%- if image_1 != blank -%}
          <div class="hero__split-image">

            {%- render 'image-hero',
              image_desktop: image_1,
              image_mobile: mobile_image,
              sizes: sizes,
              desktop_height: desktop_height,
              mobile_height: mobile_height
            -%}

          </div>
        {%- endif -%}

        {%- if image_2 != blank or image_1 == blank and show_placeholder == true -%}
          <div class="hero__split-image{% if image_1 %} desktop{% endif %}">
            {%- render 'image-hero',
              image_desktop: image_2,
              image_mobile: mobile_image,
              sizes: sizes,
              desktop_height: desktop_height,
              mobile_height: mobile_height,
              show_placeholder: show_placeholder
            -%}
          </div>
        {%- endif -%}

      {% endif %}

      {%- if image_1 == blank and image_2 == blank and show_placeholder == false -%}
        <div class="image__hero__missing-metafield-image">{{ 'products.general.missing_metafield_image' | t }}</div>
      {%- endif -%}

      {%- unless show_overlay_text or overlay_opacity == 0.0 -%}
        <div class="image-overlay" style="--overlay-opacity: {{ overlay_opacity }};"></div>
      {%- endunless -%}

      {%- if banner_link != blank -%}
        </a>
      {%- else -%}
        </div>
      {%- endif -%}

      {%- if section.blocks.size > 0 and image_1 != blank or image_2 != blank or show_placeholder == true -%}
        <div
          class="hero__content__wrapper frame__item {{ section.settings.flex_align_desktop | default: 'align--bottom-left-desktop' }} {{ section.settings.flex_align_mobile }}{% if show_header_backdrop %} backdrop--linear{% endif %}"
          {% if show_header_backdrop %}
            style="--header-overlay-color: var(--overlay-bg); --header-overlay-opacity: {{ overlay_opacity }};"
          {% endif %}
        >
          <div
            class="hero__content{% if hero_transparent %} hero__content--transparent{% endif %}{% if show_overlay_text %} backdrop--radial{% endif %}"
            {% if overlay_opacity != 0.0 %}
              style="--overlay-opacity: {{ overlay_opacity }};"
            {% endif %}
          >
            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when 'heading' -%}
                  {%- if block.settings.title != '' -%}
                    {%- liquid
                      assign animation_order = animation_order | plus: 1
                      assign heading_tag = 'h2'

                      unless block.settings.heading_tag == 'automatic' or block.settings.heading_tag == blank
                        assign heading_tag = block.settings.heading_tag
                      endunless
                    -%}

                    <{{ heading_tag }}
                      class="hero__title {{ block.settings.heading_font_size }}"
                      data-aos="hero"
                      data-aos-anchor="{{ animation_anchor }}"
                      data-aos-order="{{ animation_order }}"
                      {{ block.shopify_attributes }}
                    >
                      {{ block.settings.title | escape -}}
                    </{{ heading_tag }}>
                  {%- endif -%}

                {%- when 'text' -%}
                  {%- if block.settings.text != blank -%}
                    {%- assign animation_order = animation_order | plus: 1 -%}
                    <div
                      class="hero__description {{ block.settings.text_font_size }}"
                      data-aos="hero"
                      data-aos-anchor="{{ animation_anchor }}"
                      data-aos-order="{{ animation_order }}"
                      {{ block.shopify_attributes }}
                    >
                      {{ block.settings.text }}
                    </div>
                  {%- endif -%}

                {%- when 'button' -%}
                  {%- liquid
                    assign animation_order = animation_order | plus: 1

                    assign button_style = block.settings.button_style
                    if button_style == 'btn--text' and block.settings.show_arrow
                      assign button_style = button_style | append: ' btn--text-no-underline'
                    endif
                  -%}

                  {%- if block.settings.link_text != '' -%}
                    <div
                      class="hero__button"
                      data-aos="hero"
                      data-aos-anchor="{{ animation_anchor }}"
                      data-aos-order="{{ animation_order }}"
                      {{ block.shopify_attributes }}
                    >
                      <a
                        class="btn {{ button_style }} {{ block.settings.button_size }} {{ block.settings.button_type }}{% if block.settings.full_width %} btn--full{% endif %}"
                        href="{{ block.settings.link | default: '#!' }}"
                      >
                        <span>{{ block.settings.link_text | escape }}</span>

                        {%- if block.settings.show_arrow -%}
                          {% render 'icon-arrow-right' %}
                        {%- endif -%}
                      </a>
                    </div>
                  {%- endif -%}

                {%- when 'group-buttons' -%}
                  {%- liquid
                    assign animation_order = animation_order | plus: 1

                    assign button_style = block.settings.button_style
                    if button_style == 'btn--text' and block.settings.show_arrow
                      assign button_style = button_style | append: ' btn--text-no-underline'
                    endif

                    if block.settings.button_secondary_text
                      assign button_secondary_style = block.settings.button_secondary_style
                      if button_secondary_style == 'btn--text' and block.settings.show_secondary_arrow
                        assign button_secondary_style = button_secondary_style | append: ' btn--text-no-underline'
                      endif
                    endif
                  -%}

                  <div class="hero__button-group">
                    {%- if block.settings.button_text != blank -%}
                      <div class="hero__button"
                        data-aos="hero"
                        data-aos-anchor="{{ animation_anchor }}"
                        data-aos-order="{{ animation_order }}"
                        {{ block.shopify_attributes }}
                      >
                        <a
                          class="btn {{ button_style }} {{ block.settings.button_size }} {{ block.settings.button_type }}"
                          href="{{ block.settings.button_url | default: '#!' }}"
                        >
                          <span>{{ block.settings.button_text | escape }}</span>

                          {%- if block.settings.show_arrow -%}
                            {% render 'icon-arrow-right' %}
                          {%- endif -%}
                        </a>
                      </div>
                    {%- endif -%}

                    {%- if block.settings.button_secondary_text != blank -%}
                      <div class="hero__button"
                        data-aos="hero"
                        data-aos-anchor="{{ animation_anchor }}"
                        data-aos-order="{{ animation_order }}"
                        {{ block.shopify_attributes }}
                      >
                        <a
                          class="btn {{ button_secondary_style }} {{ block.settings.button_secondary_size }} {{ block.settings.button_secondary_type }}"
                          href="{{ block.settings.button_secondary_url | default: '#!' }}"
                        >
                          <span>{{ block.settings.button_secondary_text | escape }}</span>

                          {%- if block.settings.show_secondary_arrow -%}
                            {% render 'icon-arrow-right' %}
                          {%- endif -%}
                        </a>
                      </div>
                    {%- endif -%}
                  </div>

                {%- when 'code' -%}
                  {%- assign animation_order = animation_order | plus: 1 -%}

                  <div
                    class="hero__custom-code"
                    data-aos="hero"
                    data-aos-anchor="{{ animation_anchor }}"
                    data-aos-order="{{ animation_order }}"
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.code }}
                  </div>
              {%- endcase -%}
            {%- endfor -%}
          </div>
        </div>
      {%- endif -%}

      {%- if has_product and image_1 != blank or image_2 != blank or show_placeholder == true -%}
        {%- assign animation_order = animation_order | plus: 1 -%}

        <div class="hero__aside__wrapper frame__item {{ section.settings.flex_align_desktop | default: 'align--bottom-left-desktop' }}">
          <div
            class="hero__aside"
            data-aos="hero"
            data-aos-anchor="{{ animation_anchor }}"
            data-aos-order="{{ animation_order }}"
          >
            {%- liquid
              assign upsell_quick_add_hide = false
              assign upsell_description = ''
              assign onboarding = false
              if product == blank
                assign onboarding = true
              endif

              unless section.settings.enable_quick_add
                assign upsell_quick_add_hide = true
              endunless

              if section.settings.description != blank
                capture upsell_description
                  echo '<p>' | append: section.settings.description | append: '</p>'
                endcapture
              endif
            -%}

            {%- render 'upsell-product',
              upsell_product: product,
              upsell_modifier: 'product-upsell--block',
              upsell_quick_add_hide: upsell_quick_add_hide,
              upsell_description: upsell_description,
              upsell_description_font_size: section.settings.text_font_size,
              onboarding: onboarding
            -%}
          </div>
        </div>
      {%- endif -%}
    </div>
  {%- endif -%}
</div>


{% schema %}
{
  "name": "💄 Image banner",
  "class": "section-overlay-header",
  "settings": [
    {
      "type": "header",
      "content": "💄 Appearance"
    },
    {
      "type": "select",
      "id": "corner_radius",
      "label": "Corner radius",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "sm",
          "label": "Small"
        },
        {
          "value": "",
          "label": "Normal"
        },
        {
          "value": "lg",
          "label": "Large"
        }
      ],
      "default": "none"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "header",
      "content": "Video"
    },
    {
      "type": "checkbox",
      "id": "enable_video",
      "label": "Enable video",
      "default": true
    },
    {
      "type": "video",
      "id": "video",
      "label": "Video"
    },
    {
      "type": "header",
      "content": "Image",
      "info": "If video is added, this is used as the loading state of the video."
    },
    {
      "type": "image_picker",
      "id": "image_1",
      "label": "Primary image",
      "info": "3200 x 1200px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "Secondary image",
      "info": "Displays both images at a 50% width. 3200 x 1200px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "image_picker",
      "id": "image_fallback",
      "label": "Fallback image",
      "info": "Used if the video or primary images aren't present. 2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "url",
      "id": "link",
      "label": "Image link"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"}
      ]
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "height",
      "label": "Height",
      "default": "screen-height-three-quarters",
      "options": [
        {"value": "image-height", "label": "Image height"},
        {"value": "screen-height-full", "label": "Full screen height"},
        {"value": "screen-height-three-quarters", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds", "label": "2/3 of screen"},
        {"value": "screen-height-one-half", "label": "1/2 of screen"},
        {"value": "screen-height-one-third", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero", "label": "750px"},
        {"value": "sixty-fifty-height-hero", "label": "650px"},
        {"value": "five-fifty-height-hero ", "label": "550px"},
        {"value": "four-fifty-height-hero", "label": "450px"}
      ]
    },
    {
      "type": "select",
      "id": "flex_align_desktop",
      "label": "Desktop alignment",
      "default": "align--middle-center-desktop",
      "options": [
        {"value": "align--top-left-desktop", "label": "Top left"},
        {"value": "align--top-center-desktop", "label": "Top center"},
        {"value": "align--top-right-desktop", "label": "Top right"},
        {"value": "align--middle-left-desktop", "label": "Middle left"},
        {"value": "align--middle-center-desktop", "label": "Absolute center"},
        {"value": "align--middle-right-desktop", "label": "Middle right"},
        {"value": "align--bottom-left-desktop", "label": "Bottom left"},
        {"value": "align--bottom-center-desktop", "label": "Bottom center"},
        {"value": "align--bottom-right-desktop", "label": "Bottom right"}
      ]
    },
    {
      "type": "range",
      "id": "content_width",
      "min": 20,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "Content width",
      "default": 50
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "Image",
      "info": "1200 x 1600px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Height",
      "default": "screen-height-three-quarters--mobile",
      "options": [
        {"value": "image-height--mobile", "label": "Image height"},
        {"value": "screen-height-full--mobile", "label": "Full screen height"},
        {"value": "screen-height-three-quarters--mobile", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds--mobile", "label": "2/3 of screen"},
        {"value": "screen-height-one-half--mobile", "label": "1/2 of screen"},
        {"value": "screen-height-one-third--mobile", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero--mobile", "label": "750px"},
        {"value": "sixty-fifty-height-hero--mobile", "label": "650px"},
        {"value": "five-fifty-height-hero--mobile", "label": "550px"},
        {"value": "four-fifty-height-hero--mobile", "label": "450px"}
      ]
    },
    {
      "type": "select",
      "id": "flex_align_mobile",
      "label": "Mobile alignment",
      "default": "align--middle-center-mobile",
      "options": [
        {"value": "align--top-left-mobile", "label": "Top left"},
        {"value": "align--top-center-mobile", "label": "Top center"},
        {"value": "align--top-right-mobile", "label": "Top right"},
        {"value": "align--middle-left-mobile", "label": "Middle left"},
        {"value": "align--middle-center-mobile", "label": "Absolute center"},
        {"value": "align--middle-right-mobile", "label": "Middle right"},
        {"value": "align--bottom-left-mobile", "label": "Bottom left"},
        {"value": "align--bottom-center-mobile", "label": "Bottom center"},
        {"value": "align--bottom-right-mobile", "label": "Bottom right"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "checkbox",
      "id": "show_text_background",
      "label": "Show text background",
      "default": false
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "Overlay opacity",
      "info": "Increase contrast for legible text.",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "show_overlay_text",
      "label": "Overlay behind text only",
      "default": false,
      "info": "Overlay opacity must be greater than 0."
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 200,
      "step": 2,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 200,
      "step": 2,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "checkbox",
      "id": "show_placeholder",
      "label": "Show placeholder image",
      "info": "Disable if using a metafield",
      "default": true
    }
  ],
  "max_blocks": 5,
  "blocks": [
    {
      "type": "heading",
      "name": "Heading",
      "limit": 2,
      "settings": [
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Image banner"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-x-large",
          "options": [
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
            {"value": "heading-mini", "label": "Mini", "group": "Headings"},
            {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
            {"value": "heading-small", "label": "Small", "group": "Headings"},
            {"value": "heading-medium", "label": "Medium", "group": "Headings"},
            {"value": "heading-large", "label": "Large", "group": "Headings"},
            {"value": "heading-x-large", "label": "Extra large", "group": "Headings"}
          ]
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "limit": 2,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Tell your brand's story through images.<\/p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-large",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        }
      ]
    },
    {
      "type": "button",
      "name": "Single button",
      "limit": 2,
      "settings": [
        {
          "type": "text",
          "id": "link_text",
          "label": "Text",
          "default": "View products"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "full_width",
          "label": "Full-width",
          "default": false
        }
      ]
    },
    {
      "type": "group-buttons",
      "name": "Side by side buttons",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Primary button"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Text",
          "default": "View products"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "header",
          "content": "Secondary button"
        },
        {
          "type": "text",
          "id": "button_secondary_text",
          "label": "Text",
          "default": "Shop sale"
        },
        {
          "type": "url",
          "id": "button_secondary_url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_secondary_type",
          "label": "Color",
          "default": "btn--secondary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_secondary_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_secondary_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_secondary_arrow",
          "label": "Show arrow",
          "default": false
        }
      ]
    },
    {
      "type": "code",
      "name": "Custom code",
      "settings": [
        {
          "type": "liquid",
          "id": "code",
          "label": "Custom code",
          "default": "<p>Once you write some custom code, it will render right here.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "💄 Image banner",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "footer"]
  }
}
{% endschema %}
