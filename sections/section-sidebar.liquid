<!-- /sections/section-rich-text.liquid -->
{%- liquid
  assign aside_html = ''
  assign content_html = ''
  assign first_element_flag = true
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign animation_anchor = '#Sidebar--' | append: section.id
  assign animation_order = 1
-%}

{%- style -%}
  #Sidebar--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;

    --link: var(--text);
  }
{%- endstyle -%}

{%- if section.blocks.size > 0 -%}
  {%- for block in section.blocks -%}
    {%- liquid
      assign title = block.settings.title
      assign text = block.settings.text
      assign content = block.settings.content
      assign unique = 'Widget--' | append: section.id | append: '-' | append: block.id
    -%}

    {%- capture aside_html -%}
      {{- aside_html -}}

      {%- if block.type == 'header' and title != blank -%}
        <li>
          <scroll-spy data-scroll-spy-container="#Sidebar--{{ section.id }}">
            <scroll-to-element>
              <button type="button" class="widget__link{% if first_element_flag %} is-selected{% endif %}"
                data-scroll-to="#{{ unique }}"
                data-scroll-spy="#{{ unique }}"
                data-scroll-spy-desktop
                data-scroll-trigger-point="top"
                {{ block.shopify_attributes }}>
                {{- title -}}
              </button>
            </scroll-to-element>
          </scroll-spy>
        </li>
        {%- assign first_element_flag = false -%}
      {%- endif -%}
    {%- endcapture -%}

    {%- capture content_html -%}
      {{ content_html }}

      {%- if block.type == 'header' and title != blank -%}
        <h6 class="section-sidebar__title {{ section.settings.heading_font_size }}" id="{{ unique }}" {{ block.shopify_attributes }}>{{ title }}</h6>
      {%- endif -%}

      {%- if block.type == 'text' and text != blank -%}
        <div class="section-sidebar__text rte" {{ block.shopify_attributes }}>{{ text }}</div>
      {%- endif -%}

      {%- if block.type == 'accordion' and title != blank and content != blank -%}
        <details class="accordion" data-collapsible {{ block.shopify_attributes }}>
          <summary class="accordion__title subheading" data-collapsible-trigger>
            <span>{{ title }}</span>

            {%- render 'icon-plus' -%}
            {%- render 'icon-minus' -%}
          </summary>

          <div class="accordion__body rte" data-collapsible-body>
            <div class="accordion__content" data-collapsible-content>
              {{ content }}
            </div>
          </div>
        </details>
      {%- endif -%}
    {%- endcapture -%}
  {%- endfor -%}
{%- endif -%}

{%- if aside_html != '' -%}
  <script src="{{ 'scroll-to-element.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'scroll-spy.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

<section
  id="Sidebar--{{ section.id }}"
  class="section-sidebar section-padding {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="sidebar"
>
  {%- if section.blocks.size > 0 -%}
    <div class="{{ section.settings.width }}">
      <div class="section-sidebar__body">
        {%- if aside_html != '' -%}
          <div
            class="section-sidebar__aside"
            data-aos="hero"
            data-aos-anchor="{{ animation_anchor }}"
            data-aos-order="{{ animation_order }}"
          >
            <div class="section-sidebar__widget">
              <ul class="widget__links widget__links--secondary">
                {{ aside_html }}
              </ul>
            </div>
          </div>
          {%- assign animation_order = animation_order | plus: 1 -%}
        {%- endif -%}

        <div
          class="section-sidebar__content"
          data-aos="hero"
          data-aos-anchor="{{ animation_anchor }}"
          data-aos-order="{{ animation_order }}"
        >
          <collapsible-elements single="true">
            {{ content_html }}
          </collapsible-elements>
        </div>
      </div>
    </div>
  {%- else -%}
    {%- render 'no-blocks' -%}
  {%- endif -%}
</section>

{% schema %}
{
  "name": "Sidebar",
  "settings": [
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "select",
      "id": "heading_font_size",
      "label": "Heading size",
      "default": "heading-small",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full-padded",
      "options": [
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 100
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 100
    }
  ],
  "blocks": [
    {
      "name": "Header",
      "type": "header",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading"
        }
      ]
    },
    {
      "name": "Accordion",
      "type": "accordion",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Question",
          "default": "Frequently asked question"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Answer",
          "default": "<p>Share details about your shipping policies, item returns, or customer service.<\/p>"
        }
      ]
    },
    {
      "name": "Text",
      "type": "text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Share details about your shipping policies, item returns, or customer service.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Sidebar",
      "blocks": [
        {
          "type": "header",
          "settings": {
            "title": "Shipping & Returns"
          }
        },
        {
          "type": "accordion",
          "settings": {
            "title": "How much is shipping?",
            "content": "<p>We offer free shipping on all US orders, and a flat rate of $10 for all international orders.<\/p>"
          }
        },
        {
          "type": "accordion",
          "settings": {
            "title": "What is your return policy?",
            "content": "<p>We accept returns within 30 days of the purchase date.<\/p>"
          }
        },
        {
          "type": "header",
          "settings": {
            "title": "Discount"
          }
        },
        {
          "type": "accordion",
          "settings": {
            "title": "Can I use more than one discount code?",
            "content": "<p>Only one discount code can be used per order and cannot be combined with other promotions.<\/p>"
          }
        },
        {
          "type": "header",
          "settings": {
            "title": "Contact"
          }
        },
        {
          "type": "text",
          "settings": {
            "text": "<p>Still have questions? We're happy to help! Please email <NAME_EMAIL> and we will get back to you within 24 hours.<\/p>"
          }
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "custom.pre_footer", "footer"]
  }
}
{% endschema %}
