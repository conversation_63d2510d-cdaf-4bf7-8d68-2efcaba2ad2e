<!-- /sections/main-page.liquid -->
{%- liquid
  assign animation_anchor = '#page--' | append: section.id
  assign color_scheme = 'color-' | append: section.settings.color_scheme
-%}

{%- style -%}
  .index-page {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;
  }
{%- endstyle -%}

{%- if section.settings.show_content or section.settings.subheading != blank or section.settings.show_title -%}
  <section class="index-page section-padding {{ color_scheme }}"
    data-section-id="{{ section.id }}"
    data-section-type="page">
    <div class="hero__content__wrapper {{ section.settings.align_text }} {{ section.settings.width }}">
      <div class="hero__content hero__content--compact">
        {%- if section.settings.subheading != blank -%}
          <p class="hero__subheading"
            data-aos="hero"
            data-aos-anchor="{{ animation_anchor }}"
            data-aos-order="1">
            {{ section.settings.subheading }}
          </p>
        {%- endif -%}
        {%- if section.settings.show_title -%}
          {%- liquid
            assign heading_tag = 'h2'

            unless section.settings.heading_tag == 'automatic' or section.settings.heading_tag == blank
              assign heading_tag = section.settings.heading_tag
            endunless
          -%}

          <{{ heading_tag }} class="hero__title {{ section.settings.heading_font_size }}"
            data-aos="hero"
            data-aos-anchor="{{ animation_anchor }}"
            data-aos-order="2">
            {{ page.title }}
          </{{ heading_tag }}>
        {%- endif -%}
        {%- if section.settings.show_content -%}
          <div class="hero__rte {{ section.settings.text_font_size }}"
            data-aos="hero"
            data-aos-anchor="{{ animation_anchor }}"
            data-aos-order="3">
            <div class="rte">{{ page.content }}</div>
          </div>
        {%- endif -%}
      </div>
    </div>
  </section>
{%- endif -%}

{% schema %}
{
  "name": "Page",
  "settings": [
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading"
    },
    {
      "type": "checkbox",
      "id": "show_title",
      "label": "Show title",
      "default": true
    },
    {
      "type": "select",
      "id": "heading_font_size",
      "label": "Heading size",
      "default": "heading-large",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "checkbox",
      "id": "show_content",
      "label": "Show content",
      "default": true
    },
    {
      "type": "select",
      "id": "text_font_size",
      "label": "Text size",
      "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
      "default": "body-medium",
      "options": [
        {"value": "body-x-small", "label": "Extra small"},
        {"value": "body-small", "label": "Small"},
        {"value": "body-medium", "label": "Medium"},
        {"value": "body-large", "label": "Large"},
        {"value": "body-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "select",
      "id": "align_text",
      "label": "Text alignment",
      "default": "text-left",
      "options": [
        { "value": "text-left","label": "Left" },
        { "value": "text-center","label": "Centered" }
      ]
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper",
      "options": [
        { "value": "wrapper--full-padded", "label": "Full width padded" },
        { "value": "wrapper", "label": "Normal" },
        { "value": "wrapper--narrow", "label": "Narrow" }
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 200,
      "step": 2,
      "unit": "px",
      "label": "Top",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 200,
      "step": 2,
      "unit": "px",
      "label": "Bottom",
      "default": 50
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Title SEO tag",
      "default": "automatic",
      "options": [
        {"value": "automatic", "label": "Automatic"},
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    }
  ]
}
{% endschema %}
