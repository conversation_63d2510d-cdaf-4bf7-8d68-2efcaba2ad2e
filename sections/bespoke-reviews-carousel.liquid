{%- liquid
  assign section_name = "FeaturedReviews"
  assign section_id = section_name | append: '--' | append: section.id
  assign unique = section_id | append: '-' | append: block.id
  
  assign animation_anchor = '#' | append: section_name | append: '--' | append: section.id
  
  assign color_scheme = 'color-' | append: section.settings.color_scheme
-%}

{%- capture style -%}
  --PT: {{ section.settings.padding_top }}px;
  --PB: {{ section.settings.padding_bottom }}px;
{%- endcapture -%}

<section
  id="{{ section_id }}"
  data-section-id="{{ section.id }}"
  data-section-type="featured-products-carousel"
  class="
    {% unless section.settings.width == "wrapper--full" %}section-padding{% endunless %}
    bespoke-reviews-carousel
    {% if section.settings.variant_selection_filter %}bespoke-reviews-carousel--variant-filter{% endif %}
    {{ color_scheme }}
  "
  style="{{ style }}"
>

  {%- capture section_header_content -%}

    {%- if section.settings.title != blank or section.settings.subheading != blank -%}
      <div class="section-header__text">
        
        {%- if section.settings.eyebrow != blank -%}
          <p class="section-header__eyebrow {{ section.settings.eyebrow_font_size }}">
            {{ section.settings.eyebrow }}
          </p>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          {%- liquid
            assign heading_tag = section.settings.heading_tag
          -%}
          <{{ heading_tag }} class="section-header__title {{ section.settings.title_font_size }}">
            {{ section.settings.title }}
          </{{ heading_tag }}>
        {%- endif -%}

      </div>
    {%- endif -%}

    {%- if section.settings.shop_all_text != blank -%}
      <div class="section-header__actions">
        <a href="{{ section.settings.shop_all_link | default: "#" }}"
           class="btn {{ section.settings.shop_all_button_style }} {{ section.settings.shop_all_button_size }} {{ section.settings.shop_all_button_type }}"
        >
          <span>{{ section.settings.shop_all_text }}</span>
        </a>
      </div>
    {%- endif -%}

  {%- endcapture -%}

  {%- if section_header_content != blank -%}
    {%- if section.settings.width != "" -%}
      <div class="{{ section.settings.width }}">
    {%- endif -%}
    <div class="section-header section-header--{{ section.settings.header_layout }}">
      {{ section_header_content }}
    </div>
    {%- if section.settings.width != "" -%}
      </div>
    {%- endif -%}
  {%- endif -%}

  <!-- 
  {%- for block in section.blocks -%}
   {{ block.settings.title }}
  {%- endfor -%}
  section-->
  
  {%- capture product_items -%}
    {%- for block in section.blocks -%}
      <!--
      block.settings.color_scheme - {{ block.settings.color_scheme }}
      block.settings.enable_video - {{ block.settings.enable_video }}
      block.settings.video - {{ block.settings.video }}
      block.settings.image - {{ block.settings.image }}
      block.settings.rating - {{ block.settings.rating }}
      block.settings.eyebrow - {{ block.settings.eyebrow }}
      block.settings.title - {{ block.settings.title }}
      block.settings.review_text - {{ block.settings.review_text }}
      block.settings.author - {{ block.settings.author }}
      block.settings.model_name - {{ block.settings.model_name }}
      block.settings.option_name - {{ block.settings.option_name }}
      block.settings.swatch_color - {{ block.settings.swatch_color }}
      block.settings.link - {{ block.settings.link }}
      -->
      {%- capture classes -%}grid-item{%- endcapture -%}
      {% render 'featured-review', block: block, unique: unique, classes: classes %}
    {%- endfor -%}
  {%- endcapture -%}

  {% if product_items != blank %}

    {%- capture product_grid_classes -%}
      grid grid--slider grid--mobile-slider
    {%- endcapture -%}
    
    <div class="grid-container">
      <div class="grid__items-holder">
        <div class="grid-outer collection-list-outer">
          <grid-slider align-arrows>
            <div class="{{ product_grid_classes }}" data-grid-slider>
              {{ product_items }}
            </div>
          </grid-slider>
        </div>
      </div>
    </div>

  {% endif %}

</section>

{% schema %}
{
  "name": "💄 Featured Reviews",
  "settings": [
    {
      "type": "product",
      "id": "product",
      "label": "Product",
      "info": "Select product to pull reviews from. Defaults to the current product."
    },
    {
      "type": "header",
      "content": "Header"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "select",
      "id": "title_font_size",
      "label": "Title font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": ""
    },
    {
      "type": "text",
      "id": "eyebrow",
      "label": "Eyebrow text"
    },
    {
      "type": "select",
      "id": "eyebrow_font_size",
      "label": "Eyebrow font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": "text-badge-lg"
    },
    {
      "type": "url",
      "id": "shop_all_link",
      "label": "Shop all link"
    },
    {
      "type": "text",
      "id": "shop_all_text",
      "label": "Shop all text"
    },
    {
      "type": "select",
      "id": "shop_all_button_type",
      "label": "Color",
      "default": "btn--primary",
      "options": [
        {"value": "btn--black", "label": "Black"},
        {"value": "btn--white", "label": "White"},
        {"value": "btn--primary", "label": "Primary"},
        {"value": "btn--secondary", "label": "Secondary"}
      ]
    },
    {
      "type": "select",
      "id": "shop_all_button_size",
      "label": "Size",
      "default": "",
      "options": [
        {"label": "Small", "value": "btn--small"},
        {"label": "Medium", "value": ""},
        {"label": "Large", "value": "btn--large"}
      ]
    },
    {
      "type": "select",
      "id": "shop_all_button_style",
      "label": "Style",
      "default": "btn--solid",
      "options": [
        {"label": "Solid", "value": "btn--solid"},
        {"label": "Outline", "value": "btn--outline"},
        {"label": "Text", "value": "btn--text"}
      ]
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "h2",
      "options": [
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    },
    {
      "type": "select",
      "id": "header_layout",
      "label": "Layout",
      "default": "horizontal",
      "options": [
        {"value": "horizontal", "label": "Horizontal"},
        {"value": "vertical", "label": "Vertical"}
      ]
    },
    {
      "type": "header",
      "content": "Reviews"
    },
    {
      "type": "select",
      "id": "select_color_scheme",
      "label": "Review color scheme",
      "options": [
        {
          "value": "section",
          "label": "Section (below)"
        },
        {
          "value": "review",
          "label": "Review (set on review)"
        },
      ],
      "info": "Use the color scheme set in the section, or override it with a custom color scheme set on each review.",
      "default": "section"
    },
    {
      "type": "color_scheme",
      "id": "review_color_scheme",
      "default": "scheme_3",
      "label": "Review color scheme"
    },
    {
      "type": "select",
      "id": "review_eyebrow_font_size",
      "label": "Eyebrow font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": "subheading-eyebrow"
    },
    {
      "type": "select",
      "id": "review_title_font_size",
      "label": "Title font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": "subheading"
    },
    {
      "type": "select",
      "id": "review_text_font_size",
      "label": "Text font size",
      "options": [
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"}
      ],
      "default": "body-medium"
    },
    {
      "type": "select",
      "id": "author_text_font_size",
      "label": "Author font size",
      "options": [
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"}
      ],
      "default": "body-medium"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full-padded",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"}
      ]
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 50
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "checkbox",
      "id": "variant_selection_filter",
      "label": "Filter products by variant options",
      "default": false,
      "info": "If on a product page, and the product's option is changed, filter the reviews in this carousel to show only the reiews for that option."
    }
  ],
  "blocks": [
    {
      "type": "featured-review",
      "name": "Featured Review",
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme_3",
          "label": "Color scheme"
        },
        {
          "type": "header",
          "content": "Media"
        },
        {
          "type": "checkbox",
          "id": "enable_video",
          "label": "Enable video",
          "default": true
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "header",
          "content": "Image",
          "info": "If video is added, this is used as the loading state of the video."
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "header",
          "content": "Content",
          "info": "Overrides product content if product selected."
        },
        {
          "type": "select",
          "id": "rating",
          "label": "Star Rating",
          "options": [
            {
              "value": "",
              "label": "Hide"
            },
            {
              "value": "0",
              "label": "0"
            },
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            },
            {
              "value": "3",
              "label": "3"
            },
            {
              "value": "4",
              "label": "4"
            },
            {
              "value": "5",
              "label": "5"
            }
          ],
          "default": "5"
        },
        {
          "type": "text",
          "id": "eyebrow",
          "label": "Eyebrow text"
        },
        { 
          "id": "title",
          "label": "Title",
          "type": "text"
        },
        {
          "type": "richtext",
          "id": "review_text",
          "label": "Review Text"
        },
        { 
          "id": "author",
          "label": "Author",
          "type": "text"
        },
        {
          "type": "paragraph",
          "content": "Image Caption"
        },
        { 
          "id": "model_name",
          "label": "Model name",
          "type": "text"
        },
        { 
          "id": "option_name",
          "label": "Option name",
          "type": "text"
        },
        {
          "type": "color",
          "id": "swatch_color",
          "label": "Swatch color"
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link",
          "info": "Optional link on the review."
        },
        {
          "type": "product",
          "id": "product",
          "label": "Product",
          "info": "The product associated with the review."
        },
      ]
    },
  ],
  "presets": [
    {
      "name": "💄 Featured Reviews"
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "custom.pre_footer", "footer"]
  }
}
{% endschema %}
