{%- liquid
  assign default_title = 'home_page.sections.news_title' | t
  assign default_content = 'home_page.onboarding.no_content' | t
  assign color_scheme = 'color-' | append: section.settings.color_scheme
-%}

{%- style -%}
  #FeaturedPosts--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;
  }
{%- endstyle -%}

{%- if section.blocks.size > 0 -%}
  <div
    id="FeaturedPosts--{{ section.id }}"
    class="featured-posts section-padding {{ color_scheme }}"
    data-section-id="{{ section.id }}"
    data-section-type="blog-section"
  >
    <div class="{{ section.settings.width }}">
      <div class="blog-editorial">
        <slider-component
          class="editorial__slider"
          id="editorial__slider--{{ section.id }}"
          data-slider="{{ section.id }}"
          data-slider-fullwidth
          data-options='{"watchCSS": true, "pageDots": false, "prevNextButtons": true}'
        >
          {%- for block in section.blocks -%}
            {%- liquid
              assign article = articles[block.settings.featured_post]
              assign subheading = block.settings.subheading

              if article != blank and article.image
                assign image = article.image
              else
                assign image = 'blank.svg' | asset_url
              endif

              if block.settings.image
                assign image = block.settings.image
              endif

              capture sizes
                echo '(min-width: 750px) 50vw, 100vw'
              endcapture
            -%}

            <div
              class="editorial__slide"
              id="editorial__slide--{{ block.id }}"
              data-slide="{{ block.id }}"
              {{ block.shopify_attributes }}
            >
              <div class="editorial">
                <div class="editorial__image">
                  {%- render 'image-hero',
                    image: image,
                    sizes: sizes,
                    modifier: 'editorial__image-bg',
                    desktop_height: section.settings.height,
                    mobile_height: section.settings.mobile_height
                  -%}
                </div>

                <div class="editorial__aside">
                  <div
                    class="editorial__aside-inner"
                    {% if subheading != blank %}
                      data-subheading="{{ subheading }}"
                    {% endif %}
                  >
                    {%- if subheading != blank -%}
                      <p class="editorial__subheading subheading">{{ subheading }}</p>
                    {%- endif -%}

                    <div class="editorial__content">
                      <h2 class="editorial__title {{ section.settings.heading_font_size }}">
                        <a href="{{ article.url }}" title="{{ article.title | escape }}">
                          {{ article.title | default: default_title }}
                        </a>
                      </h2>

                      {%- if section.settings.show_date or section.settings.show_author -%}
                        <h3 class="editorial__meta">
                          {%- if section.settings.show_date -%}
                            {%- assign published_at = article.published_at
                              | default: 'now'
                              | date: format: 'full_date'
                            -%}
                            <time datetime="{{ published_at }}">{{ published_at }}</time>
                          {% endif %}

                          {%- if section.settings.show_author -%}
                            {%- assign article_author = article.author | default: 'Jane Doe' -%}
                            <span class="editorial__meta__author">
                              {{- 'blogs.article.by_author' | t: author: article_author }}
                            </span>
                          {%- endif -%}
                        </h3>
                      {%- endif -%}

                      {%- unless article != blank and article.excerpt_or_content == blank -%}
                        <div class="editorial__excerpt">
                          <p>
                            {{ article.excerpt_or_content | default: default_content | strip_html | truncatewords: 30 }}
                          </p>
                        </div>
                      {%- endunless -%}

                      {%- if section.settings.show_button -%}
                        {%- liquid
                          assign button_text = section.settings.button_text
                          assign button_style = section.settings.button_style
                          if button_style == 'btn--text' and section.settings.show_arrow
                            assign button_style = button_style | append: ' btn--text-no-underline'
                          endif
                        -%}

                        <div class="editorial__link">
                          <a
                            href="{{ article.url }}"
                            class="btn {{ button_style }} {{ section.settings.button_size }} {{ section.settings.button_type }}"
                          >
                            <span>{{ button_text }}</span>

                            {%- if section.settings.show_arrow -%}
                              {%- render 'icon-arrow-right' -%}
                            {%- endif -%}
                          </a>
                        </div>
                      {%- endif -%}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          {%- endfor -%}
        </slider-component>
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "Featured blog posts",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_author",
      "label": "Show author",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "label": "Show date",
      "default": false
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "select",
      "id": "heading_font_size",
      "label": "Heading size",
      "default": "heading-large",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "header",
      "content": "Button"
    },
    {
      "type": "checkbox",
      "id": "show_button",
      "label": "Show 'Read more' button",
      "default": true
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Label",
      "default": "Read more"
    },
    {
      "type": "select",
      "id": "button_type",
      "label": "Color",
      "default": "btn--primary",
      "options": [
        {"value": "btn--black", "label": "Black"},
        {"value": "btn--white", "label": "White"},
        {"value": "btn--primary", "label": "Primary"},
        {"value": "btn--secondary", "label": "Secondary"}
      ]
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Size",
      "default": "",
      "options": [
        {"label": "Small", "value": "btn--small"},
        {"label": "Medium", "value": ""},
        {"label": "Large", "value": "btn--large"}
      ]
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Style",
      "default": "btn--text",
      "options": [
        {"label": "Solid", "value": "btn--solid"},
        {"label": "Outline", "value": "btn--outline"},
        {"label": "Text", "value": "btn--text"}
      ]
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "Show button arrow",
      "default": false
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"}
      ]
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "height",
      "label": "Height",
      "default": "five-fifty-height-hero",
      "options": [
        {"value": "image-height", "label": "Image height"},
        {"value": "screen-height-full", "label": "Full screen height"},
        {"value": "screen-height-three-quarters", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds", "label": "2/3 of screen"},
        {"value": "screen-height-one-half", "label": "1/2 of screen"},
        {"value": "screen-height-one-third", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero", "label": "750px"},
        {"value": "sixty-fifty-height-hero", "label": "650px"},
        {"value": "five-fifty-height-hero", "label": "550px"},
        {"value": "four-fifty-height-hero", "label": "450px"}
      ]
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Height",
      "default": "screen-height-one-half--mobile",
      "options": [
        {"value": "image-height--mobile", "label": "Image height"},
        {"value": "screen-height-full--mobile", "label": "Full screen height"},
        {"value": "screen-height-three-quarters--mobile", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds--mobile", "label": "2/3 of screen"},
        {"value": "screen-height-one-half--mobile", "label": "1/2 of screen"},
        {"value": "screen-height-one-third--mobile", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero--mobile", "label": "750px"},
        {"value": "sixty-fifty-height-hero--mobile", "label": "650px"},
        {"value": "five-fifty-height-hero--mobile", "label": "550px"},
        {"value": "four-fifty-height-hero--mobile", "label": "450px"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "featured_post",
      "name": "Featured post",
      "settings": [
        {
          "type": "article",
          "id": "featured_post",
          "label": "Featured post"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "Optional: replace the article featured image. 2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Featured post"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Featured blog posts",
      "blocks": [
        {
          "type": "featured_post"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "footer"]
  }
}
{% endschema %}
