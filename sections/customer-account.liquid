<!-- /sections/customer-account.liquid -->

<section class="account">
  <div class="wrapper">
    <div class="grid grid--account">
      {%- render 'account-menu' -%}

      <div class="account-main">
        <h2 class="page__heading">{{ 'customer.orders.title' | t }}</h2>
        {% paginate customer.orders by 20 %}
          {% if customer.orders.size != 0 %}
            <table>
              <thead>
                <tr>
                  <th class="order-number">{{ 'customer.orders.order_number' | t }}</th>
                  <th class="date">{{ 'customer.orders.date' | t }}</th>
                  <th class="payment-status">{{ 'customer.orders.payment_status' | t }}</th>
                  <th class="fulfillment-status">{{ 'customer.orders.fulfillment_status' | t }}</th>
                  <th class="total">{{ 'customer.orders.total' | t }}</th>
                </tr>
              </thead>
              <tbody>
                {% for order in customer.orders %}
                  <tr class="{% cycle 'odd', 'even' %} {% if order.cancelled %}cancelled_order{% endif %}">
                    <td class="order-number">
                      <span>{{ order.name | link_to: order.customer_url }}</span>
                    </td>
                    <td class="date">
                      <span class="note">{{ order.created_at | date: format: 'full_date' }}</span>
                    </td>
                    <td class="payment-status">
                      <span>{{ order.financial_status_label }}</span>
                      {% if order.cancelled %}
                        {% assign order_cancelled_at = order.cancelled_at | date: format: 'full_date_and_time' %}
                        <p class="small highlight">
                          {{ 'customer.order.cancelled' | t: date: order_cancelled_at }}
                          <br>
                          {{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason }}
                        </p>
                      {% endif %}
                    </td>
                    <td class="fulfillment-status">
                      <span>{{ order.fulfillment_status_label }}</span>
                    </td>
                    <td class="total">
                      <span class="total money">
                        {%- if order.total_price == 0 -%}
                          {{ 'general.money.free' | t }}
                        {%- else -%}
                          {{ order.total_price | money_with_currency }}
                        {%- endif -%}
                      </span>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          {% else %}
            <p>{{ 'customer.orders.none' | t }}</p>
          {% endif %}

          {% render 'pagination', paginate: paginate %}

        {% endpaginate %}
      </div>
    </div>
  </div>
</section>

{% schema %}
  {
    "name": "Account",
    "blocks": [
      {
        "type": "@app"
      },
      {
        "type": "account",
        "name": "Account links"
      },
      {
        "type": "email",
        "name": "Email link",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Email title",
            "default": "Support"
          },
          {
            "type": "text",
            "id": "link",
            "label": "Link",
            "info": "Leave blank to use the store email address"
          }
        ]
      },
      {
        "type": "link",
        "name": "Link",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Link title",
            "default": "Home"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Link",
            "default": "/"
          }
        ]
      }
    ]
  }
{% endschema %}
