{%- assign products = section.settings.product_list -%}

{%- if products != blank -%}

{%- liquid
  assign section_name = 'ProductCompare'
  assign section_id = section_name | append: '--' | append: section.id

  assign animation_anchor = '#' | append: section_id
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign compare_color_scheme = 'color-' | append: section.settings.compare_color_scheme
-%}

{%- capture style -%}
  --PT: {{ section.settings.padding_top }}px;
  --PB: {{ section.settings.padding_bottom }}px;
{%- endcapture -%}

<section
  id="{{ section_id }}"
  data-section-id="{{ section.id }}"
  data-section-type="product-comparison"
  class="
    bespoke-product-comparison
    {{ color_scheme }}
    {% unless section.settings.width == "wrapper--full" %}section-padding{% endunless %}
    no-padding--medium-down"
  style="{{ style }}">


  {% comment %} ----- Sidebar ----- {% endcomment %}

  {%- capture compare_sidebar -%}

    {%- if section.settings.title != blank or section.settings.subheading != blank -%}
      <div class="compare-sidebar__text">

        {%- if section.settings.eyebrow != blank -%}
          <p class="compare-sidebar__eyebrow {{ section.settings.eyebrow_font_size }}">
            {{ section.settings.eyebrow }}
          </p>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          {%- liquid
            assign heading_tag = section.settings.heading_tag
          -%}
          <{{ heading_tag }} class="compare-sidebar__title {{ section.settings.title_font_size }}">
            {{ section.settings.title }}
          </{{ heading_tag }}>
        {%- endif -%}

        {%- if section.settings.text != blank -%}
          <div class="compare-sidebar__text {{ section.settings.text_font_size }}">
            {{ section.settings.text }}
          </div>
        {%- endif -%}

      </div>
    {%- endif -%}

    {%- if section.settings.button_text != blank -%}
      <div class="compare-sidebar__actions">
        <a href="{{ section.settings.button_link | default: "#" }}"
           class="btn {{ section.settings.button_style }} {{ section.settings.button_size }} {{ section.settings.button_type }}">
          <span>{{ section.settings.button_text }}</span>
        </a>
      </div>
    {%- endif -%}

  {%- endcapture -%}


  {% comment %} ----- Content ----- {% endcomment %}

  {%- capture compare_content -%}

    {%- if section.settings.product_list != blank -%}

      {%- assign spacer = true -%}
      {%- assign use_product_cards = true -%}

      <div class="comparison-table" data-comparison-table>

        <div class="comparison-table__head">
          {% comment %} Products {% endcomment %}

          <div class="comparison-table__cell comparison-table__cell--label">
            <span class="visually-hidden">Products</span>
          </div>

          {%- if spacer == true -%}
            <div class="comparison-table__cell comparison-table__cell--spacer">
              &nbsp;
            </div>
          {%- endif -%}

          {%- for product in products -%}
            <div class="comparison-table__cell comparison-table__cell--head-product">

              {%- if use_product_cards == true -%}
                {%- render 'product-grid-item',
                  product: product,
                  animation_delay: animation_delay,
                  index: index,
                  hide_badges: true,
                  hide_prices: true,
                  hide_hover_images: true,
                  hide_quick_add: true,
                  sizes: image_sizes
                -%}
              {%- else -%}

                <a href="{{ product.url }}">
                  {%- render 'image',
                    image: product.featured_image,
                    widths: '365, 550, 730, 1100, 1460',
                    loading: 'eager',
                    cover: true
                  -%}
                </a>
              {%- endif -%}

            </div>
          {%- endfor -%}

        </div>

        <div class="comparison-table__body">

          {%- for n in (1..10) -%}

            {%- capture compare_field_label_setting -%}compare_field_{{ n }}_label{%- endcapture -%}
            {%- capture compare_field_value_setting -%}compare_field_{{ n }}_keyvalue{%- endcapture -%}

            {%- assign namespacekey = section.settings[compare_field_value_setting] -%}
            {%- assign namespacekey_array = namespacekey | strip | split: "." -%}
            {%- assign metafield_namespace = namespacekey_array | first -%}
            {%- assign metafield_key = namespacekey_array | last -%}

            {%- capture compare_field_label -%}{{ section.settings[compare_field_label_setting] }}{%- endcapture -%}

            {%- if compare_field_label != blank -%}
              <div class="comparison-table__row">

                <div class="comparison-table__cell comparison-table__cell--label">
                  <span class="comparison-table__cell__label subheading-eyebrow-2">{{ compare_field_label }}</span>
                </div>

                {%- if spacer == true -%}
                  <div class="comparison-table__cell comparison-table__cell--spacer">
                    &nbsp;
                  </div>
                {%- endif -%}

                {%- for product in products -%}
                  <div class="comparison-table__cell">
                    {%- assign metafield = product.metafields[metafield_namespace][metafield_key] -%}
                    {% render 'product-compare-field', 
                      metafield: metafield,
                      label: compare_field_label,
                      hide_label: true
                    %}
                  </div>
                {%- endfor -%}

              </div>
            {%- endif -%}

          {%- endfor -%}

        </div>

      </div>

    {%- endif -%}

  {%- endcapture -%}


  {% comment %} ----- DISPLAY ----- {% endcomment %}

  <div class="{{ section.settings.width }} no-padding--medium-down">

    <div class="compare-wrapper {{ compare_color_scheme }}">
      {%- if compare_sidebar != blank -%}
        <div class="compare-sidebar">
          {{ compare_sidebar }}
        </div>
      {%- endif -%}
      {%- if compare_content != blank -%}
        <div class="compare-content background--bg-accent">
          {{ compare_content }}
        </div>
      {%- endif -%}
    </div>

  </div>

</section>
  
{%- endif -%}

{% schema %}
{
  "name": "💄 Product Comparison",
  "settings": [
    {
      "type": "product_list",
      "id": "product_list",
      "label": "Products to Compare",
      "info": "Products to show in the comparison table. Overrides the products added as blocks."
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full-padded",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"}
      ]
    },
    {
      "type": "header",
      "content": "Sidebar"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "select",
      "id": "title_font_size",
      "label": "Title font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": ""
    },
    {
      "type": "text",
      "id": "eyebrow",
      "label": "Eyebrow text"
    },
    {
      "type": "select",
      "id": "eyebrow_font_size",
      "label": "Eyebrow font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": "text-badge-lg"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "text_font_size",
      "label": "Text font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": "body-medium"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "select",
      "id": "button_type",
      "label": "Color",
      "default": "btn--primary",
      "options": [
        {"value": "btn--black", "label": "Black"},
        {"value": "btn--white", "label": "White"},
        {"value": "btn--primary", "label": "Primary"},
        {"value": "btn--secondary", "label": "Secondary"}
      ]
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Size",
      "default": "",
      "options": [
        {"label": "Small", "value": "btn--small"},
        {"label": "Medium", "value": ""},
        {"label": "Large", "value": "btn--large"}
      ]
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Style",
      "default": "btn--solid",
      "options": [
        {"label": "Solid", "value": "btn--solid"},
        {"label": "Outline", "value": "btn--outline"},
        {"label": "Text", "value": "btn--text"}
      ]
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "h2",
      "options": [
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    },
    {
      "type": "header",
      "content": "Compare Fields"
    },
    {
      "type": "text",
      "id": "compare_field_1_label",
      "label": "Field 1 - Label",
      "placeholder": "Best For",
      "info": "The name of the metafield to be compared."
    },
    {
      "type": "text",
      "id": "compare_field_1_keyvalue",
      "label": "Field 1 - Value",
      "placeholder": "custom.best_for",
      "info": "The namespace/key pair of the metafield to be compared, in the format `custom.best_for`."
    },
    {
      "type": "text",
      "id": "compare_field_2_label",
      "label": "Field 2 - Label",
      "placeholder": "Features",
      "info": "The name of the metafield to be compared."
    },
    {
      "type": "text",
      "id": "compare_field_2_keyvalue",
      "label": "Field 2 - Value",
      "placeholder": "custom.features",
      "info": "The namespace/key pair of the metafield to be compared, in the format `custom.features`."
    },
    {
      "type": "text",
      "id": "compare_field_3_label",
      "label": "Field 3 - Label",
      "placeholder": "Price",
      "info": "The name of the metafield to be compared."
    },
    {
      "type": "text",
      "id": "compare_field_3_keyvalue",
      "label": "Field 3 - Value",
      "placeholder": "custom.price",
      "info": "The namespace/key pair of the metafield to be compared, in the format `custom.price`."
    },
    {
      "type": "text",
      "id": "compare_field_4_label",
      "label": "Field 4 - Label",
      "placeholder": "Color",
      "info": "The name of the metafield to be compared."
    },
    {
      "type": "text",
      "id": "compare_field_4_keyvalue",
      "label": "Field 4 - Value",
      "placeholder": "custom.color",
      "info": "The namespace/key pair of the metafield to be compared, in the format `custom.color`."
    },
    {
      "type": "text",
      "id": "compare_field_5_label",
      "label": "Field 5 - Label",
      "placeholder": "Size",
      "info": "The name of the metafield to be compared."
    },
    {
      "type": "text",
      "id": "compare_field_5_keyvalue",
      "label": "Field 5 - Value",
      "placeholder": "custom.size",
      "info": "The namespace/key pair of the metafield to be compared, in the format `custom.size`."
    },
    {
      "type": "text",
      "id": "compare_field_6_label",
      "label": "Field 6 - Label",
      "placeholder": "Material",
      "info": "The name of the metafield to be compared."
    },
    {
      "type": "text",
      "id": "compare_field_6_keyvalue",
      "label": "Field 6 - Value",
      "placeholder": "custom.material",
      "info": "The namespace/key pair of the metafield to be compared, in the format `custom.material`."
    },
    {
      "type": "text",
      "id": "compare_field_7_label",
      "label": "Field 7 - Label",
      "placeholder": "Weight",
      "info": "The name of the metafield to be compared."
    },
    {
      "type": "text",
      "id": "compare_field_7_keyvalue",
      "label": "Field 7 - Value",
      "placeholder": "custom.weight",
      "info": "The namespace/key pair of the metafield to be compared, in the format `custom.weight`."
    },
    {
      "type": "text",
      "id": "compare_field_8_label",
      "label": "Field 8 - Label",
      "placeholder": "Dimensions",
      "info": "The name of the metafield to be compared."
    },
    {
      "type": "text",
      "id": "compare_field_8_keyvalue",
      "label": "Field 8 - Value",
      "placeholder": "custom.dimensions",
      "info": "The namespace/key pair of the metafield to be compared, in the format `custom.dimensions`."
    },
    {
      "type": "text",
      "id": "compare_field_9_label",
      "label": "Field 9 - Label",
      "placeholder": "Care",
      "info": "The name of the metafield to be compared."
    },
    {
      "type": "text",
      "id": "compare_field_9_keyvalue",
      "label": "Field 9 - Value",
      "placeholder": "custom.care",
      "info": "The namespace/key pair of the metafield to be compared, in the format `custom.care`."
    },
    {
      "type": "text",
      "id": "compare_field_10_label",
      "label": "Field 10 - Label",
      "placeholder": "Warranty",
      "info": "The name of the metafield to be compared."
    },
    {
      "type": "text",
      "id": "compare_field_10_keyvalue",
      "label": "Field 10 - Value",
      "placeholder": "custom.warranty",
      "info": "The namespace/key pair of the metafield to be compared, in the format `custom.warranty`."
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Section Color scheme"
    },
    {
      "type": "color_scheme",
      "id": "compare_color_scheme",
      "default": "scheme_1",
      "label": "Compare color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 50
    }
  ],
  "presets": [
    {
      "name": "💄 Product Comparison"
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "custom.pre_footer", "footer"]
  }
}
{% endschema %}
