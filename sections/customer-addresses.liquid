<!-- /sections/customer-addresses.liquid -->
<script src="{{ 'customer-addresses.js' | asset_url }}" defer="defer"></script>

<section class="account">
  <div class="wrapper">
    <addresses-component class="grid grid--account">
      {%- render 'account-menu' -%}

      <div class="account-main">
        <header role="banner" class="account-header">
          <h2>{{ 'customer.addresses.title' | t }}</h2>
          <button type="button" class="btn btn--solid btn--primary address-new-toggle">
            <span>{{ 'customer.addresses.add_new' | t }}</span>
          </button>
        </header>

        {%- paginate customer.addresses by 5 -%}
          <div id="AddressNewForm" class="hidden">
            {%- form 'customer_address', customer.new_address -%}
              <h2>{{ 'customer.addresses.add_new' | t }}</h2>

              <div class="grid grid--2">
                <div class="grid__item">
                  <div class="form-field">
                    <label for="AddressFirstNameNew">{{ 'customer.addresses.first_name' | t }}</label>
                    <input placeholder="{{ 'customer.addresses.first_name' | t }}" type="text" name="address[first_name]" id="AddressFirstNameNew" value="{{ form.first_name }}" autocapitalize="words">
                  </div>
                </div>

                <div class="grid__item">
                  <div class="form-field">
                    <label for="AddressLastNameNew">{{ 'customer.addresses.last_name' | t }}</label>
                    <input placeholder="{{ 'customer.addresses.last_name' | t }}" type="text" name="address[last_name]" id="AddressLastNameNew" value="{{ form.last_name }}" autocapitalize="words">
                  </div>
                </div>
              </div>

              <div class="grid grid--1">
                <div class="grid__item">
                  <div class="form-field">
                    <label for="AddressCompanyNew">{{ 'customer.addresses.company' | t }}</label>
                    <input placeholder="{{ 'customer.addresses.company' | t }}" type="text" name="address[company]" id="AddressCompanyNew" value="{{ form.company }}" autocapitalize="words">
                  </div>
                </div>
              </div>

              <div class="grid grid--2">
                <div class="grid__item">
                  <div class="form-field">
                    <label for="AddressAddress1New">{{ 'customer.addresses.address1' | t }}</label>
                    <input placeholder="{{ 'customer.addresses.address1' | t }}" type="text" name="address[address1]" id="AddressAddress1New" value="{{ form.address1 }}" autocapitalize="words">
                  </div>
                </div>

                <div class="grid__item">
                  <div class="form-field">
                    <label for="AddressAddress2New">{{ 'customer.addresses.address2' | t }}</label>
                    <input placeholder="{{ 'customer.addresses.address2' | t }}" type="text" name="address[address2]" id="AddressAddress2New" value="{{ form.address2 }}" autocapitalize="words">
                  </div>
                </div>
              </div>

              <div class="grid grid--3">
                <div class="grid__item">
                  <div class="form-field">
                    <label for="AddressCityNew">{{ 'customer.addresses.city' | t }}</label>
                    <input placeholder="{{ 'customer.addresses.city' | t }}" type="text" name="address[city]" id="AddressCityNew" value="{{ form.city }}" autocapitalize="words">
                  </div>
                </div>

                <div class="grid__item">
                  <div class="form-field">
                    <label for="AddressCountryNew">{{ 'customer.addresses.country' | t }}</label>
                    <select name="address[country]" id="AddressCountryNew" data-default="{{ form.country }}">{{ country_option_tags }}</select>
                  </div>
                </div>

                <div class="grid__item" id="AddressProvinceContainerNew" style="display: none">
                  <div class="form-field">
                    <label for="AddressProvinceNew">{{ 'customer.addresses.province' | t }}</label>
                    <select name="address[province]" id="AddressProvinceNew" data-default="{{ form.province }}"></select>
                  </div>
                </div>
              </div>

              <div class="grid grid--2">
                <div class="grid__item">
                  <div class="form-field">
                    <label for="AddressZipNew">{{ 'customer.addresses.zip' | t }}</label>
                    <input placeholder="{{ 'customer.addresses.zip' | t }}" type="text" name="address[zip]" id="AddressZipNew" value="{{ form.zip }}" autocapitalize="characters">
                  </div>
                </div>

                <div class="grid__item">
                  <div class="form-field">
                    <label for="AddressPhoneNew">{{ 'customer.addresses.phone' | t }}</label>
                    <input placeholder="{{ 'customer.addresses.phone' | t }}" type="tel" name="address[phone]" id="AddressPhoneNew" value="{{ form.phone }}">
                  </div>
                </div>
              </div>

              <p>
                {{ form.set_as_default_checkbox }}
                <label for="address_default_address_new">{{ 'customer.addresses.set_default' | t }}</label>
              </p>

              <button type="submit" class="btn btn--black btn--solid">
                <span>{{ 'customer.addresses.add' | t }}</span>
              </button>

              <button type="button" class="text-link address-new-toggle">
                {{ 'customer.addresses.cancel' | t }}
              </button>

            {%- endform -%}

            <hr>
          </div>

          {%- for address in customer.addresses -%}
            {%- if address == customer.default_address -%}
              <p><strong>{{ 'customer.addresses.default' | t }}</strong></p>
            {%- endif -%}
            {{ address | format_address }}

            <button type="button" class="btn btn--solid btn--primary address-edit-toggle" data-form-id="{{ address.id }}">
              <span>{{ 'customer.addresses.edit' | t }}</span>
            </button>
            <button type="button" class="text-link address-delete" data-form-id="{{ address.id }}" data-confirm-message="{{ 'customer.addresses.delete_confirm' | t }}">
              {{ 'customer.addresses.delete' | t }}
            </button>

            <div id="EditAddress_{{ address.id }}" class="hidden">
              <hr>
              {%- form 'customer_address', address -%}
                <h2>{{ 'customer.addresses.edit_address' | t }}</h2>

                <div class="grid grid--2">
                  <div class="grid__item">
                    <div class="form-field">
                      <label for="AddressFirstName_{{ form.id }}">
                        {{ 'customer.addresses.first_name' | t }}
                      </label>
                      <input placeholder="{{ 'customer.addresses.first_name' | t }}" type="text" name="address[first_name]" id="AddressFirstName_{{ form.id }}" value="{{ form.first_name }}" autocapitalize="words">
                    </div>
                  </div>

                  <div class="grid__item">
                    <div class="form-field">
                      <label for="AddressLastName_{{ form.id }}">
                        {{ 'customer.addresses.last_name' | t }}
                      </label>
                      <input placeholder="{{ 'customer.addresses.last_name' | t }}" type="text" name="address[last_name]" id="AddressLastName_{{ form.id }}" value="{{ form.last_name }}" autocapitalize="words">
                    </div>
                  </div>
                </div>

                <div class="grid grid--1">
                  <div class="grid__item">
                    <div class="form-field">
                      <label for="AddressCompany_{{ form.id }}">{{ 'customer.addresses.company' | t }}</label>
                      <input placeholder="{{ 'customer.addresses.company' | t }}" type="text" name="address[company]" id="AddressCompany_{{ form.id }}" value="{{ form.company }}" autocapitalize="words">
                    </div>
                  </div>
                </div>

                <div class="grid grid--2">
                  <div class="grid__item">
                    <div class="form-field">
                      <label for="AddressAddress1_{{ form.id }}">
                        {{ 'customer.addresses.address1' | t }}
                      </label>
                      <input placeholder="{{ 'customer.addresses.address1' | t }}" type="text" name="address[address1]" id="AddressAddress1_{{ form.id }}" value="{{ form.address1 }}" autocapitalize="words">
                    </div>
                  </div>

                  <div class="grid__item">
                    <div class="form-field">
                      <label for="AddressAddress2_{{ form.id }}">{{ 'customer.addresses.address2' | t }}</label>
                      <input placeholder="{{ 'customer.addresses.address2' | t }}" type="text" name="address[address2]" id="AddressAddress2_{{ form.id }}" value="{{ form.address2 }}" autocapitalize="words">
                    </div>
                  </div>
                </div>

                <div class="grid grid--3">
                  <div class="grid__item">
                    <div class="form-field">
                      <label for="AddressCity_{{ form.id }}">{{ 'customer.addresses.city' | t }}</label>
                      <input placeholder="{{ 'customer.addresses.city' | t }}" type="text" name="address[city]" id="AddressCity_{{ form.id }}" value="{{ form.city }}" autocapitalize="words">
                    </div>
                  </div>

                  <div class="grid__item">
                    <div class="form-field">
                      <label for="AddressCountry_{{ form.id }}">{{ 'customer.addresses.country' | t }}</label>
                      <select name="address[country]" id="AddressCountry_{{ form.id }}" class="address-country-option" data-form-id="{{ form.id }}" data-default="{{ form.country }}">
                        {{ country_option_tags }}
                      </select>
                    </div>
                  </div>

                  <div class="grid__item" id="AddressProvinceContainer_{{ form.id }}" style="display: none">
                    <div class="form-field">
                      <label for="AddressProvince_{{ form.id }}">{{ 'customer.addresses.province' | t }}</label>
                      <select name="address[province]" id="AddressProvince_{{ form.id }}" data-default="{{ form.province }}"></select>
                    </div>
                  </div>
                </div>

                <div class="grid grid--2">
                  <div class="grid__item">
                    <div class="form-field">
                      <label for="AddressZip_{{ form.id }}">{{ 'customer.addresses.zip' | t }}</label>
                      <input placeholder="{{ 'customer.addresses.zip' | t }}" type="text" name="address[zip]" id="AddressZip_{{ form.id }}" value="{{ form.zip }}" autocapitalize="characters">
                    </div>
                  </div>

                  <div class="grid__item">
                    <div class="form-field">
                      <label for="AddressPhone_{{ form.id }}">{{ 'customer.addresses.phone' | t }}</label>
                      <input placeholder="{{ 'customer.addresses.phone' | t }}" type="tel" name="address[phone]" id="AddressPhone_{{ form.id }}" value="{{ form.phone }}">
                    </div>
                  </div>
                </div>

                <p>
                  {{ form.set_as_default_checkbox }}
                  <label for="address_default_address_{{ form.id }}">
                    {{ 'customer.addresses.set_default' | t }}
                  </label>
                </p>

                <button type="submit" class="btn btn--solid btn--primary">
                  <span>{{ 'customer.addresses.update' | t }}</span>
                </button>

                <button type="button" class="text-link address-edit-toggle" data-form-id="{{ form.id }}">
                  {{ 'customer.addresses.cancel' | t }}
                </button>

              {%- endform -%}
            </div>
            <hr>
          {%- endfor -%}

          {%- render 'pagination', paginate: paginate -%}
        {%- endpaginate -%}
      </div>
    </addresses-component>
  </div>
</section>

{% schema %}
  {
    "name": "Order",
    "blocks": [
      {
        "type": "@app"
      },
      {
        "type": "account",
        "name": "Account links"
      },
      {
        "type": "email",
        "name": "Email link",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Email title",
            "default": "Support"
          },
          {
            "type": "text",
            "id": "link",
            "label": "Link",
            "info": "Leave blank to use the store email address"
          }
        ]
      },
      {
        "type": "link",
        "name": "Link",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Link title",
            "default": "Home"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Link",
            "default": "/"
          }
        ]
      }
    ]
  }
{% endschema %}
