<header id="header" class="giftcard-header">
  <div class="h1">
    {% if section.settings.logo != blank %}
      <a href="{{ shop.url }}" itemprop="url">
        <img
          src="{{ section.settings.logo | image_url }}"
          style="width: {{ section.settings.logo_max_limit }}px; margin: 0 auto;"
          alt="{{ section.settings.logo.alt | default: shop.name }}"
          loading="eager"
        >
      </a>
    {% else %}
      <a href="{{ shop.url }}">
        {{ shop.name | escape }}
      </a>
    {% endif %}
  </div>
  <div class="shop-url">{{ shop.url }}</div>
</header>
{% schema %}
{
  "name": "Gift card",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo image",
      "info": "300 x 90px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "range",
      "id": "logo_max_limit",
      "min": 5,
      "max": 495,
      "step": 5,
      "unit": "px",
      "label": "Logo width",
      "default": 300
    }
  ]
}
{% endschema %}
