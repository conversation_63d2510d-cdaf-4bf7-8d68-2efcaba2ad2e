{%- liquid
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign default_open = section.settings.default_open
  assign animation_anchor = '#Faq--' | append: section.id
-%}

{%- if section.blocks.size > 0 -%}
  {%- style -%}
    #Faq--{{ section.id }} {
      --PT: {{ section.settings.padding_top }}px;
      --PB: {{ section.settings.padding_bottom }}px;
    }
  {%- endstyle -%}

  <section
    id="Faq--{{ section.id }}"
    class="accordion-section section-padding {{ color_scheme }}"
    data-section-id="{{ section.id }}"
    data-section-type="faq"
    data-aos="hero"
    data-aos-anchor="{{ animation_anchor }}"
  >
    <div class="{{ section.settings.width }}">
      {%- if section.settings.title != blank -%}
        <collapsible-elements>
          <details
            class="accordion"
            {% if default_open %}
              open="true"
            {% endif %}
            data-collapsible
          >
            {%- if section.settings.title != blank -%}
              <summary
                class="accordion__title {{ section.settings.heading_font_size }} font-heading"
                data-collapsible-trigger
              >
                {%- if section.settings.show_icon -%}
                  {%- liquid
                    assign icon_size = section.settings.icon_size
                    assign icon_color = section.settings.icon_color
                    assign icon_image = section.settings.icon_image
                    capture icon_style
                      echo 'style="'
                      echo '--icon-size: ' | append: icon_size | append: 'px;'
                      if icon_color != blank and icon_color != 'rgba(0,0,0,0)'
                        echo ' --icons: ' | append: icon_color | append: ';'
                      endif
                      echo '"'
                    endcapture
                  -%}

                  <div
                    class="icon__animated icon__animated--{{ section.id }}{% if icon_image != blank %} icon__animated--image{% endif %}"
                    {{ icon_style }}
                  >
                    {%- liquid
                      if icon_image
                        assign icon_width = icon_size
                        assign icon_width_retina = icon_width | times: 2
                        assign icon_sizes = icon_width | append: 'px'
                        assign icon_widths = icon_width | append: ', ' | append: icon_width_retina

                        render 'image', image: icon_image, width: icon_width_retina, sizes: icon_sizes, widths: icon_widths
                      else
                        render 'animated-icon', filename: section.settings.icon_name
                      endif
                    -%}
                  </div>
                {%- endif -%}

                {{ section.settings.title }}

                {%- render 'icon-plus' -%}
                {%- render 'icon-minus' -%}
              </summary>
            {%- endif -%}

            <div
              class="accordion__body rte"
              {% if default_open != blank %}
                style="height: auto;"
              {% endif %}
              data-collapsible-body
            >
              <div class="accordion__content" data-collapsible-content>
                <div class="faq-list faq-list--{{ section.blocks.size }}">
                  {%- for block in section.blocks -%}
                    {%- capture text_content -%}
                      <div class="faq-list__item-description {{ block.settings.text_alignment }}">
                        {%- if block.settings.title != blank -%}
                          <h3 class="faq-list__item-title {{ block.settings.heading_font_size }}">
                            {{ block.settings.title }}
                          </h3>
                        {%- endif -%}

                        <div class="faq-list__item-content">
                          {{ block.settings.text }}
                        </div>
                      </div>
                    {%- endcapture -%}

                    {%- case block.type -%}
                      {%- when '@app' -%}
                        <div class="faq-list__item">
                          {%- render block -%}
                        </div>

                      {%- when 'text' -%}
                        <div class="faq-list__item" {{ block.shopify_attributes }}>
                          {{ text_content }}
                        </div>
                      {%- when 'icon' -%}
                        <div class="faq-list__item" {{ block.shopify_attributes }}>
                          {%- liquid
                            capture icon_alignment
                              if block.settings.icon_alignment == 'left'
                                echo 'faq-list__item-icon--left'
                              else
                                echo 'faq-list__item-icon--center'
                              endif
                            endcapture
                          -%}

                          <div class="faq-list__item-icon {{ icon_alignment }}">
                            {%- liquid
                              assign icon_size = block.settings.icon_size
                              assign icon_color = block.settings.icon_color
                              capture icon_style
                                echo 'style="'
                                echo '--icon-size: ' | append: icon_size | append: 'px;'
                                if icon_color != blank and icon_color != 'rgba(0,0,0,0)'
                                  echo ' --icons: ' | append: icon_color | append: ';'
                                endif
                                echo '"'
                              endcapture
                            -%}

                            <div class="icon__animated icon__animated--{{ block.id }}" {{ icon_style }}>
                              {%- render 'animated-icon', filename: block.settings.icon_name -%}
                            </div>
                          </div>
                          {{ text_content }}
                        </div>
                      {%- when 'image' -%}
                        <div class="faq-list__item" {{ block.shopify_attributes }}>
                          <div class="faq-list__item-image">
                            {%- liquid
                              assign aspect_ratio = 1 | divided_by: block.settings.aspect_ratio

                              if section.settings.width == 'wrapper'
                                assign sizes = '(min-width: 1400px) calc((80vw - 100px) * 0.45), (min-width: 750px) calc(1100px * 0.45), calc(100vw - 32px - 50px)'
                              else
                                assign sizes = '(min-width: 750px) calc(1100px * 0.45), calc(100vw - 32px - 50px)'
                              endif
                            -%}
                            {%- render 'image',
                              image: block.settings.image,
                              aspect_ratio: aspect_ratio,
                              sizes: sizes,
                              widths: '360, 540, 720, 900, 1080, 1296, 1512, 1728, 1950, 2100',
                              loading: 'lazy',
                              fetchpriority: 'auto'
                            -%}
                          </div>
                          {{ text_content }}
                        </div>
                      {%- when 'code' -%}
                        <div class="faq-list__item" {{ block.shopify_attributes }}>
                          {{ block.settings.code }}
                        </div>
                      {%- when 'contact-form' -%}
                        <div class="faq-list__item faq-list__item--form" {{ block.shopify_attributes }}>
                          {%- assign section_block_id = section.id | append: '-' | append: block.id -%}
                          {%- assign form_id = 'contact-us-page-' | append: section_block_id -%}
                          {%- assign field_name = 'contact.form.name' | t -%}
                          {%- assign field_email = 'contact.form.email' | t -%}
                          {%- assign field_message = 'contact.form.message' | t -%}
                          {%- form 'contact', id: form_id -%}
                            {%- if form.posted_successfully? -%}
                              <p class="form-success">
                                {{ 'contact.form.post_success' | t }}
                              </p>
                            {%- endif -%}

                            {{ form.errors | default_errors }}

                            <div class="custom-form__block">
                              <label for="Form-{{ section.id }}-{{ forloop.index0 }}" class="visually-hidden">
                                {{- field_name -}}
                              </label>
                              <input
                                type="text"
                                class="contactFormText field"
                                id="Form-{{ section_block_id }}-{{ forloop.index0 }}"
                                name="contact[{{ field_name | handleize }}]"
                                placeholder="{{ field_name }}"
                                autocapitalize="words"
                                value=""
                                required
                              >
                            </div>

                            <div class="custom-form__block">
                              <label for="contactFormEmail" class="visually-hidden">{{ field_email }}</label>
                              <input
                                type="email"
                                id="{{ section_block_id }}-contactFormEmail"
                                class="contactFormEmail field"
                                name="contact[email]"
                                placeholder="{{ field_email }}"
                                autocorrect="off"
                                autocapitalize="off"
                                required
                              >
                            </div>

                            <div class="custom-form__block">
                              <label for="ContactFormMessage" class="visually-hidden">{{ field_message }}</label>
                              <textarea
                                rows="10"
                                id="{{ section_block_id }}-ContactFormMessage"
                                class="contactFormMessage field"
                                name="contact[body]"
                                placeholder="{{ field_message }}"
                              ></textarea>
                            </div>

                            <div class="custom-form__block">
                              <input
                                type="submit"
                                class="btn btn--black btn--solid btn--full"
                                value="{{ 'contact.form.send' | t }}"
                              >
                            </div>

                            {%- if block.settings.terms -%}
                              <div class="form__legal">
                                {{ 'shopify.online_store.spam_detection.disclaimer_html' | t }}
                              </div>
                            {%- endif -%}
                          {%- endform -%}
                        </div>
                      {%- else -%}
                    {%- endcase -%}
                  {%- endfor -%}
                </div>
              </div>
            </div>
          </details>
        </collapsible-elements>
      {%- endif -%}
    </div>
  </section>
{%- else -%}
  {%- render 'no-blocks' -%}
{%- endif -%}

{% schema %}
{
  "name": "Accordion",
  "class": "accordions-holder",
  "settings": [
    {
      "type": "checkbox",
      "id": "default_open",
      "label": "Open by default",
      "default": false
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "info": "Section will not be visible if left empty.",
      "default": "FAQ"
    },
    {
      "type": "select",
      "id": "heading_font_size",
      "label": "Heading size",
      "default": "heading-small",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "header",
      "content": "Icon"
    },
    {
      "type": "checkbox",
      "id": "show_icon",
      "label": "Show icon",
      "default": false
    },
    {
      "type": "select",
      "id": "icon_name",
      "label": "Icon",
      "default": "icon-award",
      "options": [
        {"label": "Award", "value": "icon-award"},
        {"label": "Box", "value": "icon-box"},
        {"label": "Chat", "value": "icon-chat"},
        {"label": "Check", "value": "icon-check"},
        {"label": "Check circle", "value": "icon-check-circle"},
        {"label": "Cloud", "value": "icon-cloud"},
        {"label": "Diameter", "value": "icon-diameter"},
        {"label": "Discount", "value": "icon-discount"},
        {"label": "Donation", "value": "icon-donation"},
        {"label": "Droplet", "value": "icon-droplet"},
        {"label": "Info", "value": "icon-info-empty"},
        {"label": "Email", "value": "icon-email"},
        {"label": "Fast shipment", "value": "icon-fast-shipment"},
        {"label": "Flare", "value": "icon-flare"},
        {"label": "Flower", "value": "icon-flower"},
        {"label": "Gift", "value": "icon-gift"},
        {"label": "Green shipment", "value": "icon-green-shipment"},
        {"label": "Heart", "value": "icon-heart"},
        {"label": "Leaf", "value": "icon-leaf"},
        {"label": "Lightning", "value": "icon-lightning"},
        {"label": "Location", "value": "icon-location"},
        {"label": "Mail", "value": "icon-mail"},
        {"label": "Notes", "value": "icon-notes"},
        {"label": "Pants", "value": "icon-pants"},
        {"label": "Peace", "value": "icon-peace"},
        {"label": "Pin", "value": "icon-pin"},
        {"label": "Planet", "value": "icon-planet"},
        {"label": "Phone", "value": "icon-phone"},
        {"label": "Recycle", "value": "icon-recycle"},
        {"label": "Ruler", "value": "icon-ruler"},
        {"label": "Shield", "value": "icon-shield"},
        {"label": "Smile", "value": "icon-smile"},
        {"label": "Star", "value": "icon-star"},
        {"label": "Tree", "value": "icon-tree"},
        {"label": "Trophy", "value": "icon-trophy"},
        {"label": "Truck", "value": "icon-truck"},
        {"label": "Vegan", "value": "icon-vegan"},
        {"label": "Wash", "value": "icon-wash"},
        {"label": "Washing machine", "value": "icon-washing-machine"}
      ]
    },
    {
      "type": "image_picker",
      "id": "icon_image",
      "label": "Alternative icon/image",
      "info": "160 x 160px .svg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "range",
      "id": "icon_size",
      "label": "Size",
      "unit": "px",
      "min": 20,
      "max": 80,
      "step": 5,
      "default": 20
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper",
      "options": [
        {"value": "wrapper", "label": "Normal"},
        {"value": "wrapper--narrow", "label": "Narrow"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icon"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "name": "Text column",
      "type": "text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Frequently asked question"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-small",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this to answer some common questions you hear from your customers. You could discuss product details, size fit, shipping policies, or anything you think would help merchants make an informed decision about your products. This section will appear across all products.</p>"
        }
      ]
    },
    {
      "name": "Image column",
      "type": "image",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Image heading"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-small",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this to answer some common questions you hear from your customers. You could discuss product details, size fit, shipping policies, or anything you think would help merchants make an informed decision about your products. This section will appear across all products.</p>"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1400 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "range",
          "id": "aspect_ratio",
          "min": 0.3,
          "max": 1.5,
          "step": 0.1,
          "unit": ":1",
          "label": "Photo aspect ratio",
          "info": "Wide to tall",
          "default": 0.6
        }
      ]
    },
    {
      "type": "icon",
      "name": "Icon column",
      "settings": [
        {
          "type": "select",
          "id": "icon_name",
          "label": "Icon",
          "default": "icon-award",
          "options": [
            {"label": "Award", "value": "icon-award"},
            {"label": "Box", "value": "icon-box"},
            {"label": "Chat", "value": "icon-chat"},
            {"label": "Check", "value": "icon-check"},
            {"label": "Check circle", "value": "icon-check-circle"},
            {"label": "Cloud", "value": "icon-cloud"},
            {"label": "Diameter", "value": "icon-diameter"},
            {"label": "Discount", "value": "icon-discount"},
            {"label": "Donation", "value": "icon-donation"},
            {"label": "Droplet", "value": "icon-droplet"},
            {"label": "Info", "value": "icon-info-empty"},
            {"label": "Email", "value": "icon-email"},
            {"label": "Fast shipment", "value": "icon-fast-shipment"},
            {"label": "Flare", "value": "icon-flare"},
            {"label": "Flower", "value": "icon-flower"},
            {"label": "Gift", "value": "icon-gift"},
            {"label": "Green shipment", "value": "icon-green-shipment"},
            {"label": "Heart", "value": "icon-heart"},
            {"label": "Leaf", "value": "icon-leaf"},
            {"label": "Lightning", "value": "icon-lightning"},
            {"label": "Location", "value": "icon-location"},
            {"label": "Mail", "value": "icon-mail"},
            {"label": "Notes", "value": "icon-notes"},
            {"label": "Pants", "value": "icon-pants"},
            {"label": "Peace", "value": "icon-peace"},
            {"label": "Pin", "value": "icon-pin"},
            {"label": "Planet", "value": "icon-planet"},
            {"label": "Phone", "value": "icon-phone"},
            {"label": "Recycle", "value": "icon-recycle"},
            {"label": "Ruler", "value": "icon-ruler"},
            {"label": "Shield", "value": "icon-shield"},
            {"label": "Smile", "value": "icon-smile"},
            {"label": "Star", "value": "icon-star"},
            {"label": "Tree", "value": "icon-tree"},
            {"label": "Trophy", "value": "icon-trophy"},
            {"label": "Truck", "value": "icon-truck"},
            {"label": "Vegan", "value": "icon-vegan"},
            {"label": "Wash", "value": "icon-wash"},
            {"label": "Washing machine", "value": "icon-washing-machine"}
          ]
        },
        {
          "type": "range",
          "id": "icon_size",
          "label": "Size",
          "unit": "px",
          "min": 20,
          "max": 80,
          "step": 5,
          "default": 20
        },
        {
          "type": "color",
          "id": "icon_color",
          "label": "Icon color"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Icon heading"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-small",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "select",
          "id": "icon_alignment",
          "label": "Icon alignment",
          "default": "left",
          "options": [
            {"value": "left", "label": "Left"},
            {"value": "center", "label": "Center"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this to answer some common questions you hear from your customers. You could discuss product details, size fit, shipping policies, or anything you think would help merchants make an informed decision about your products. This section will appear across all products.</p>"
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "default": "text-left",
          "options": [
            {"value": "text-left", "label": "Left"},
            {"value": "text-center", "label": "Centered"}
          ]
        }
      ]
    },
    {
      "type": "code",
      "name": "Custom code",
      "settings": [
        {
          "type": "liquid",
          "id": "code",
          "label": "Custom code",
          "default": "<p>Once you write some custom code, it will render right here.</p>"
        }
      ]
    },
    {
      "type": "contact-form",
      "name": "Contact form",
      "settings": [
        {
          "type": "checkbox",
          "id": "terms",
          "label": "Show reCAPTCHA terms",
          "default": true,
          "info": "Recommended if your online store preferences have spam protection enabled."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Accordion",
      "blocks": [
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "custom.pre_footer", "footer"]
  }
}
{% endschema %}
