{%- render 'announcement' -%}

{% schema %}
{
  "name": "Announcement bar",
  "class": "page-announcement",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "checkbox",
      "id": "show_border",
      "label": "Show border",
      "default": false
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "options": [
        {
          "value": "slider",
          "label": "Slider"
        },
        {
          "value": "marquee",
          "label": "Scrolling text"
        }
      ]
    },
    {
      "type": "header",
      "content": "Slider"
    },
    {
      "type": "paragraph",
      "content": "Applies only if Layout is set to Slider"
    },
    {
      "type": "range",
      "id": "slider_speed",
      "label": "Autoplay speed",
      "unit": "sec",
      "min": 5,
      "max": 20,
      "step": 1,
      "default": 7
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "show_arrows",
      "label": "Show arrows",
      "default": false
    },
    {
      "type": "header",
      "content": "Scrolling text"
    },
    {
      "type": "paragraph",
      "content": "Applies only if Layout is set to Scrolling text"
    },
    {
      "type": "range",
      "id": "marquee_speed",
      "label": "Autoplay speed",
      "unit": "%",
      "min": 50,
      "max": 300,
      "step": 25,
      "default": 100
    },
    {
      "type": "range",
      "id": "message_spacing",
      "label": "Space between messages",
      "default": 10,
      "min": 5,
      "max": 100,
      "step": 5,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "select",
      "id": "text_font_size",
      "label": "Text size",
      "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
      "default": "body-small",
      "options": [
        {"value": "body-x-small", "label": "Extra small"},
        {"value": "body-small", "label": "Small"},
        {"value": "body-medium", "label": "Medium"},
        {"value": "body-large", "label": "Large"},
        {"value": "body-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 15,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 15,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "text",
      "name": "Text announcement",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>New customers save 10% with code <strong>GET10</strong></p>"
        },
        {
          "type": "header",
          "content": "Target page"
        },
        {
          "id": "target_url_enabled",
          "type": "checkbox",
          "label": "Limit to specific page"
        },
        {
          "id": "target_url",
          "type": "url",
          "label": "Targeted page"
        },
        {
          "type": "header",
          "content": "Target referrer"
        },
        {
          "id": "target_referrer_enabled",
          "type": "checkbox",
          "label": "Limit to specific referrer path"
        },
        {
          "id": "target_referrer",
          "type": "text",
          "label": "Targeted referrer"
        },
        {
          "type": "header",
          "content": "Target device"
        },
        {
          "id": "target_device_enabled",
          "type": "checkbox",
          "label": "Limit to desktop or mobile"
        },
        {
          "type": "select",
          "id": "target_device",
          "label": "Device ",
          "default": "mobile",
          "options": [
            {"value": "mobile", "label": "Only show on mobile"},
            {"value": "desktop", "label": "Only show on desktop"}
          ]
        }
      ]
    },
    {
      "type": "message",
      "name": "Free shipping message",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_wheel",
          "label": "Show progress wheel",
          "default": false
        },
        {
          "type": "textarea",
          "id": "message",
          "label": "Message",
          "info": "Use ||amount|| to display progress towards free shipping.",
          "default": "You are ||amount|| away from free shipping."
        },
        {
          "type": "paragraph",
          "content": "Navigate to Theme settings -> \"Cart -> Free shipping message\" to set the amount."
        },
        {
          "type": "header",
          "content": "Target page"
        },
        {
          "id": "target_url_enabled",
          "type": "checkbox",
          "label": "Limit to specific page"
        },
        {
          "id": "target_url",
          "type": "url",
          "label": "Targeted page"
        },
        {
          "type": "header",
          "content": "Target referrer"
        },
        {
          "id": "target_referrer_enabled",
          "type": "checkbox",
          "label": "Limit to specific referrer path"
        },
        {
          "id": "target_referrer",
          "type": "text",
          "label": "Targeted referrer"
        },
        {
          "type": "header",
          "content": "Target device"
        },
        {
          "id": "target_device_enabled",
          "type": "checkbox",
          "label": "Limit to desktop or mobile"
        },
        {
          "type": "select",
          "id": "target_device",
          "label": "Device ",
          "default": "mobile",
          "options": [
            {"value": "mobile", "label": "Only show on mobile"},
            {"value": "desktop", "label": "Only show on desktop"}
          ]
        }
      ]
    },
    {
      "type": "code",
      "name": "Custom code",
      "settings": [
        {
          "type": "liquid",
          "id": "code",
          "label": "Code",
          "default": "<p>Custom code</p>"
        },
        {
          "type": "header",
          "content": "Target page"
        },
        {
          "id": "target_url_enabled",
          "type": "checkbox",
          "label": "Limit to specific page"
        },
        {
          "id": "target_url",
          "type": "url",
          "label": "Targeted page"
        },
        {
          "type": "header",
          "content": "Target referrer"
        },
        {
          "id": "target_referrer_enabled",
          "type": "checkbox",
          "label": "Limit to specific referrer path"
        },
        {
          "id": "target_referrer",
          "type": "text",
          "label": "Targeted referrer"
        },
        {
          "type": "header",
          "content": "Target device"
        },
        {
          "id": "target_device_enabled",
          "type": "checkbox",
          "label": "Limit to desktop or mobile"
        },
        {
          "type": "select",
          "id": "target_device",
          "label": "Device ",
          "default": "mobile",
          "options": [
            {"value": "mobile", "label": "Only show on mobile"},
            {"value": "desktop", "label": "Only show on desktop"}
          ]
        }
      ]
    },
    {
      "type": "countdown",
      "name": "Countdown timer",
      "settings": [
        {
          "type": "text",
          "id": "end_date",
          "label": "End date",
          "placeholder": "2025-12-31",
          "default": "2025-12-31",
          "info": "Use format \"YYYY-MM-DD\". Expiration date is based on the [store primary timezone](/admin/settings/general)."
        },
        {
          "type": "text",
          "id": "end_time",
          "label": "End time",
          "default": "11:59",
          "placeholder": "11:59",
          "info": "Use 12-hour time convention in format \"HH:MM\""
        },
        {
          "type": "radio",
          "id": "period",
          "label": "AM/PM",
          "options": [
            {"value": "am", "label": "AM"},
            {"value": "pm", "label": "PM"}
          ],
          "default": "am"
        },
        {
          "type": "richtext",
          "id": "end_message",
          "label": "End of timer message",
          "default": "<p>Offer has expired</p>"
        },
        {
          "type": "checkbox",
          "id": "hide_on_complete",
          "label": "Hide block after end of timer",
          "default": true
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Limited time offer</p>"
        }
      ]
    }
  ],
  "enabled_on": {
    "groups": ["header"]
  }
}
{% endschema %}
