{%- liquid
  assign desktop_height = section.settings.height
  assign mobile_height = section.settings.mobile_height
  assign width = section.settings.width
  assign color_scheme = 'color-' | append: section.settings.color_scheme

  assign border_class = ''
  if section.settings.borders
    assign border_class = 'brick--border'
  endif

  assign transparent_header = false
  assign non_image_block_types = 'text, newsletter, reviews, product, collection' | split: ','
  unless non_image_block_types contains section.blocks[0].type and non_image_block_types contains section.blocks[1].type
    assign transparent_header = true
  endunless

  assign selected_color_scheme = section.settings.color_scheme | strip
  assign scheme_bg_color = settings.color_schemes[selected_color_scheme].settings.section_bg
  assign animation_anchor = '#BrickSection--' | append: section.id

  assign show_video_popup = false
  assign show_video_background = false
  assign video_blocks = section.blocks | where: 'type', 'video'
  for block in video_blocks
    if block.settings.video != null
      assign show_video_background = true
    endif

    if block.settings.show_video_popup
      assign show_video_popup = block.settings.show_video_popup
    endif
  endfor
-%}

{%- if show_video_background -%}
  <script src="{{ 'video-background.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if show_video_popup -%}
  <script src="{{ 'load-photoswipe.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'video-popup.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- style -%}
  #BrickSection--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;
  }
{%- endstyle -%}

<section
  id="BrickSection--{{ section.id }}"
  data-section-id="{{ section.id }}"
  data-section-type="custom-content"
  {% if transparent_header %}
    data-overlay-header
  {% endif %}
  class="section-padding custom-custom-content {{ color_scheme }}"
>
  {%- if section.blocks.size > 0 -%}
    <div class="brick__section{% if section.settings.reverse_blocks %} brick__section--reversed-mobile{% endif %}{% if section.settings.gutters %} brick--margin{% endif %} {{ border_class }} {{ width }}">
      {%- for block in section.blocks -%}
        {%- liquid
          assign animation_order = 0
          assign bg_color = block.settings.bg_color | default: scheme_bg_color
          assign text_color = block.settings.color
          assign padding_class = ''
          assign color_difference = bg_color | color_difference: scheme_bg_color

          if bg_color != 'rgba(0,0,0,0)' and color_difference > 10
            assign padding_class = ' has-padding'
          endif
        -%}

        {%- capture style -%}
          {%- unless bg_color == 'rgba(0,0,0,0)' or bg_color == blank -%}
            --bg: {{ bg_color }};
          {%- endunless -%}

          {%- unless text_color == 'rgba(0,0,0,0)' or text_color == blank -%}
            --text: {{ text_color }};
            --text-light: {{ text_color | color_mix: bg_color, 70 }};
            --text-dark: {{ text_color | color_saturate: 10 | color_darken: 15 }};
          {%- endunless -%}
        {%- endcapture -%}

        {%- case block.type -%}
          {%- when 'image' -%}
            {%- render 'brick-image', block: block, style: style, animation_anchor: animation_anchor -%}

          {%- when 'text' -%}
            {%- render 'brick-text',
              block: block,
              style: style,
              padding_class: padding_class,
              animation_anchor: animation_anchor
            -%}

          {%- when 'newsletter' -%}
            {%- render 'brick-newsletter',
              block: block,
              style: style,
              padding_class: padding_class,
              animation_anchor: animation_anchor
            -%}

          {%- when 'review' -%}
            {%- render 'brick-review',
              block: block,
              style: style,
              padding_class: padding_class,
              animation_anchor: animation_anchor
            -%}

          {%- when 'product' -%}
            <div
              class="brick__block {% if section.settings.block_radius == true %}block-radius{% endif %} {{ block.settings.color_scheme }}"
              {% if style != blank %}
                style="{{ style }}"
              {% endif %}
              {{ block.shopify_attributes }}
            >

              {%- assign color_scheme = 'color-' | append: block.settings.color_scheme -%}
              
              <div class="brick__block__text brick__block__text--column{{ padding_class }} text-center {{ color_scheme }}">
                {%- if block.settings.title != blank -%}
                  {%- assign animation_order = animation_order | plus: 1 -%}
                  <p
                    class="brick__product__subheading subheading {{ block.settings.subheading_font_size }}"
                    data-aos="hero"
                    data-aos-anchor="{{ animation_anchor }}"
                    data-aos-order="{{ animation_order }}"
                  >
                    {{ block.settings.title }}
                  </p>
                {%- endif -%}

                {%- assign animation_order = animation_order | plus: 1 -%}
                <div
                  class="brick__product"
                  data-aos="hero"
                  data-aos-anchor="{{ animation_anchor }}"
                  data-aos-order="{{ animation_order }}"
                >
                  {%- assign product = all_products[block.settings.product] -%}
                  {%- if product != blank -%}
                    {%- render 'product-grid-item', product: product, index: forloop.index -%}
                  {%- else -%}
                    {%- render 'onboarding-product-grid-item', index: forloop.index, animation_delay: forloop.index0 -%}
                  {%- endif -%}
                </div>
              </div>
            </div>
          {%- when 'video' -%}

            {%- assign color_scheme = 'color-' | append: block.settings.color_scheme -%}

            <div
              class="brick__block {{ desktop_height }} {{ mobile_height }} {{ color_scheme }}"
              {% if style != blank %}
                style="{{ style }}"
              {% endif %}
              {{ block.shopify_attributes }}
            >
              <div class="brick__block__video frame">
                {%- render 'video',
                  section: block,
                  desktop_height: desktop_height,
                  mobile_height: mobile_height,
                  block_element: true
                -%}
              </div>
            </div>
          {%- when 'collection' -%}

            {%- assign block_collection = collections[block.settings.collection] -%}
            {%- assign color_scheme = 'color-' | append: block.settings.color_scheme -%}
            
            <div
              class="brick__block brick__block--collection {% if section.settings.block_radius == true %}block-radius{% endif %} {{ color_scheme }}"
              {% if style != blank %}
                style="{{ style }}"
              {% endif %}
              {{ block.shopify_attributes }}
            >
              <div class="brick__block__collection{{ padding_class }}">
                {%- if block.settings.title != blank -%}
                  <p
                    class="look__title text-center {{ block.settings.heading_font_size }} "
                    data-aos="hero"
                    data-aos-anchor="{{ animation_anchor }}"
                    data-aos-order="1"
                  >
                    {{ block.settings.title }}
                  </p>
                {%- endif -%}

                <slider-component
                  class="look__slider"
                  data-slider="{{ block.id }}"
                  data-options='{"watchCSS": true, "fade": true, "pageDots": false, "prevNextButtons": true, "adaptiveHeight": false}'
                  data-arrow-position-middle
                >
                  {%- if block_collection != blank -%}
                    {%- if block_collection.products_count > 0 -%}
                      {% assign index = 0 %}

                      {%- for product in block_collection.products -%}
                        {%- liquid
                          if product.tags contains 'hide'
                            continue
                          endif
                        -%}

                        <div
                          class="look__slide look__slide--{{ block.id }}-{{ forloop.index0 }}"
                          data-slide="{{ block.id }}-{{ forloop.index0 }}"
                          {{ block.shopify_attributes }}
                        >
                          {%- render 'product-grid-item', product: product, index: forloop.index -%}
                        </div>

                        {%- liquid
                          assign index = index | plus: 1
                          if index == 5
                            break
                          endif
                        -%}
                      {%- endfor -%}
                    {%- else -%}
                      <div class="no-results">
                        <p>
                          <strong>{{ 'collections.general.no_matches' | t }}</strong>
                        </p>
                      </div>
                    {%- endif -%}
                  {%- else -%}
                    {%- for i in (1..2) -%}
                      <div
                        class="look__slide look__slide--{{ block.id }}"
                        data-slide="{{ block.id }}"
                        {{ block.shopify_attributes }}
                      >
                        {%- liquid
                          assign product_index = forloop.index | plus: 2
                          assign placeholder = 'product-' | append: product_index
                          capture title
                            cycle 'Nomad X Sunglasses', 'Nomad X Hat'
                          endcapture
                          render 'onboarding-product-grid-item', title: title, placeholder: placeholder, index: forloop.index, animation_delay: forloop.index0
                        -%}
                      </div>
                    {%- endfor -%}
                  {%- endif -%}
                </slider-component>
              </div>
            </div>
          {%- when 'before-and-after' -%}

            {%- assign color_scheme = 'color-' | append: block.settings.color_scheme -%}

            <div
              class="brick__block brick__block--compare {% if section.settings.block_radius == true %}block-radius{% endif %} {{ desktop_height }} {{ mobile_height }} {{ color_scheme }}"
              {% if style != blank %}
                style="{{ style }}"
              {% endif %}
              {{ block.shopify_attributes }}
            >
              <div class="brick__block__compare">
                {%- render 'compare-images',
                  image_1: block.settings.image_1,
                  image_2: block.settings.image_2,
                  image_position: block.settings.image_position,
                  animation_anchor: animation_anchor,
                  wrapper: section.settings.width
                -%}
              </div>
            </div>
        {%- endcase -%}
      {%- endfor -%}
    </div>
  {%- else -%}
    {%- render 'no-blocks' -%}
  {%- endif -%}
</section>

{% schema %}
{
  "name": "💄 Custom content",
  "max_blocks": 2,
  "settings": [
    {
      "type": "checkbox",
      "id": "borders",
      "label": "Add borders",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "block_radius",
      "label": "Round corners",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "gutters",
      "label": "Enable gutters",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "compact",
      "label": "Compact",
      "default": true
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full-padded",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"}
      ]
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "height",
      "label": "Height",
      "default": "screen-height-one-half",
      "options": [
        {"value": "image-height", "label": "Image height"},
        {"value": "screen-height-full", "label": "Full screen height"},
        {"value": "screen-height-three-quarters", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds", "label": "2/3 of screen"},
        {"value": "screen-height-one-half", "label": "1/2 of screen"},
        {"value": "screen-height-one-third", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero", "label": "750px"},
        {"value": "sixty-fifty-height-hero", "label": "650px"},
        {"value": "five-fifty-height-hero", "label": "550px"},
        {"value": "four-fifty-height-hero", "label": "450px"}
      ]
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Height",
      "default": "screen-height-one-half--mobile",
      "options": [
        {"value": "image-height--mobile", "label": "Image height"},
        {"value": "screen-height-full--mobile", "label": "Full screen height"},
        {"value": "screen-height-three-quarters--mobile", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds--mobile", "label": "2/3 of screen"},
        {"value": "screen-height-one-half--mobile", "label": "1/2 of screen"},
        {"value": "screen-height-one-third--mobile", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero--mobile", "label": "750px"},
        {"value": "sixty-fifty-height-hero--mobile", "label": "650px"},
        {"value": "five-fifty-height-hero--mobile", "label": "550px"},
        {"value": "four-fifty-height-hero--mobile", "label": "450px"}
      ]
    },
    {
      "type": "checkbox",
      "id": "reverse_blocks",
      "label": "Reverse block placement",
      "default": false
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Media",
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme_1",
          "label": "Color scheme"
        },
        {
          "type": "header",
          "content": "Video"
        },
        {
          "type": "checkbox",
          "id": "enable_video",
          "label": "Enable video",
          "default": true
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "header",
          "content": "Image",
          "info": "If video is added, this is used as the loading state of the video."
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "select",
          "id": "mobile_height",
          "label": "Mobile height",
          "default": "screen-height-one-half--mobile",
          "options": [
            {"value": "image-height--mobile", "label": "Image height"},
            {"value": "screen-height-full--mobile", "label": "Full screen height"},
            {"value": "screen-height-three-quarters--mobile", "label": "3/4 of screen"},
            {"value": "screen-height-two-thirds--mobile", "label": "2/3 of screen"},
            {"value": "screen-height-one-half--mobile", "label": "1/2 of screen"},
            {"value": "screen-height-one-third--mobile", "label": "1/3 of screen"},
            {"value": "seven-fifty-height-hero--mobile", "label": "750px"},
            {"value": "sixty-fifty-height-hero--mobile", "label": "650px"},
            {"value": "five-fifty-height-hero--mobile", "label": "550px"},
            {"value": "four-fifty-height-hero--mobile", "label": "450px"}
          ]
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "select",
          "id": "spacer",
          "label": "Split content",
          "options": [
            {"value": "", "label": "No Spacer"},
            {"value": "top", "label": "Top"},
            {"value": "subheading", "label": "Subheading"},
            {"value": "heading", "label": "Heading"},
            {"value": "text", "label": "Text"},
            {"value": "button", "label": "Button"},
            {"value": "bottom", "label": "Bottom"},
          ],
          "info": "Split this block in two at this point, and add space between the elements."
        },
        { 
          "id": "subheading",
          "label": "Subheading",
          "type": "text"
        },
        { 
          "id": "subheading_font_size",
          "label": "Subheading size",
          "type": "select",
          "default": "subheading-eyebrow",
          "options": [
            {"value": "body-x-small", "label": "Extra small", "group": "Body"},
            {"value": "body-small", "label": "Small", "group": "Body"},
            {"value": "body-medium", "label": "Medium", "group": "Body"},
            {"value": "body-large", "label": "Large", "group": "Body"},
            {"value": "body-x-large", "label": "Extra large", "group": "Body"},
            {"value": "text-badge", "label": "Badge", "group": "Misc."},
            {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."},
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"}
          ]
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-large",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "range",
          "id": "text_max_width",
          "min": 50,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Text width",
          "default": 100
        },
        {
          "type": "select",
          "id": "flex_align",
          "label": "Block alignment",
          "default": "align--middle-center",
          "options": [
            {"value": "align--top-left", "label": "Top left"},
            {"value": "align--top-center", "label": "Top center"},
            {"value": "align--top-right", "label": "Top right"},
            {"value": "align--middle-left", "label": "Middle left"},
            {"value": "align--middle-center", "label": "Absolute center"},
            {"value": "align--middle-right", "label": "Middle right"},
            {"value": "align--bottom-left", "label": "Bottom left"},
            {"value": "align--bottom-center", "label": "Bottom center"},
            {"value": "align--bottom-right", "label": "Bottom right"}
          ]
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "Text",
          "info": "Leave blank to link entire image"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "button_full",
          "label": "Full-width Button",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        { 
          "content": "Overlay Content",
          "type": "header"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "Overlay opacity",
          "info": "Increase contrast for legible text.",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "default": 0
        },
        {
          "type": "color",
          "id": "overlay_color",
          "label": "Overlay color"
        },
        {
          "type": "checkbox",
          "id": "show_overlay_text",
          "label": "Overlay behind text only",
          "default": false
        },
        { 
          "id": "overlay_subheading",
          "label": "Subheading",
          "type": "text"
        },
        { 
          "id": "overlay_subheading_font_size",
          "label": "Subheading size",
          "type": "select",
          "default": "subheading-eyebrow",
          "options": [
            {"value": "body-x-small", "label": "Extra small", "group": "Body"},
            {"value": "body-small", "label": "Small", "group": "Body"},
            {"value": "body-medium", "label": "Medium", "group": "Body"},
            {"value": "body-large", "label": "Large", "group": "Body"},
            {"value": "body-x-large", "label": "Extra large", "group": "Body"},
            {"value": "text-badge", "label": "Badge", "group": "Misc."},
            {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."},
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"}
          ]
        },
        { 
          "id": "overlay_title",
          "label": "Title",
          "type": "text"
        },
        { 
          "id": "overlay_title_font_size",
          "label": "Title size",
          "type": "select",
          "default": "heading-small",
          "options": [
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
            {"value": "heading-mini", "label": "Mini", "group": "Headings"},
            {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
            {"value": "heading-small", "label": "Small", "group": "Headings"},
            {"value": "heading-medium", "label": "Medium", "group": "Headings"},
            {"value": "heading-large", "label": "Large", "group": "Headings"},
            {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
          ]
        },
        {
          "id": "overlay_content",
          "label": "Content",
          "type": "richtext"
        },
        { 
          "id": "overlay_product",
          "label": "Product",
          "type": "product"
        },
        { 
          "id": "overlay_button_text",
          "label": "Button text",
          "type": "text"
        },
        { 
          "id": "overlay_button_url",
          "label": "Button link",
          "type": "url"
        },
        { 
          "id": "overlay_button_type",
          "label": "Button color",
          "type": "select",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "id": "overlay_button_size",
          "label": "Button size",
          "type": "select",
          "default": "btn--large",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "id": "overlay_button_style",
          "label": "Button style",
          "type": "select",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "id": "overlay_text_align",
          "label": "Text alignment",
          "type": "select",
          "default": "center",
          "options": [
            {"label": "Left", "value": "left"},
            {"label": "Center", "value": "center"},
            {"label": "Right", "value": "right"}
          ]
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "checkbox",
          "id": "show_text_background",
          "label": "Show text background",
          "default": false
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text"
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme_1",
          "label": "Color scheme"
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "select",
          "id": "spacer",
          "label": "Split content",
          "options": [
            {"value": "", "label": "No Spacer"},
            {"value": "top", "label": "Top"},
            {"value": "subheading", "label": "Subheading"},
            {"value": "heading", "label": "Heading"},
            {"value": "text", "label": "Text"},
            {"value": "button", "label": "Button"},
            {"value": "bottom", "label": "Bottom"},
          ],
          "info": "Split this block in two at this point, and add space between the elements."
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Introducing"
        },
        {
          "type": "select",
          "id": "subheading_font_size",
          "label": "Subheading font size",
          "options": [
            {"value": "", "label": "Default"},
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
            {"value": "heading-mini", "label": "Mini", "group": "Headings"},
            {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
            {"value": "heading-small", "label": "Small", "group": "Headings"},
            {"value": "heading-medium", "label": "Medium", "group": "Headings"},
            {"value": "heading-large", "label": "Large", "group": "Headings"},
            {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
            {"value": "body-x-small", "label": "Extra small", "group": "Body"},
            {"value": "body-small", "label": "Small", "group": "Body"},
            {"value": "body-medium", "label": "Medium", "group": "Body"},
            {"value": "body-large", "label": "Large", "group": "Body"},
            {"value": "body-x-large", "label": "Extra large", "group": "Body"},
            {"value": "text-badge", "label": "Badge", "group": "Misc."},
            {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
          ],
          "default": "subheading-eyebrow"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Custom content"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-large",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this section to create unique side-by-side layouts with various content blocks. Pair text with images, newsletter blocks, products, videos, testimonials and more.</p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Text",
          "default": "Learn more"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "button_full",
          "label": "Full-width Button",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_social_links",
          "label": "Show social icons",
          "info": "Edit your social settings and accounts in [Theme settings](/admin/themes/current/editor?context=theme)",
          "default": false
        },
        {
          "type": "range",
          "id": "text_columns",
          "label": "Columns",
          "min": 1,
          "max": 3,
          "step": 1,
          "default": 1,
          "info": "Desktop only"
        },
        {
          "type": "select",
          "id": "align_text",
          "label": "Text alignment",
          "default": "text-left",
          "options": [
            {"value": "text-left", "label": "Left"},
            {"value": "text-center", "label": "Centered"}
          ]
        },
        {
          "type": "header",
          "content": "Background Image"
        },
        {
          "type": "image_picker",
          "id": "background_image",
          "label": "Background Image"
        },
        {
          "type": "range",
          "id": "background_image_opacity",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Opacity",
          "default": 30
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text"
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "limit": 1,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme_1",
          "label": "Color scheme"
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Newsletter"
        },
        {
          "type": "select",
          "id": "subheading_font_size",
          "label": "Subheading font size",
          "options": [
            {"value": "", "label": "Default"},
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
            {"value": "heading-mini", "label": "Mini", "group": "Headings"},
            {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
            {"value": "heading-small", "label": "Small", "group": "Headings"},
            {"value": "heading-medium", "label": "Medium", "group": "Headings"},
            {"value": "heading-large", "label": "Large", "group": "Headings"},
            {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
            {"value": "body-x-small", "label": "Extra small", "group": "Body"},
            {"value": "body-small", "label": "Small", "group": "Body"},
            {"value": "body-medium", "label": "Medium", "group": "Body"},
            {"value": "body-large", "label": "Large", "group": "Body"},
            {"value": "body-x-large", "label": "Extra large", "group": "Body"},
            {"value": "text-badge", "label": "Badge", "group": "Misc."},
            {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
          ],
          "default": "subheading-eyebrow"
        },
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "Sign up"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-large",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Subscribe to get special offers, free giveaways, and once-in-a-lifetime deals.</p>"
        },
        {
          "type": "checkbox",
          "id": "spacer",
          "label": "Add Spacer",
          "default": true,
          "info": "Split this block in two after the content, and add space between the elements."
        },
        {
          "type": "richtext",
          "id": "newsletter_terms_text",
          "label": "Newsletter terms text",
          "default": "<p>By signing up, you agree to receive our email communications. No spam, just radiance redefined!</p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "range",
          "id": "text_columns",
          "label": "Columns",
          "min": 1,
          "max": 3,
          "step": 1,
          "default": 1,
          "info": "Desktop only"
        },
        {
          "type": "select",
          "id": "align_text",
          "label": "Text alignment",
          "default": "text-left",
          "options": [
            {"value": "text-left", "label": "Left"},
            {"value": "text-center", "label": "Centered"}
          ]
        },
        {
          "type": "header",
          "content": "Form"
        },
        {
          "type": "paragraph",
          "content": "Subscribers are under 'Accepts Marketing' in your [customer admin](/admin/customers)."
        },
        {
          "type": "checkbox",
          "id": "show_name",
          "label": "Show name field",
          "default": false
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button text",
          "default": "Join"
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "terms",
          "label": "Show reCAPTCHA terms",
          "default": true,
          "info": "Recommended if your online store preferences have spam protection enabled."
        },
        {
          "type": "header",
          "content": "Button",
          "info": "Applies if \"Show name field\" is enabled."
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "button_full",
          "label": "Full-width Button",
          "default": true
        },
        {
          "type": "header",
          "content": "Social media icons"
        },
        {
          "type": "checkbox",
          "id": "show_social_links",
          "label": "Show social icons",
          "info": "Edit your social settings and accounts in [Theme settings](/admin/themes/current/editor?context=theme)",
          "default": false
        },
        {
          "type": "header",
          "content": "Background Image"
        },
        {
          "type": "image_picker",
          "id": "background_image",
          "label": "Background Image"
        },
        {
          "type": "range",
          "id": "background_image_opacity",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Opacity",
          "default": 30
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text"
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    },
    {
      "type": "review",
      "name": "Testimonial",
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme_1",
          "label": "Color scheme"
        },
        {
          "type": "header",
          "content": "Testimonial"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Customer name",
          "default": "Example Customer"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "@username"
        },
        {
          "type": "select",
          "id": "subheading_font_size",
          "label": "Subheading font size",
          "options": [
            {"value": "", "label": "Default"},
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
            {"value": "heading-mini", "label": "Mini", "group": "Headings"},
            {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
            {"value": "heading-small", "label": "Small", "group": "Headings"},
            {"value": "heading-medium", "label": "Medium", "group": "Headings"},
            {"value": "heading-large", "label": "Large", "group": "Headings"},
            {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
            {"value": "body-x-small", "label": "Extra small", "group": "Body"},
            {"value": "body-small", "label": "Small", "group": "Body"},
            {"value": "body-medium", "label": "Medium", "group": "Body"},
            {"value": "body-large", "label": "Large", "group": "Body"},
            {"value": "body-x-large", "label": "Extra large", "group": "Body"},
            {"value": "text-badge", "label": "Badge", "group": "Misc."},
            {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
          ],
          "default": "subheading-eyebrow"
        },
        {
          "type": "textarea",
          "id": "review",
          "label": "Testimonial",
          "default": "Use this text to showcase a review from one of your customers. A great review is honest and speaks to the concerns of your customers."
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "image_picker",
          "id": "bio_image",
          "label": "Bio image",
          "info": "70 x 70px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "header",
          "content": "Review link",
          "info": "Whole block turns into a link"
        },
        {
          "type": "url",
          "id": "review_url",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Style"
        },
        {
          "type": "checkbox",
          "id": "show_quotation_marks",
          "label": "Show quotation mark",
          "default": true
        },
        {
          "type": "select",
          "id": "align_text",
          "label": "Text alignment",
          "default": "text-left",
          "options": [
            {"value": "text-left", "label": "Left"},
            {"value": "text-center", "label": "Centered"}
          ]
        },
        {
          "type": "header",
          "content": "Background Image"
        },
        {
          "type": "image_picker",
          "id": "background_image",
          "label": "Background Image"
        },
        {
          "type": "range",
          "id": "background_image_opacity",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Opacity",
          "default": 30
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text"
        }
      ]
    },
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme_1",
          "label": "Color scheme"
        },
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Subheading",
          "default": "Product Feature"
        },
        {
          "type": "header",
          "content": "Background Image"
        },
        {
          "type": "image_picker",
          "id": "background_image",
          "label": "Background Image"
        },
        {
          "type": "range",
          "id": "background_image_opacity",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Opacity",
          "default": 30
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text"
        }
      ]
    },
    {
      "type": "video",
      "name": "Video",
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme_1",
          "label": "Color scheme"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "header",
          "content": "Popup",
          "info": "Only one popup option can be used"
        },
        {
          "type": "checkbox",
          "id": "show_video_popup",
          "label": "Enable popup",
          "default": false
        },
        {
          "type": "video",
          "id": "video_popup",
          "label": "MP4/MOV"
        },
        {
          "type": "video_url",
          "id": "video_popup_social",
          "label": "Youtube/Vimeo link",
          "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
          "accept": ["youtube", "vimeo"]
        },
        {
          "type": "header",
          "content": "Image"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "3200 x 1200px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile image",
          "info": "1200 x 1600px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Video"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-large",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Tell your brand's story through images.</p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "select",
          "id": "flex_align",
          "label": "Block alignment",
          "default": "align--middle-center",
          "options": [
            {"value": "align--top-left", "label": "Top left"},
            {"value": "align--top-center", "label": "Top center"},
            {"value": "align--top-right", "label": "Top right"},
            {"value": "align--middle-left", "label": "Middle left"},
            {"value": "align--middle-center", "label": "Absolute center"},
            {"value": "align--middle-right", "label": "Middle right"},
            {"value": "align--bottom-left", "label": "Bottom left"},
            {"value": "align--bottom-center", "label": "Bottom center"},
            {"value": "align--bottom-right", "label": "Bottom right"}
          ]
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "Text",
          "default": "View products",
          "info": "Leave blank to link entire image"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "header",
          "content": "Background Image"
        },
        {
          "type": "image_picker",
          "id": "background_image",
          "label": "Background Image"
        },
        {
          "type": "range",
          "id": "background_image_opacity",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Opacity",
          "default": 30
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "checkbox",
          "id": "show_text_background",
          "label": "Show text background",
          "default": false
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text",
          "default": "#FFFFFF"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "Overlay opacity",
          "info": "Increase contrast for legible text.",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "default": 0
        },
        {
          "type": "color",
          "id": "overlay_color",
          "label": "Overlay color"
        },
        {
          "type": "checkbox",
          "id": "show_overlay_text",
          "label": "Overlay behind text only",
          "default": false
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    },
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme_1",
          "label": "Color scheme"
        },
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Shop the look"
        },
        {
          "type": "header",
          "content": "Background Image"
        },
        {
          "type": "image_picker",
          "id": "background_image",
          "label": "Background Image"
        },
        {
          "type": "range",
          "id": "background_image_opacity",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Opacity",
          "default": 30
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text"
        }
      ]
    },
    {
      "type": "before-and-after",
      "name": "Before and after",
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme_1",
          "label": "Color scheme"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "Image 1",
          "info": "2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "Image 2",
          "info": "2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "💄 Custom content",
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "text"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "footer"]
  }
}
{% endschema %}
