<!-- /sections/section-accordion-group.liquid -->
{%- liquid
  assign section_name = 'AccordionGroup'
  assign section_id = section_name | append: '--' | append: section.id
  assign animation_order = 0
  assign color_scheme = 'color-' | append: section.settings.color_scheme
-%}

{%- style -%}
  #AccordionGroup--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;

    --accordion-columns: {{ section.settings.columns }};
  }
{%- endstyle -%}

<div id="{{ section_id }}" class="accordion-group custom-accordion-group section-padding {{ color_scheme }}">

  
  {% comment %} ----- Header ----- {% endcomment %}

  {%- capture section_header -%}

    {%- if section.settings.title != blank or section.settings.subheading != blank -%}
      <div class="section-header__text">

        {%- if section.settings.eyebrow != blank -%}
          <p class="section-header__eyebrow {{ section.settings.eyebrow_font_size }}">
            {{ section.settings.eyebrow }}
          </p>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          {%- liquid
            assign heading_tag = section.settings.heading_tag
          -%}
          <{{ heading_tag }} class="section-header__title {{ section.settings.title_font_size }}">
            {{ section.settings.title }}
          </{{ heading_tag }}>
        {%- endif -%}

      </div>
    {%- endif -%}

    {%- if section.settings.header_button_text != blank -%}
      <div class="section-header__actions">
        <a href="{{ section.settings.header_button_link | default: "#" }}"
           class="btn {{ section.settings.header_button_style }} {{ section.settings.header_button_size }} {{ section.settings.header_button_type }}"
        >
          <span>{{ section.settings.header_button_text }}</span>
        </a>
      </div>
    {%- endif -%}

  {%- endcapture -%}

  {%- if section_header != blank -%}
    {%- capture section_header -%}
      <div class="section-header-wrapper">
        <div class="section-header section-header--{{ section.settings.header_layout }}">
          {{ section_header }}
        </div>
      </div>
    {%- endcapture -%}
  {%- endif -%}

  
  {% comment %} ----- Content ----- {% endcomment %}

  {%- capture section_content -%}

    {%- if section.blocks.size > 0 -%}
      <div
        class="faq"
        data-section-id="{{ section.id }}"
        data-section-type="faq"
      >
        <collapsible-elements single="true">
          <div class="accordion-group__items">
            {%- for block in section.blocks -%}
              {%- liquid
                assign title = block.settings.title
                assign content = block.settings.content
                assign default_open = block.settings.default_open
              -%}

              {%- if block.type == 'heading' and title != blank -%}
                {%- assign animation_order = animation_order | plus: 1 -%}

                <h3
                  class="accordion__heading {{ block.settings.heading_font_size | default: section.settings.faq_heading_font_size }}"
                  data-aos="hero"
                  data-aos-anchor="{{ animation_anchor }}"
                  data-aos-order="{{ animation_order }}"
                  {{ block.shopify_attributes }}
                >
                  {{ title }}
                </h3>
              {%- endif -%}

              {%- if block.type == 'question' and title != blank and content != blank -%}
                {%- if title != blank -%}
                  {%- assign animation_order = animation_order | plus: 1 -%}
                  <details
                    class="accordion"
                    data-collapsible
                    {% if default_open %}
                      open="true"
                    {% endif %}
                    data-aos="hero"
                    data-aos-anchor="{{ animation_anchor }}"
                    data-aos-order="{{ animation_order }}"
                    {{ block.shopify_attributes }}
                  >
                    <summary
                      class="accordion__title {{ block.settings.heading_font_size | default: section.settings.faq_question_font_size }}"
                      data-collapsible-trigger
                    >
                      {%- if block.settings.show_icon -%}
                        {%- liquid
                          assign icon_size = block.settings.icon_size
                          assign icon_color = block.settings.icon_color
                          assign icon_image = block.settings.icon_image
                          capture icon_style
                            echo 'style="'
                            echo '--icon-size: ' | append: icon_size | append: 'px;'
                            if icon_color != blank and icon_color.alpha != 0.0
                              echo ' --icons: ' | append: icon_color | append: ';'
                            endif
                            echo '"'
                          endcapture
                        -%}

                        <div
                          class="icon__animated icon__animated--{{ block.id }}{% if icon_image != blank %} icon__animated--image{% endif %}"
                          {{ icon_style }}
                        >
                          {%- liquid
                            if icon_image
                              assign icon_width = icon_size
                              assign icon_width_retina = icon_width | times: 2
                              assign icon_sizes = icon_width | append: 'px'
                              assign icon_widths = icon_width | append: ', ' | append: icon_width_retina

                              render 'image', image: icon_image, width: icon_width_retina, sizes: icon_sizes, widths: icon_widths
                            else
                              render 'animated-icon', filename: block.settings.icon_name
                            endif
                          -%}
                        </div>
                      {%- endif -%}

                      {{ title }}

                      {%- render 'icon-plus' -%}
                      {%- render 'icon-minus' -%}
                    </summary>

                    <div
                      class="accordion__body rte"
                      {% if default_open != blank %}
                        style="height: auto;"
                      {% endif %}
                      data-collapsible-body
                    >
                      <div class="accordion__content {{ block.settings.text_font_size | default: section.settings.faq_text_font_size }}" data-collapsible-content>
                        {{ content }}
                      </div>
                    </div>
                  </details>
                {%- endif -%}
              {%- endif -%}
            {%- endfor -%}
          </div>
        </collapsible-elements>
      </div>
    {%- endif -%}
    
  {%- endcapture -%}

  {%- if section_content != blank -%}
    {%- capture section_content -%}
      <div class="section-content">
        {{ section_content }}
      </div>
    {%- endcapture -%}
  {%- endif -%}

  
  {% comment %} ----- DISPLAY ----- {% endcomment %}

  {%- capture section_inner -%}
    {%- if section_header != blank -%}
      {{ section_header }}
    {%- endif -%}
    {%- if section_content != blank -%}
      {{ section_content }}
    {%- endif -%}
  {%- endcapture -%}

  {%- if section_inner -%}
    <div class="{{ section.settings.width }}">
      <div class="accordion-group--{{ section.settings.layout }}">
        {{ section_inner }}
      </div>
    </div>
  {%- endif -%}

</div>

{% schema %}
{
  "name": "💄 Accordion group",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper",
      "options": [
        {"value": "wrapper", "label": "Normal"},
        {"value": "wrapper--narrow", "label": "Narrow"},
        {"value": "wrapper--full", "label": "Full"},
        {"value": "wrapper--full-padded", "label": "Full Padded"}
      ]
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "options": [
        {
          "value": "vertical",
          "label": "Vertical"
        },
        {
          "value": "columns",
          "label": "Content in side column"
        }
      ],
      "default": "columns"
    },
    {
      "type": "header",
      "content": "Header"
    },
    {
      "type": "select",
      "id": "header_layout",
      "label": "Layout",
      "default": "horizontal",
      "options": [
        {"value": "horizontal", "label": "Horizontal"},
        {"value": "vertical", "label": "Vertical"}
      ]
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "select",
      "id": "title_font_size",
      "label": "Title font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": ""
    },
    {
      "type": "text",
      "id": "eyebrow",
      "label": "Eyebrow text"
    },
    {
      "type": "select",
      "id": "eyebrow_font_size",
      "label": "Eyebrow font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": "text-badge-lg"
    },
    {
      "type": "url",
      "id": "header_button_link",
      "label": "Button link"
    },
    {
      "type": "text",
      "id": "header_button_text",
      "label": "Button text"
    },
    {
      "type": "select",
      "id": "header_button_type",
      "label": "Button color",
      "default": "btn--primary",
      "options": [
        {"value": "btn--black", "label": "Black"},
        {"value": "btn--white", "label": "White"},
        {"value": "btn--primary", "label": "Primary"},
        {"value": "btn--secondary", "label": "Secondary"}
      ]
    },
    {
      "type": "select",
      "id": "header_button_size",
      "label": "Button size",
      "default": "",
      "options": [
        {"label": "Small", "value": "btn--small"},
        {"label": "Medium", "value": ""},
        {"label": "Large", "value": "btn--large"}
      ]
    },
    {
      "type": "select",
      "id": "header_button_style",
      "label": "Button style",
      "default": "btn--solid",
      "options": [
        {"label": "Solid", "value": "btn--solid"},
        {"label": "Outline", "value": "btn--outline"},
        {"label": "Text", "value": "btn--text"}
      ]
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "h2",
      "options": [
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    },
    {
      "type": "header",
      "content": "Accordion Items Styling"
    },

    {
      "type": "select",
      "id": "faq_heading_font_size",
      "label": "Heading size",
      "default": "text-badge-lg",
      "options": [
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ]
    },
    {
      "type": "select",
      "id": "faq_question_font_size",
      "label": "Question font size",
      "default": "body-large",
      "options": [
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ]
    },
    {
      "type": "select",
      "id": "faq_text_font_size",
      "label": "Answer font size",
      "default": "body-medium",
      "options": [
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ]
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 50
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    }
  ],
  "blocks": [
    {
      "name": "Heading",
      "type": "heading",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "FAQ"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "",
          "options": [
            {"value": "", "label": "Default" },
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
            {"value": "body-x-small", "label": "Extra small", "group": "Body"},
            {"value": "body-small", "label": "Small", "group": "Body"},
            {"value": "body-medium", "label": "Medium", "group": "Body"},
            {"value": "body-large", "label": "Large", "group": "Body"},
            {"value": "body-x-large", "label": "Extra large", "group": "Body"},
            {"value": "text-badge", "label": "Badge", "group": "Misc."},
            {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
          ]
        }
      ]
    },
    {
      "name": "Question",
      "type": "question",
      "settings": [
        {
          "type": "checkbox",
          "id": "default_open",
          "label": "Open by default",
          "default": false
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Question",
          "info": "Block will not be visible if left empty.",
          "default": "Frequently asked question"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "",
          "options": [
            {"value": "", "label": "Default" },
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
            {"value": "body-x-small", "label": "Extra small", "group": "Body"},
            {"value": "body-small", "label": "Small", "group": "Body"},
            {"value": "body-medium", "label": "Medium", "group": "Body"},
            {"value": "body-large", "label": "Large", "group": "Body"},
            {"value": "body-x-large", "label": "Extra large", "group": "Body"},
            {"value": "text-badge", "label": "Badge", "group": "Misc."},
            {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
          ]
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Answer",
          "default": "<p>Share details about your shipping policies, item returns, or customer service.<\/p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text font size",
          "default": "",
          "options": [
            {"value": "", "label": "Default" },
            {"value": "body-x-small", "label": "Extra small", "group": "Body"},
            {"value": "body-small", "label": "Small", "group": "Body"},
            {"value": "body-medium", "label": "Medium", "group": "Body"},
            {"value": "body-large", "label": "Large", "group": "Body"},
            {"value": "body-x-large", "label": "Extra large", "group": "Body"},
            {"value": "text-badge", "label": "Badge", "group": "Misc."},
            {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
          ]
        },
        {
          "type": "select",
          "id": "list_style",
          "label": "List style",
          "options": [
            {"value": "", "label": "Default"},
            {"value": "ticks", "label": "Ticks"}
          ]
        },
        {
          "type": "header",
          "content": "Icon"
        },
        {
          "type": "checkbox",
          "id": "show_icon",
          "label": "Show icon",
          "default": false
        },
        {
          "type": "select",
          "id": "icon_name",
          "label": "Icon",
          "default": "icon-award",
          "options": [
            {"label": "Award", "value": "icon-award"},
            {"label": "Box", "value": "icon-box"},
            {"label": "Chat", "value": "icon-chat"},
            {"label": "Check", "value": "icon-check"},
            {"label": "Check circle", "value": "icon-check-circle"},
            {"label": "Cloud", "value": "icon-cloud"},
            {"label": "Diameter", "value": "icon-diameter"},
            {"label": "Discount", "value": "icon-discount"},
            {"label": "Donation", "value": "icon-donation"},
            {"label": "Droplet", "value": "icon-droplet"},
            {"label": "Info", "value": "icon-info-empty"},
            {"label": "Email", "value": "icon-email"},
            {"label": "Fast shipment", "value": "icon-fast-shipment"},
            {"label": "Flare", "value": "icon-flare"},
            {"label": "Flower", "value": "icon-flower"},
            {"label": "Gift", "value": "icon-gift"},
            {"label": "Green shipment", "value": "icon-green-shipment"},
            {"label": "Heart", "value": "icon-heart"},
            {"label": "Leaf", "value": "icon-leaf"},
            {"label": "Lightning", "value": "icon-lightning"},
            {"label": "Location", "value": "icon-location"},
            {"label": "Mail", "value": "icon-mail"},
            {"label": "Notes", "value": "icon-notes"},
            {"label": "Pants", "value": "icon-pants"},
            {"label": "Peace", "value": "icon-peace"},
            {"label": "Pin", "value": "icon-pin"},
            {"label": "Planet", "value": "icon-planet"},
            {"label": "Phone", "value": "icon-phone"},
            {"label": "Recycle", "value": "icon-recycle"},
            {"label": "Ruler", "value": "icon-ruler"},
            {"label": "Shield", "value": "icon-shield"},
            {"label": "Smile", "value": "icon-smile"},
            {"label": "Star", "value": "icon-star"},
            {"label": "Tree", "value": "icon-tree"},
            {"label": "Trophy", "value": "icon-trophy"},
            {"label": "Truck", "value": "icon-truck"},
            {"label": "Vegan", "value": "icon-vegan"},
            {"label": "Wash", "value": "icon-wash"},
            {"label": "Washing machine", "value": "icon-washing-machine"}
          ]
        },
        {
          "type": "image_picker",
          "id": "icon_image",
          "label": "Alternative icon/image",
          "info": "160 x 160px .svg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "range",
          "id": "icon_size",
          "label": "Size",
          "unit": "px",
          "min": 20,
          "max": 80,
          "step": 5,
          "default": 20
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "color",
          "id": "icon_color",
          "label": "Icon"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "💄 Accordion group",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "question"
        },
        {
          "type": "question"
        },
        {
          "type": "question"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "custom.pre_footer", "footer"]
  }
}
{% endschema %}
