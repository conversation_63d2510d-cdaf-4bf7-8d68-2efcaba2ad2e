<!-- /sections/customer-order.liquid -->

{%- liquid
  assign order_line_items_subtotal_price = order.line_items_subtotal_price | money
  assign order_total_price = order.total_price | money_with_currency
-%}

<section class="account">
  <div class="wrapper">
    <div class="grid grid--account">
      {%- render 'account-menu' -%}
      <div class="account-main">
        <h2 class="page__heading">{{ 'customer.order.title' | t: name: order.name }}</h2>

        {%- if order.cancelled -%}
        <div id="OrderCancelled" class="form-errors">
          {%- assign order_cancelled_at = order.cancelled_at | date: format: 'full_date_and_time' -%}
          <h4 class="h3">{{ 'customer.order.cancelled' | t: date: order_cancelled_at }}</h4>
          <p>{{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason }}</p>
        </div>
        {%- endif -%}

        <div id="OrderAddress" class="order-address grid-2">
          <div id="OrderInfo" class="order-info">
            {%- assign order_created_at = order.created_at | date: format: 'full_date_and_time' -%}
            <p>{{ 'customer.order.date' | t: date: order_created_at }}</p>
            <p class="order-status"><span class="note">{{ 'customer.order.payment_status' | t }}:</span> <span class="status">{{ order.financial_status_label }}</span></p>
            <p class="order-status"><span class="note">{{ 'customer.order.fulfillment_status' | t }}:</span> <span class="status">{{ order.fulfillment_status_label }}</span></p>
          </div>
          <div id="OrderPayment" class="col">
            <p class="h5">{{ 'customer.order.billing_address' | t }}</p>
            <div class="address note">
              {{ order.billing_address | format_address }}
            </div>
          </div>
          {%- if order.shipping_address -%}
          <div id="OrderShipping" class="col">
            <p class="h5">{{ 'customer.order.shipping_address' | t }}</p>
            <div class="address note">
              {{ order.shipping_address | format_address }}
            </div>
          </div>
          {%- endif -%}
        </div>

        <table id="OrderDetails" class="desktop">
          <thead>
            <tr>
              <th>{{ 'customer.order.details.product' | t }}</th>
              <th>{{ 'customer.order.details.sku' | t }}</th>
              <th>{{ 'customer.order.details.price' | t }}</th>
              <th class="center">{{ 'customer.order.details.quantity' | t }}</th>
              <th class="total">{{ 'customer.order.details.total' | t }}</th>
            </tr>
          </thead>
          <tbody>
            {% for line_item in order.line_items %}
              {%- liquid
                assign unitPrice = false
                assign line_item_original_price = line_item.original_price | money
                assign line_item_final_price = line_item.final_price | money
                assign line_item_unit_price = line_item.unit_price | money
              -%}
              {%- if line_item.unit_price_measurement -%}
                {%- assign unitPrice = true -%}
                {%- capture unit_price_separator -%}
                  <span aria-hidden="true">/</span><span class="visually-hidden">{{ 'general.accessibility.unit_price_separator' | t }}&nbsp;</span>
                {%- endcapture -%}
                {%- capture unit_price_base_unit -%}
                  {%- if line_item.unit_price_measurement.reference_value != 1 -%}
                    {{- line_item.unit_price_measurement.reference_value -}}
                  {%- endif -%}
                  {{ line_item.unit_price_measurement.reference_unit }}
                {%- endcapture -%}
              {%- endif -%}
              {%- assign discounted = false -%}
              {%- if line_item.original_price > line_item.final_price -%}
                {%- assign discounted = true -%}
              {%- endif -%}
              <tr id="{{ line_item.id }}" class="{% cycle 'odd', 'even' %}">
                <td class="line-item-product">
                  {{ line_item.title | link_to: line_item.product.url }}
                  {%- unless line_item.selling_plan_allocation == nil -%}
                    <p class="order__item__subscription">
                      {{ line_item.selling_plan_allocation.selling_plan.name }}
                    </p>
                  {%- endunless -%}
                  {%- if discounted -%}
                    {%- for discount in line_item.line_level_discount_allocations -%}
                      <div class="order__item__savings">
                        <span class="item__total__savings">
                          <span class="cart__icon--tags">
                            {%- render 'icon-tags' -%}
                          </span>
                          {{ discount.amount | money_without_trailing_zeros }}
                          {{ 'customer.order.details.saved_with' | t }}
                          {{ discount.discount_application.title }}
                        </span>
                      </div>
                    {%- endfor -%}
                  {%- endif -%}

                  {%- assign property_size = line_item.properties | size -%}
                    {%- if property_size > 0 -%}
                      {%- for p in line_item.properties -%}
                        {%- assign property_first_char = p.first | slice: 0 -%}
                        {%- if p.last != blank and property_first_char != '_' -%}
                          <p class="order-product__property">
                            <span>{{ p.first }}: </span>
                            <span>
                              {%- if p.last contains '/uploads/' -%}
                                <a href="{{ p.last }}">{{ p.last | split: '/' | last }}</a>
                              {%- else -%}
                                {{ p.last }}
                              {%- endif -%}
                            </span>
                          </p>
                        {%- endif -%}
                      {%- endfor -%}
                    {%- endif -%}

                  {%- if line_item.fulfillment -%}
                  <div class="note">
                    {%- assign fulfilled_at = line_item.fulfillment.created_at | date: format: 'full_date' -%}
                    {{ 'customer.order.details.fulfilled_at' | t: date: fulfilled_at }}
                    {%- if line_item.fulfillment.tracking_number -%}
                    <a href="{{ line_item.fulfillment.tracking_url }}">{{ line_item.fulfillment.tracking_company }} #{{ line_item.fulfillment.tracking_number }}</a>
                    {%- endif -%}
                  </div>
                  {%- endif -%}
                </td>
                <td class="sku note">{{ line_item.sku }}</td>
                <td class="money">
                  {%- if discounted -%}
                    <p class="order__item--original">{{ line_item_original_price }}</p>
                  {%- endif -%}
                  {%- if line_item.final_price == 0 -%}
                    {{ 'general.money.free' | t }}
                  {%- else -%}
                    {{ line_item_final_price }}
                  {%- endif -%}
                  {%- if unitPrice -%}
                    <p class="body-x-small">{{ line_item_unit_price }}{{ unit_price_separator }}{{ unit_price_base_unit }}</p>
                  {%- endif -%}
                </td>
                <td class="quantity center">{{ line_item.quantity }}</td>
                <td class="total money">
                  {%- if discounted -%}
                    <p class="order__item--original">{{ line_item_original_price }}</p>
                  {%- endif -%}
                  {%- if line_item.final_line_price == 0 -%}
                    {{ 'general.money.free' | t }}
                  {%- else -%}
                    {{ line_item_final_price }}
                  {%- endif -%}
                </td>
              </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr class="order_summary note">
              <td class="label" colspan="4">{{ 'customer.order.details.subtotal' | t }}:</td>
              <td class="total money">
                {%- if order.line_items_subtotal_price == 0 -%}
                  {{ 'general.money.free' | t }}
                {%- else -%}
                  {{ order_line_items_subtotal_price }}
                {%- endif -%}
              </td>
            </tr>
            {%- for discount in order.cart_level_discount_applications -%}
              <tr class="order_summary discount">
                <td class="label" colspan="4">{{ 'customer.order.discount' | t }}: <span class="body-x-small caps highlight">{{ discount.title }}</span></td>
                <td class="total money">-{{ discount.total_allocated_amount | money }}</td>
              </tr>
            {%- endfor -%}
            {%- for shipping_method in order.shipping_methods -%}
              <tr class="order_summary note">
                <td class="label" colspan="4">{{ 'customer.order.shipping' | t }} ({{ shipping_method.title }}):</td>
                <td class="total money">
                  {%- if shipping_method.price == 0 -%}
                    {{ 'general.money.free' | t }}
                  {%- else -%}
                    {{ shipping_method.price | money }}
                  {%- endif -%}
                </td>
              </tr>
            {%- endfor -%}

            {%- for tax_line in order.tax_lines -%}
              <tr class="order_summary note">
                <td class="label" colspan="4">{{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%):</td>
                <td class="total money">
                  {%- if tax_line.price == 0 -%}
                    {{ 'general.money.free' | t }}
                  {%- else -%}
                    {{ tax_line.price | money }}
                  {%- endif -%}
                </td>
              </tr>
            {%- endfor -%}

            <tr class="order_summary order_total">
              <td class="label" colspan="4">{{ 'customer.order.details.total' | t }}:</td>
              <td class="total money">
                {%- if order.total_price == 0 -%}{{ 'general.money.free' | t }}{%- else -%}{{ order_total_price }}{%- endif -%}
              </td>
            </tr>
          </tfoot>
        </table>

        <section class="mobile">
          <div class="hr"></div>
          <table id="OrderDetailsM" class="order-details-m">
            <tbody>
              {%- for line_item in order.line_items -%}
                {%- liquid
                    assign line_item_final_line_price = line_item.final_line_price | money
                    assign line_item_final_price = line_item.final_price | money
                    assign line_item_unit_price = line_item.unit_price | money
                -%}
                <tr>
                  <td>
                    <div class="order-image">
                      {%- render 'image' image: line_item.product.featured_media.preview_image, width: 180, widths: '90, 180, 270, 360', sizes: '90px' -%}
                    </div>
                  </td>
                  <td>
                    <div>
                      <b>{{ line_item.title | link_to: line_item.product.url }}</b><br>
                      {{ 'customer.order.details.sku' | t }}: {{ line_item.sku }}<br>
                      {{ 'customer.order.details.quantity' | t }}: {{ line_item.quantity }}<br>
                      {{ 'customer.order.details.price' | t }}: {{ line_item_final_price }}
                      {%- if unitPrice -%}
                        <p class="body-x-small">{{ line_item_unit_price }}{{ unit_price_separator }}{{ unit_price_base_unit }}</p>
                      {%- endif -%}
                      {% if line_item.quantity > 1 %}
                        <br>{{ 'customer.order.details.total' | t }}: {% if line_item.final_line_price == 0 %}{{ 'general.money.free' | t }}{% else %}{{ line_item_final_line_price }}{% endif %}
                        {% if line_item.original_price > line_item.final_price %}
                          {%- for discount in line_item.line_level_discount_allocations -%}
                            <div>
                              <span class="item__total__savings">
                                <span class="cart__icon--tags">
                                  {%- render 'icon-tags' -%}
                                </span>
                                {{ discount.amount | money_without_trailing_zeros }}
                                {{ 'customer.order.details.saved_with' | t }}
                                {{ discount.discount_application.title }}
                              </span>
                            </div>
                          {%- endfor -%}
                        {% endif %}
                      {% endif %}
                    </div>

                    {%- assign property_size = line_item.properties | size -%}
                    {%- if property_size > 0 -%}
                      {%- for p in line_item.properties -%}
                        {%- assign property_first_char = p.first | slice: 0 -%}
                        {%- if p.last != blank and property_first_char != '_' -%}
                          <p class="order-product__property">
                            <span>{{ p.first }}: </span>
                            <span>
                              {%- if p.last contains '/uploads/' -%}
                                <a href="{{ p.last }}">{{ p.last | split: '/' | last }}</a>
                              {%- else -%}
                                {{ p.last }}
                              {%- endif -%}
                            </span>
                          </p>
                        {%- endif -%}
                      {%- endfor -%}
                    {%- endif -%}

                    {% if line_item.fulfillment %}
                    <p class="note">
                      Fulfilled {{ line_item.fulfillment.created_at | date: "%b %d" }}
                      {% if line_item.fulfillment.tracking_number %}
                      <a href="{{ line_item.fulfillment.tracking_url }}">{{ line_item.fulfillment.tracking_company }} #{{ line_item.fulfillment.tracking_number }}</a>
                      {% endif %}
                    </p>
                    {% endif %}
                  </td>
                </tr>
              {%- endfor -%}
            </tbody>
          </table>

          <p><b>{{ 'customer.order.details.subtotal' | t }}:</b> {% if order.line_items_subtotal_price == 0 %}{{ 'general.money.free' | t }}{% else %}{{ order_line_items_subtotal_price }}{% endif %}</p>

          {%- for discount in order.cart_level_discount_applications -%}
            <p>
              <b>{{ 'customer.order.discount' | t }}: <span class="body-x-small caps highlight">{{ discount.title }}</span></b>
              <span class="money">-{{ discount.total_allocated_amount | money }}</span>
            </p>
          {%- endfor -%}

          {%- for shipping_method in order.shipping_methods -%}
            <p><b>{{ 'customer.order.shipping' | t }} ({{ shipping_method.title }}):</b> {% if shipping_method.price == 0 %}{{ 'general.money.free' | t }}{% else %}{{ shipping_method.price | money }}{% endif %}</p>
          {%- endfor -%}

          {%- for tax_line in order.tax_lines -%}
            <p><b>{{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%):</b> {% if tax_line.price == 0 %}{{ 'general.money.free' | t }}{% else %}{{ tax_line.price | money }}{% endif %}</p>
          {%- endfor -%}

          <p class="body-large"><b>{{ 'customer.order.details.total' | t }}:</b> {% if order.total_price == 0 %}{{ 'general.money.free' | t }}{% else %}{{ order_total_price }}{% endif %}</p>
        </section>
      </div>
    </div>
  </div>
</section>

{% schema %}
  {
    "name": "Order",
    "blocks": [
      {
        "type": "@app"
      },
      {
        "type": "account",
        "name": "Account links"
      },
      {
        "type": "email",
        "name": "Email link",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Email title",
            "default": "Support"
          },
          {
            "type": "text",
            "id": "link",
            "label": "Link",
            "info": "Leave blank to use the store email address"
          }
        ]
      },
      {
        "type": "link",
        "name": "Link",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Link title",
            "default": "Home"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Link",
            "default": "/"
          }
        ]
      }
    ]
  }
{% endschema %}
