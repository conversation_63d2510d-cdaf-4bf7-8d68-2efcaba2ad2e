{%- liquid
  assign section_name = 'GlowGallery'
  assign section_id = section_name | append: '--' | append: section.id
  assign animation_anchor = '#' | append: section_id
  assign color_scheme = 'color-' | append: section.settings.color_scheme

  assign desktop_height = section.settings.height
  assign mobile_height = section.settings.mobile_height

  assign gallery_object = section.settings.gallery_metaobject

-%}

{%- capture style -%}
  --PT: {{ section.settings.padding_top }}px;
  --PB: {{ section.settings.padding_bottom }}px;
{%- endcapture -%}

<section
  id="{{ section_id }}"
  data-section-id="{{ section.id }}"
  data-section-type="tabbed-gallery"
  class="
    bespoke-tabbed-gallery 
    {{ color_scheme }}
    {% unless section.settings.width == "wrapper--full" %}section-padding{% endunless %}"
  style="{{ style }}">
  
  {% comment %} ----- Header ----- {% endcomment %}

  {%- capture section_header -%}

    {%- if section.settings.title != blank or section.settings.subheading != blank -%}
      <div class="section-header__text">

        {%- if section.settings.eyebrow != blank -%}
          <p class="section-header__eyebrow {{ section.settings.eyebrow_font_size }}">
            {{ section.settings.eyebrow }}
          </p>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          {%- liquid
            assign heading_tag = section.settings.heading_tag
          -%}
          <{{ heading_tag }} class="section-header__title {{ section.settings.title_font_size }}">
            {{ section.settings.title }}
          </{{ heading_tag }}>
        {%- endif -%}

      </div>
    {%- endif -%}

    {%- if section.settings.header_button_text != blank -%}
      <div class="section-header__actions">
        <a href="{{ section.settings.header_button_link | default: "#" }}"
           class="btn {{ section.settings.header_button_style }} {{ section.settings.header_button_size }} {{ section.settings.header_button_type }}"
        >
          <span>{{ section.settings.header_button_text }}</span>
        </a>
      </div>
    {%- endif -%}

  {%- endcapture -%}

  {%- if section_header != blank -%}
    {%- capture section_header -%}
      <div class="section-header-wrapper">
        <div class="section-header section-header--{{ section.settings.header_layout }}">
          {{ section_header }}
        </div>
      </div>
    {%- endcapture -%}
  {%- endif -%}


  {% comment %} ----- Content ----- {% endcomment %}

  {%- assign gallery_object = section.settings.gallery_metaobject -%}

  {% comment %} Capture Tabs {% endcomment %}

  {%- capture section_content -%}

    {% comment %} 
    ----------------------------------------
    Print Tab Navigation Items and Tab Content
    ----------------------------------------
    {% endcomment %}

    {% comment %} Tab Navigation {% endcomment %}

    {%- capture tabs_navigation -%}

      {%- assign show_tabs = false -%}
      {%- assign active_tabs = 0 -%}
      
      {%- for tab_number in (1..3) -%}

        {%- capture setting_heading -%}heading_{{ tab_number }}{%- endcapture -%}
        {%- assign tab_title = gallery_object[setting_heading] -%}
        
        {%- if tab_title != blank -%}
          {%- assign active_tabs = active_tabs | plus: 1 -%}
        {%- endif -%}
        
      {% endfor %}

      {%- if active_tabs > 1 -%}
        {%- assign show_tabs = true -%}
      {%- endif -%}

      {%- if show_tabs == true -%}
        {%- for tab_number in (1..3) -%}
  
          {%- capture setting_heading -%}heading_{{ tab_number }}{%- endcapture -%}
          {%- assign tab_title = gallery_object[setting_heading] -%}
          
          {%- assign first_tab = forloop.first -%}
  
          {%- assign tab_id = section_id | append: '--tab-' | append: tab_number -%}
  
          {%- render 'tab-navigation-item',
            id: tab_id,
            tab_id: tab_number,
            animation_order: tab_number,
            animation_anchor: section_id,
            title: tab_title,
            active: first_tab
          -%}
  
        {% endfor %}
      {%- endif -%}

    {%- endcapture -%}

    
    {% comment %} Tab Content {% endcomment %}

    {%- for tab_number in (1..3) -%}

      {%- assign tab_number = tab_number | plus: 1 -%}
      {%- assign first_tab = forloop.first -%}

      {%- capture setting_heading -%}heading_{{ tab_number }}{%- endcapture -%}
      {%- capture setting_files -%}files_for_heading_{{ tab_number }}{%- endcapture -%}

      {%- assign gallery_files = gallery_object[setting_files] -%}
      {%- assign gallery_images = gallery_files.value -%}

      {%- capture tab_content -%}

        {%- for file in gallery_images -%}

          {%- capture classes -%}grid-item {{ desktop_height }} {{ mobile_height }}{%- endcapture -%}
          {%- render 'tabbed-gallery-image', image: file, classes: classes, desktop_height: desktop_height, mobile_height: mobile_height -%}  
        {%- endfor -%}

      {%- endcapture -%}

      {%- capture tab_content -%}
        {%- if tab_content != blank -%}
          {%- capture classes -%}
            grid grid--slider grid--mobile-slider
          {%- endcapture -%}
          <div class="grid-container">
            <div class="grid__items-holder">
              <div class="grid-outer collection-list-outer">
                <grid-slider align-arrows>
                  <div class="{{ classes }}" data-grid-slider>
                    {{ tab_content }}
                  </div>
                </grid-slider>
              </div>
            </div>
          </div>
        {%- endif -%}
      {%- endcapture -%}
      
      {%- capture tab_content -%}
        {%- render 'tab-content',
          tab_id: tab_number,
          content: tab_content,
          active: first_tab
        -%}
      {%- endcapture -%}

      {%- capture tabs_content -%}
        {{ tabs_content }}
        {%- if tab_content != blank -%}
          {{ tab_content }}
        {%- endif -%}
      {%- endcapture -%}

    {%- endfor -%}

    {% comment %} DISPLAY {% endcomment %}

    {%- capture tabs_navigation -%}
      {%- if tabs_navigation != blank -%}
        <ul class="tabs" data-scrollbar data-scrollbar-slider>
          {{ tabs_navigation }}
        </ul>
      {%- endif -%}
    {%- endcapture -%}

    <tabs-component class="tabs-collections">
      <div class="{{ section.settings.width }}">
        {{ tabs_navigation }}
      </div>
      {{ tabs_content }}
    </tabs-component>

  {%- endcapture -%}

  {%- if section_content != blank -%}
    {%- capture section_content -%}
      <div class="section-content">
        {{ section_content }}
      </div>
    {%- endcapture -%}
  {%- endif -%}

  
  {% comment %} ----- DISPLAY ----- {% endcomment %}

  {%- capture section_inner -%}
    {%- if section_header != blank -%}
      <div class="{{ section.settings.width }}">
        {{ section_header }}
      </div>
    {%- endif -%}
    {%- if section_content != blank -%}
      {{ section_content }}
    {%- endif -%}
  {%- endcapture -%}

  {{ section_inner }}

</section>

{% schema %}
{
  "name": "💄 Glow Gallery",
  "settings": [
    {
      "type": "metaobject",
      "metaobject_type": "glow_your_own_way_gallery",
      "id": "gallery_metaobject",
      "label": "Gallery Metaobject"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full-padded",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"}
      ]
    },
    {
      "type": "select",
      "id": "height",
      "label": "Image Height",
      "default": "",
      "options": [
        {"value": "", "label": "Image height"},
        {"value": "750", "label": "750px"},
        {"value": "650", "label": "650px"},
        {"value": "550", "label": "550px"},
        {"value": "450", "label": "450px"}
      ]
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Image Height (Mobile)",
      "default": "",
      "options": [
        {"value": "", "label": "Image height"},
        {"value": "750", "label": "750px"},
        {"value": "650", "label": "650px"},
        {"value": "550", "label": "550px"},
        {"value": "450", "label": "450px"}
      ]
    },
    {
      "type": "header",
      "content": "Header"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "select",
      "id": "title_font_size",
      "label": "Title font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": ""
    },
    {
      "type": "text",
      "id": "eyebrow",
      "label": "Eyebrow text"
    },
    {
      "type": "select",
      "id": "eyebrow_font_size",
      "label": "Eyebrow font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": "text-badge-lg"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "select",
      "id": "button_type",
      "label": "Color",
      "default": "btn--primary",
      "options": [
        {"value": "btn--black", "label": "Black"},
        {"value": "btn--white", "label": "White"},
        {"value": "btn--primary", "label": "Primary"},
        {"value": "btn--secondary", "label": "Secondary"}
      ]
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Size",
      "default": "",
      "options": [
        {"label": "Small", "value": "btn--small"},
        {"label": "Medium", "value": ""},
        {"label": "Large", "value": "btn--large"}
      ]
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Style",
      "default": "btn--solid",
      "options": [
        {"label": "Solid", "value": "btn--solid"},
        {"label": "Outline", "value": "btn--outline"},
        {"label": "Text", "value": "btn--text"}
      ]
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "h2",
      "options": [
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    },
    {
      "type": "select",
      "id": "header_layout",
      "label": "Layout",
      "default": "horizontal",
      "options": [
        {"value": "horizontal", "label": "Horizontal"},
        {"value": "vertical", "label": "Vertical"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 50
    }
  ],
  "blocks": [
    {
      "type": "tabbed-gallery-item",
      "name": "Tabbed Gallery Item",
      "settings": [
        {
          "type": "text",
          "id": "tab",
          "label": "Tab",
          "info": "Enter the tab name exactly. This will group the images together."
        },
        {
          "type": "header",
          "content": "Media"
        },
        {
          "type": "checkbox",
          "id": "enable_video",
          "label": "Enable video",
          "default": true
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "header",
          "content": "Image",
          "info": "If video is added, this is used as the loading state of the video."
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Description"
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        }
      ]
    },
  ],
  "presets": [
    {
      "name": "💄 Glow Gallery"
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "custom.pre_footer", "footer"]
  }
}
{% endschema %}
