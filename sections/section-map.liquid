<!-- /sections/section-map.liquid -->

{%- liquid
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign desktop_height = section.settings.height
  assign mobile_height = section.settings.mobile_height

  assign button_style = section.settings.button_style
  if button_style == 'btn--text' and section.settings.show_arrow
    assign button_style = button_style | append: ' btn--text-no-underline'
  endif
-%}

<script src="{{ 'map.js' | asset_url }}" defer="defer"></script>

{%- style -%}
  #Map--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;
  }
{%- endstyle -%}

{%- liquid
  assign display_map = false
  if section.settings.api_key != blank and section.settings.map_address
    assign display_map = true
  endif

  assign animation_anchor = '#Map--' | append: section.id

  capture columns_container_class
    echo 'map__columns'
    echo ' ' | append: desktop_height
    echo ' ' | append: mobile_height
    if section.settings.image == blank
      if desktop_height == 'image-height'
        echo ' auto-height'
      endif

      if mobile_height == 'image-height--mobile'
        echo ' auto-height--mobile'
      endif
    endif
  endcapture

  assign animation_order = 0
-%}

<div
  id="Map--{{ section.id }}"
  class="section-padding {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="map"
>
  <div class="{{ section.settings.width }}">
    <div class="{{ columns_container_class }}" {{ block.shopify_attributes }}>
      {%- if section.settings.title != blank and section.settings.address != blank and section.settings.button -%}
        <div class="map__column map__column--content">
          <div class="hero__content__wrapper align--middle-center">
            <div class="hero__content hero__content--compact text-center">
              {%- if section.settings.title != blank -%}
                {%- liquid
                  assign animation_order = animation_order | plus: 1
                  assign heading_tag = 'h2'

                  unless section.settings.heading_tag == 'automatic' or section.settings.heading_tag == blank
                    assign heading_tag = section.settings.heading_tag
                  endunless
                -%}

                <{{ heading_tag }}
                  class="hero__title {{ section.settings.heading_font_size }}"
                  data-aos="hero"
                  data-aos-anchor="{{ animation_anchor }}"
                  data-aos-order="{{ animation_order }}"
                >
                  {{ section.settings.title | escape }}
                </{{ heading_tag }}>
              {%- endif -%}

              {%- if section.settings.address != blank -%}
                {%- assign animation_order = animation_order | plus: 1 -%}
                <div
                  class="hero__rte rte"
                  data-aos="hero"
                  data-aos-anchor="{{ animation_anchor }}"
                  data-aos-order="{{ animation_order }}"
                >
                  {{ section.settings.address }}
                </div>
              {%- endif -%}

              {%- if section.settings.button != blank -%}
                {%- assign animation_order = animation_order | plus: 1 -%}
                <div
                  class="hero__button"
                  data-aos="hero"
                  data-aos-anchor="{{ animation_anchor }}"
                  data-aos-order="{{ animation_order }}"
                >
                  <a
                    href="https://maps.google.com?daddr={{ section.settings.map_address | escape }}"
                    rel="noopener"
                    target="_blank"
                    class="btn {{ button_style }} {{ section.settings.button_size }} {{ section.settings.button_type }}"
                  >
                    <span>{{ section.settings.button | escape }}</span>

                    {%- if section.settings.show_arrow -%}
                      {%- render 'icon-arrow-right' -%}
                    {%- endif -%}
                  </a>
                </div>
              {%- endif -%}
            </div>
          </div>
        </div>
      {%- endif -%}

      <div class="map__column map__column--map">
        {%- capture sizes -%}
          (min-width: 990px) 66.66vw, 50vw
        {%- endcapture -%}
        {%- render 'image-hero',
          image: section.settings.image,
          desktop_height: desktop_height,
          mobile_height: mobile_height,
          sizes: sizes
        -%}

        {%- if display_map -%}
          <map-component
            class="{{ desktop_height }} {{ mobile_height }}"
            data-zoom="{{ section.settings.zoom }}"
            data-style="{{ section.settings.style }}"
            data-address="{{ section.settings.map_address | escape }}"
            data-latlong-correction="{{ section.settings.enable_latlong }}"
            data-lat="{{ section.settings.lat }}"
            data-long="{{ section.settings.long }}"
            data-api-key="{{ section.settings.api_key }}"
          >
          </map-component>
        {%- endif -%}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Map",
  "settings": [
    {
      "id": "api_key",
      "type": "text",
      "label": "Google Maps API key",
      "info": "You’ll need to [register a Google Maps API Key](https://help.shopify.com/manual/using-themes/troubleshooting/map-section-api-key) to display the map"
    },
    {
      "id": "map_address",
      "type": "text",
      "label": "Map address",
      "info": "Google Maps will use this address to get latitude and longitude",
      "default": "404 Beach Avenue, Vancouver, BC"
    },
    {
      "type": "range",
      "id": "zoom",
      "min": 1,
      "max": 20,
      "step": 1,
      "label": "Zoom",
      "info": "Low levels cover a wide area, while higher levels cover a smaller area.",
      "default": 11
    },
    {
      "type": "select",
      "id": "style",
      "label": "Map style",
      "default": "light",
      "options": [
        {"value": "default", "label": "Default"},
        {"value": "light", "label": "Minimal"},
        {"value": "white_label", "label": "Light"},
        {"value": "dark_label", "label": "Dark"}
      ]
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "Displayed if the map isn't loaded. 3200 x 1200px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "header",
      "content": "Location correction"
    },
    {
      "type": "paragraph",
      "content": "Correct the pin position if Google shows an incorrect location for your address."
    },
    {
      "type": "checkbox",
      "id": "enable_latlong",
      "label": "Enable location correction",
      "default": false
    },
    {
      "type": "text",
      "id": "lat",
      "label": "Latitude",
      "info": "Example: 49.281670"
    },
    {
      "type": "text",
      "id": "long",
      "label": "Longitude",
      "info": "Example: -123.139640"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Map"
    },
    {
      "type": "select",
      "id": "heading_font_size",
      "label": "Heading size",
      "default": "heading-medium",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "richtext",
      "id": "address",
      "label": "Address and hours",
      "default": "<p>123 Fake St.<br>Vancouver, Canada</p><p>Mon - Fri, 10am - 9pm<br>Weekends, 11am - 4pm</p>"
    },
    {
      "type": "text",
      "id": "button",
      "label": "Map link label",
      "default": "Directions"
    },
    {
      "type": "header",
      "content": "Button"
    },
    {
      "type": "select",
      "id": "button_type",
      "label": "Color",
      "default": "btn--primary",
      "options": [
        {"value": "btn--black", "label": "Black"},
        {"value": "btn--white", "label": "White"},
        {"value": "btn--primary", "label": "Primary"},
        {"value": "btn--secondary", "label": "Secondary"}
      ]
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Size",
      "default": "",
      "options": [
        {"label": "Small", "value": "btn--small"},
        {"label": "Medium", "value": ""},
        {"label": "Large", "value": "btn--large"}
      ]
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Style",
      "default": "btn--outline",
      "options": [
        {"label": "Solid", "value": "btn--solid"},
        {"label": "Outline", "value": "btn--outline"},
        {"label": "Text", "value": "btn--text"}
      ]
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "Show arrow",
      "default": false
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "height",
      "label": "Height",
      "default": "screen-height-one-half",
      "options": [
        {"value": "image-height", "label": "Image height"},
        {"value": "screen-height-full", "label": "Full screen height"},
        {"value": "screen-height-three-quarters", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds", "label": "2/3 of screen"},
        {"value": "screen-height-one-half", "label": "1/2 of screen"},
        {"value": "screen-height-one-third", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero", "label": "750px"},
        {"value": "sixty-fifty-height-hero", "label": "650px"},
        {"value": "five-fifty-height-hero ", "label": "550px"},
        {"value": "four-fifty-height-hero", "label": "450px"}
      ]
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Height",
      "default": "screen-height-one-half--mobile",
      "options": [
        {"value": "image-height--mobile", "label": "Image height"},
        {"value": "screen-height-full--mobile", "label": "Full screen height"},
        {"value": "screen-height-three-quarters--mobile", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds--mobile", "label": "2/3 of screen"},
        {"value": "screen-height-one-half--mobile", "label": "1/2 of screen"},
        {"value": "screen-height-one-third--mobile", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero--mobile", "label": "750px"},
        {"value": "sixty-fifty-height-hero--mobile", "label": "650px"},
        {"value": "five-fifty-height-hero--mobile", "label": "550px"},
        {"value": "four-fifty-height-hero--mobile", "label": "450px"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"}
      ]
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "automatic",
      "options": [
        {"value": "automatic", "label": "Automatic"},
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    }
  ],
  "presets": [
    {
      "name": "Map"
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "footer"]
  }
}
{% endschema %}
