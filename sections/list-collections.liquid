<!-- /sections/list-collections.liquid -->

{%- liquid
  assign limit = section.settings.grid | times: section.settings.rows
  assign heading = section.settings.title
  assign color_scheme = 'color-' | append: section.settings.color_scheme

  assign columns_desktop = section.settings.grid | plus: 0
  assign columns_medium = 3
  assign columns_small = 2
  assign columns_mobile = section.settings.layout_mobile | plus: 0

  if columns_desktop == 2 or columns_desktop == 4
    assign columns_medium = 2
  endif

  assign excluded_collections_strict = settings.exclude_collections_strict | split: ','
  assign excluded_collections = settings.exclude_collections_contain | split: ','
-%}

{%- style -%}
  .index-list-collections {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;

    --COLUMNS: {{ columns_desktop }};
    --COLUMNS-MEDIUM: {{ columns_medium }};
    --COLUMNS-SMALL: {{ columns_small }};
    --COLUMNS-MOBILE: {{ columns_mobile }};
  }
{%- endstyle -%}

<div
  class="index-list-collections section-padding {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="list-collections"
>
  {%- if heading != blank -%}
    {%- liquid
      assign heading_tag = 'h2'

      unless section.settings.heading_tag == 'automatic' or section.settings.heading_tag == blank
        assign heading_tag = section.settings.heading_tag
      endunless
    -%}

    <div class="grid__heading-holder text-center">
      <{{ heading_tag }} class="grid__heading {{ section.settings.heading_font_size }}">
        {{- heading -}}
      </{{ heading_tag }}>
    </div>
  {%- endif -%}

  <div class="collection-list">
    {%- if section.settings.display_type == 'all' -%}
      {%- for collection in collections -%}
        {%- liquid
          assign skip_collection = false

          for exclude in excluded_collections_strict
            assign exclude_handle = exclude | handle

            if exclude_handle == collection.handle
              assign skip_collection = true
            endif
          endfor

          for exclude in excluded_collections
            assign exclude_handle = exclude | handle

            if collection.handle contains exclude_handle
              assign skip_collection = true
            endif
          endfor
        -%}

        {%- if skip_collection == false and collection.products_count > 0 -%}
          <div class="collection-block__wrapper collection-block__wrapper--{{ collection.handle }}">
            {%- render 'collection-block', collection: collection, block: block, limit: limit -%}
          </div>
        {%- endif -%}
      {%- endfor -%}
    {%- else -%}
      {%- for block in section.blocks -%}
        {%- assign collection = collections[block.settings.collection] -%}

        <div class="collection-block__wrapper collection-block__wrapper--{{ block.id }}" {{ block.shopify_attributes }}>
          {%- render 'collection-block', collection: collection, block: block, limit: limit -%}
        </div>
      {%- endfor -%}
    {%- endif -%}
  </div>
</div>

{% schema %}
{
  "name": "Collection list",
  "settings": [
    {
      "type": "paragraph",
      "content": "All of your collections are listed by default. To customize your list, choose 'Selected' and add collections."
    },
    {
      "type": "radio",
      "id": "display_type",
      "label": "Select collections to show",
      "default": "all",
      "options": [
        {
          "value": "all",
          "label": "All"
        },
        {
          "value": "selected",
          "label": "Selected"
        }
      ]
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Collections"
    },
    {
      "type": "select",
      "id": "heading_font_size",
      "label": "Heading size",
      "default": "heading-medium",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "header",
      "content": "Product grid"
    },
    {
      "type": "range",
      "id": "grid",
      "min": 2,
      "max": 6,
      "step": 1,
      "label": "Products per row",
      "default": 4
    },
    {
      "type": "range",
      "id": "rows",
      "min": 1,
      "max": 8,
      "step": 1,
      "label": "Number of rows",
      "default": 1
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "select",
      "id": "layout_mobile",
      "label": "Layout",
      "options": [
        {
          "value": "1",
          "label": "1 item per row"
        },
        {
          "value": "2",
          "label": "2 items per row"
        },
        {
          "value": "slider",
          "label": "Slider"
        }
      ],
      "default": "slider"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 32
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 32
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "automatic",
      "options": [
        {"value": "automatic", "label": "Automatic"},
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "label": "Collection",
          "id": "collection",
          "type": "collection"
        },
        {
          "label": "Collection name",
          "id": "collection_name",
          "type": "text",
          "info": "Replace the collection name with custom text"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "Replace the collection image with a custom image. 2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        }
      ]
    }
  ]
}
{% endschema %}
