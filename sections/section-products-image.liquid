{%- liquid
  assign animation_anchor = '#ProductsImage--' | append: section.id
  assign wrapper_width = section.settings.width
  assign color_scheme = 'color-' | append: section.settings.color_scheme
-%}

{%- capture style -%}
  --PT: {{ section.settings.padding_top }}px;
  --PB: {{ section.settings.padding_bottom }}px;
{%- endcapture -%}

<section
  id="ProductsImage--{{ section.id }}"
  data-section-id="{{ section.id }}"
  data-section-type="products-image"
  class="section-padding {{ color_scheme }}"
  style="{{ style }}"
>
  {%- if section.blocks.size > 0 -%}
    <div class="brick__section{% if section.settings.reverse_blocks %} brick__section--reversed-mobile{% endif %} {{ wrapper_width }}">
      {%- liquid
        for block in section.blocks
          case block.type
            when 'products'
              render 'brick-products', block: block, animation_anchor: animation_anchor

            when 'image'
              render 'brick-image', block: block, animation_anchor: animation_anchor
          endcase
        endfor
      -%}
    </div>
  {%- else -%}
    {%- render 'no-blocks' -%}
  {%- endif -%}
</section>

{% schema %}
{
  "name": "Products with image",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full-padded",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"}
      ]
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "checkbox",
      "id": "reverse_blocks",
      "label": "Reverse block placement",
      "default": false
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 50
    }
  ],
  "blocks": [
    {
      "type": "products",
      "name": "Products",
      "limit": 1,
      "settings": [
        {
          "type": "product_list",
          "id": "product_list",
          "label": "Product list",
          "limit": 4,
          "info": "Choose up to 4 products"
        },
        {
          "type": "header",
          "content": "Mobile"
        },
        {
          "type": "select",
          "id": "layout_mobile",
          "label": "Layout",
          "options": [
            {
              "value": "1",
              "label": "1 item per row"
            },
            {
              "value": "2",
              "label": "2 items per row"
            },
            {
              "value": "slider",
              "label": "Slider"
            }
          ],
          "default": "slider"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "limit": 1,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "select",
          "id": "mobile_height",
          "label": "Mobile height",
          "default": "screen-height-one-half--mobile",
          "options": [
            {"value": "image-height--mobile", "label": "Image height"},
            {"value": "screen-height-full--mobile", "label": "Full screen height"},
            {"value": "screen-height-three-quarters--mobile", "label": "3/4 of screen"},
            {"value": "screen-height-two-thirds--mobile", "label": "2/3 of screen"},
            {"value": "screen-height-one-half--mobile", "label": "1/2 of screen"},
            {"value": "screen-height-one-third--mobile", "label": "1/3 of screen"},
            {"value": "seven-fifty-height-hero--mobile", "label": "750px"},
            {"value": "sixty-fifty-height-hero--mobile", "label": "650px"},
            {"value": "five-fifty-height-hero--mobile", "label": "550px"},
            {"value": "four-fifty-height-hero--mobile", "label": "450px"}
          ]
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-large",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "select",
          "id": "flex_align",
          "label": "Block alignment",
          "default": "align--middle-center",
          "options": [
            {"value": "align--top-left", "label": "Top left"},
            {"value": "align--top-center", "label": "Top center"},
            {"value": "align--top-right", "label": "Top right"},
            {"value": "align--middle-left", "label": "Middle left"},
            {"value": "align--middle-center", "label": "Absolute center"},
            {"value": "align--middle-right", "label": "Middle right"},
            {"value": "align--bottom-left", "label": "Bottom left"},
            {"value": "align--bottom-center", "label": "Bottom center"},
            {"value": "align--bottom-right", "label": "Bottom right"}
          ]
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "Overlay opacity",
          "info": "Increase contrast for legible text.",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "default": 0
        },
        {
          "type": "color",
          "id": "overlay_color",
          "label": "Overlay color"
        },
        {
          "type": "checkbox",
          "id": "show_overlay_text",
          "label": "Overlay behind text only",
          "default": false
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "Text",
          "info": "Leave blank to link entire image"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "checkbox",
          "id": "show_text_background",
          "label": "Show text background",
          "default": false
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text"
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Products with image",
      "blocks": [
        {
          "type": "products"
        },
        {
          "type": "image"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "custom.pre_footer", "footer"]
  }
}
{% endschema %}
