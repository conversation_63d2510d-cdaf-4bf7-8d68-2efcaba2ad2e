{%- render 'announcement' -%}

{% schema %}
{
  "name": "Scrolling text",
  "settings": [
    {
      "type": "header",
      "content": "Slider"
    },
    {
      "type": "range",
      "id": "slider_speed",
      "label": "Autoplay speed",
      "unit": "sec",
      "min": 5,
      "max": 20,
      "step": 1,
      "default": 7
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ]
    },
    {
      "type": "header",
      "content": "Scrolling text"
    },
    {
      "type": "range",
      "id": "marquee_speed",
      "label": "Autoplay speed",
      "unit": "%",
      "min": 50,
      "max": 300,
      "step": 25,
      "default": 100
    },
    {
      "type": "select",
      "id": "direction",
      "label": "Text direction",
      "default": "ticker-rtl",
      "options": [
        {"value": "ticker-ltr", "label": "Left to right"},
        {"value": "ticker-rtl", "label": "Right to left"}
      ]
    },
    {
      "type": "range",
      "id": "message_spacing",
      "label": "Space between messages",
      "default": 10,
      "min": 5,
      "max": 100,
      "step": 5,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Background image (optional)"
    },
    {
      "type": "image_picker",
      "id": "background_image",
      "label": "Image",
      "info": "3200 x 1200px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "checkbox",
      "id": "show_border",
      "label": "Show border",
      "default": false
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "default": "marquee",
      "options": [
        {"value": "slider", "label": "Slider"},
        {"value": "marquee", "label": "Scrolling text"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "Heading",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "Make an announcement"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-small",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p><strong>Make an announcement</strong> | More info</p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-small",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text "
        },
        {
          "type": "header",
          "content": "Target device"
        },
        {
          "id": "target_device_enabled",
          "type": "checkbox",
          "label": "Limit to desktop or mobile"
        },
        {
          "type": "select",
          "id": "target_device",
          "label": "Device ",
          "default": "mobile",
          "options": [
            {"value": "mobile", "label": "Only show on mobile"},
            {"value": "desktop", "label": "Only show on desktop"}
          ]
        }
      ]
    },
    {
      "type": "message",
      "name": "Free shipping message",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_wheel",
          "label": "Show progress wheel",
          "default": true
        },
        {
          "type": "textarea",
          "id": "message",
          "label": "Message",
          "info": "Use ||amount|| to display progress towards free shipping.",
          "default": "You are ||amount|| away from free shipping."
        },
        {
          "type": "paragraph",
          "content": "Navigate to Theme settings -> \"Cart -> Free shipping message\" to set the amount."
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size.",
          "default": "body-small",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "header",
          "content": "Target device"
        },
        {
          "id": "target_device_enabled",
          "type": "checkbox",
          "label": "Limit to desktop or mobile"
        },
        {
          "type": "select",
          "id": "target_device",
          "label": "Device ",
          "default": "mobile",
          "options": [
            {"value": "mobile", "label": "Only show on mobile"},
            {"value": "desktop", "label": "Only show on desktop"}
          ]
        }
      ]
    },
    {
      "type": "code",
      "name": "Custom code",
      "settings": [
        {
          "type": "liquid",
          "id": "code",
          "label": "Code",
          "default": "<p>Custom code</p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size.",
          "default": "body-small",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "header",
          "content": "Target device"
        },
        {
          "id": "target_device_enabled",
          "type": "checkbox",
          "label": "Limit to desktop or mobile"
        },
        {
          "type": "select",
          "id": "target_device",
          "label": "Device ",
          "default": "mobile",
          "options": [
            {"value": "mobile", "label": "Only show on mobile"},
            {"value": "desktop", "label": "Only show on desktop"}
          ]
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "360 x 360px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "range",
          "id": "image_width",
          "label": "Width",
          "unit": "px",
          "min": 20,
          "max": 180,
          "step": 10,
          "default": 100
        }
      ]
    },
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size.",
          "default": "body-small",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "checkbox",
          "id": "type_font_caps",
          "label": "Uppercase",
          "default": false
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text "
        }
      ]
    },
    {
      "type": "advanced-text",
      "name": "Advanced text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p><strong>Make an announcement</strong> | More info</p>"
        },
        {
          "type": "font_picker",
          "id": "type_font",
          "label": "Font",
          "default": "poppins_n4"
        },
        {
          "type": "range",
          "id": "text_font_size_px",
          "min": 10,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Text size",
          "default": 16
        },
        {
          "type": "range",
          "id": "type_letter_spacing",
          "min": -25,
          "max": 200,
          "step": 25,
          "unit": "%",
          "label": "Letter spacing",
          "default": 0
        },
        {
          "type": "checkbox",
          "id": "type_font_caps",
          "label": "Uppercase",
          "default": false
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "paragraph",
          "content": "Overrides color scheme. Set to Transparent to revert back to using color schemes."
        },
        {
          "type": "color",
          "id": "color",
          "label": "Text"
        },
        {
          "type": "header",
          "content": "Target device"
        },
        {
          "id": "target_device_enabled",
          "type": "checkbox",
          "label": "Limit to desktop or mobile"
        },
        {
          "type": "select",
          "id": "target_device",
          "label": "Device ",
          "default": "mobile",
          "options": [
            {"value": "mobile", "label": "Only show on mobile"},
            {"value": "desktop", "label": "Only show on desktop"}
          ]
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Scrolling text",
      "blocks": [
        {
          "type": "text",
          "settings": {
            "text": "<p><strong>Make an announcement</strong> | Target to specific devices or referrers</p>"
          }
        },
        {
          "type": "text",
          "settings": {
            "text": "<p><strong>Create a free shipping countdown</strong></p>"
          }
        },
        {
          "type": "message",
          "settings": {
            "show_wheel": false
          }
        },
        {
          "type": "text",
          "settings": {
            "text": "<p><strong>Create a scrolling marquee or a simple announcement</strong></p>"
          }
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside"]
  }
}
{% endschema %}
