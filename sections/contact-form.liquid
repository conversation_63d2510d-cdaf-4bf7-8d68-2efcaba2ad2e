<!-- /sections/contact-form.liquid -->

{%- liquid
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign types = ''
  assign has_required_field = false
  assign animation_anchor = '#ContactForm--' | append: section.id
  assign animation_order = 1
-%}

{%- style -%}
  #ContactForm--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;
  }
{%- endstyle -%}

<section
  id="ContactForm--{{ section.id }}"
  class="index-contact section-padding {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="contact"
>
  <div class="{{ section.settings.width }}">
    {%- if section.settings.title != blank -%}
      {%- liquid
        assign heading_tag = 'h2'

        unless section.settings.heading_tag == 'automatic' or section.settings.heading_tag == blank
          assign heading_tag = section.settings.heading_tag
        endunless
      -%}

      <{{ heading_tag }}
        class="contact__form__heading {{ section.settings.heading_font_size }}"
        data-aos="hero"
        data-aos-anchor="{{ animation_anchor }}"
        data-aos-order="{{ animation_order }}"
      >
        {{ section.settings.title }}
      </{{ heading_tag }}>
    {%- endif -%}

    {%- form 'contact', id: 'contact-us-page' -%}
      {%- if form.posted_successfully? -%}
        <p class="form-success">
          {{ 'contact.form.post_success' | t }}
        </p>
      {%- endif -%}

      {{ form.errors | default_errors }}

      {%- for block in section.blocks -%}
        {%- liquid
          assign types = types | append: block.type
          assign animation_order = animation_order | plus: 1
          assign title = block.settings.title
          assign placeholder = title

          assign required = ''
          if block.settings.required
            assign required = 'required'
            assign placeholder = title | append: '*'

            unless has_required_field
              assign has_required_field = true
            endunless
          endif
        -%}

        {%- case block.type -%}
          {%- when 'email' -%}
            <div
              class="custom-form__block"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
              {{ block.shopify_attributes }}
            >
              <label for="Form-{{ section.id }}-{{ forloop.index0 }}" class="visually-hidden">{{ title }}</label>
              <input
                type="email"
                class="contactFormEmail field"
                id="Form-{{ section.id }}-{{ forloop.index0 }}"
                name="contact[email]"
                placeholder="{{ placeholder }}"
                autocorrect="off"
                autocapitalize="off"
                {{ required }}
              >
            </div>

          {%- when 'body' -%}
            <div
              class="custom-form__block"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
              {{ block.shopify_attributes }}
            >
              <label for="Form-{{ section.id }}-{{ forloop.index0 }}" class="visually-hidden">{{ title }}</label>
              <textarea
                rows="10"
                id="Form-{{ section.id }}-{{ forloop.index0 }}"
                class="contactFormMessage field"
                name="contact[body]"
                placeholder="{{ placeholder }}"
                {{ required -}}
              ></textarea>
            </div>

          {%- when 'text' -%}
            <div
              class="custom-form__block"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
              {{ block.shopify_attributes }}
            >
              <label for="Form-{{ section.id }}-{{ forloop.index0 }}" class="visually-hidden">{{ title }}</label>
              <input
                type="text"
                class="contactFormText field"
                id="Form-{{ section.id }}-{{ forloop.index0 }}"
                name="contact[{{ title | handleize | default: 'text' }}]"
                placeholder="{{ placeholder }}"
                autocapitalize="words"
                value=""
                {{ required }}
              >
            </div>

          {%- when 'textarea' -%}
            <div
              class="custom-form__block"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
              {{ block.shopify_attributes }}
            >
              <label for="Form-{{ section.id }}-{{ forloop.index0 }}" class="visually-hidden">{{ title }}</label>
              <textarea
                rows="10"
                id="Form-{{ section.id }}-{{ forloop.index0 }}"
                class="contactFormMessage field"
                name="contact[{{ title | handleize | default: 'textarea' }}]"
                placeholder="{{ placeholder }}"
                {{ required -}}
              ></textarea>
            </div>

          {%- when 'telephone' -%}
            <div
              class="custom-form__block"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
              {{ block.shopify_attributes }}
            >
              <label for="Form-{{ section.id }}-{{ forloop.index0 }}" class="visually-hidden">{{ title }}</label>
              <input
                type="tel"
                id="Form-{{ section.id }}-{{ forloop.index0 }}"
                class="contactFormPhone field"
                name="contact[telephone]"
                placeholder="{{ placeholder }}"
                value=""
                {{ required }}
              >
            </div>

          {%- when 'formHeading' -%}
            <div
              class="custom-form__block custom-form__block--heading"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
              {{ block.shopify_attributes }}
            >
              <p class="strong">{{ title }}</p>
            </div>

          {%- when 'spacer' -%}
            {%- assign hr_class = 'hr--clear' -%}
            {%- if block.settings.line -%}
              {%- assign hr_class = 'hr--color' -%}
            {%- endif -%}
            <div
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
              {{ block.shopify_attributes }}
            >
              <hr class="{{ hr_class }}">
            </div>

          {%- when 'checkbox' -%}
            <div
              class="custom-form__block checkbox"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
              {{ block.shopify_attributes }}
            >
              <input
                type="checkbox"
                id="Form-{{ section.id }}-{{ forloop.index0 }}"
                class="contactFormCheckbox"
                name="contact[{{ title | handleize | default: 'checkbox' }}]"
                value="{{ title }}"
                {{ required }}
              >
              <label for="Form-{{ section.id }}-{{ forloop.index0 }}">
                {%- if required != blank -%}
                  {%- assign title = title | append: '*' -%}
                {%- endif -%}

                {{ title }}
              </label>
            </div>

          {%- when 'radio' -%}
            <fieldset
              class="custom-form__block custom-form__block--group"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
              {{ block.shopify_attributes }}
            >
              {%- if title != blank -%}
                {%- if required != blank -%}
                  {%- assign title = title | append: '*' -%}
                {%- endif -%}
                <legend class="custom-form__label">{{ title }}</legend>
              {%- endif -%}
              {%- if block.settings['label-one'] != blank -%}
                <div>
                  <input
                    type="radio"
                    id="Form-{{ section.id }}-{{ forloop.index0 }}-1"
                    class="contactFormRadio"
                    name="contact[{{ title | handleize | default: 'radio' }}]"
                    value="{{ block.settings.label-one }}"
                    {{ required }}
                  >
                  <label for="Form-{{ section.id }}-{{ forloop.index0 }}-1">{{ block.settings['label-one'] }}</label>
                </div>
              {%- endif -%}
              {%- if block.settings['label-two'] != blank -%}
                <div>
                  <input
                    type="radio"
                    id="Form-{{ section.id }}-{{ forloop.index0 }}-2"
                    class="contactFormRadio"
                    name="contact[{{ title | handleize | default: 'radio' }}]"
                    value="{{ block.settings.label-two }}"
                    {{ required }}
                  >
                  <label for="Form-{{ section.id }}-{{ forloop.index0 }}-2">{{ block.settings['label-two'] }}</label>
                </div>
              {%- endif -%}
              {%- if block.settings['label-three'] != blank -%}
                <div>
                  <input
                    type="radio"
                    id="Form-{{ section.id }}-{{ forloop.index0 }}-3"
                    class="contactFormRadio"
                    name="contact[{{ title | handleize | default: 'radio' }}]"
                    value="{{ block.settings.label-three }}"
                    {{ required }}
                  >
                  <label for="Form-{{ section.id }}-{{ forloop.index0 }}-3">{{ block.settings['label-three'] }}</label>
                </div>
              {%- endif -%}
              {%- if block.settings['label-four'] != blank -%}
                <div>
                  <input
                    type="radio"
                    id="Form-{{ section.id }}-{{ forloop.index0 }}-4"
                    class="contactFormRadio"
                    name="contact[{{ title | handleize | default: 'radio' }}]"
                    value="{{ block.settings.label-four }}"
                    {{ required }}
                  >
                  <label for="Form-{{ section.id }}-{{ forloop.index0 }}-4">{{ block.settings['label-four'] }}</label>
                </div>
              {%- endif -%}
              {%- if block.settings['label-five'] != blank -%}
                <div>
                  <input
                    type="radio"
                    id="Form-{{ section.id }}-{{ forloop.index0 }}-5"
                    class="contactFormRadio"
                    name="contact[{{ title | handleize | default: 'radio' }}]"
                    value="{{ block.settings.label-five }}"
                    {{ required }}
                  >
                  <label for="Form-{{ section.id }}-{{ forloop.index0 }}-5">{{ block.settings['label-five'] }}</label>
                </div>
              {%- endif -%}
              {%- if block.settings['label-six'] != blank -%}
                <div>
                  <input
                    type="radio"
                    id="Form-{{ section.id }}-{{ forloop.index0 }}-6"
                    class="contactFormRadio"
                    name="contact[{{ title | handleize | default: 'radio' }}]"
                    value="{{ block.settings.label-six }}"
                    {{ required }}
                  >
                  <label for="Form-{{ section.id }}-{{ forloop.index0 }}-6">{{ block.settings['label-six'] }}</label>
                </div>
              {%- endif -%}
            </fieldset>

          {%- when 'select' -%}
            <fieldset
              class="custom-form__block custom-form__block--group"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
              {{ block.shopify_attributes }}
            >
              {%- if title != blank -%}
                {%- if required != blank -%}
                  {%- assign title = title | append: '*' -%}
                {%- endif -%}
                <label for="Form-{{ section.id }}-{{ forloop.index0 }}" class="custom-form__label label--block">
                  {{- title -}}
                </label>
              {%- endif -%}
              <select
                id="Form-{{ section.id }}-{{ forloop.index0 }}"
                class="contactFormSelect field"
                name="contact[{{ title | handleize | default: 'select' }}]"
                {{ required }}
              >
                {%- if required != blank -%}
                  <option value="">{{ 'contact.form.select_default' | t }}</option>
                {%- endif -%}
                {%- if block.settings['label-one'] != blank -%}
                  <option value="{{ block.settings.label-one }}">{{ block.settings['label-one'] }}</option>
                {%- endif -%}
                {%- if block.settings['label-two'] != blank -%}
                  <option value="{{ block.settings.label-two }}">{{ block.settings['label-two'] }}</option>
                {%- endif -%}
                {%- if block.settings['label-three'] != blank -%}
                  <option value="{{ block.settings.label-three }}">{{ block.settings['label-three'] }}</option>
                {%- endif -%}
                {%- if block.settings['label-four'] != blank -%}
                  <option value="{{ block.settings.label-four }}">{{ block.settings['label-four'] }}</option>
                {%- endif -%}
                {%- if block.settings['label-five'] != blank -%}
                  <option value="{{ block.settings.label-five }}">{{ block.settings['label-five'] }}</option>
                {%- endif -%}
                {%- if block.settings['label-six'] != blank -%}
                  <option value="{{ block.settings.label-six }}">{{ block.settings['label-six'] }}</option>
                {%- endif -%}
              </select>
            </fieldset>
          {%- else -%}
        {%- endcase -%}
      {%- endfor -%}

      {%- unless types contains 'email' -%}
        {%- liquid
          assign email = 'contact.form.email' | t
          assign has_required_field = true
          assign animation_order = animation_order | plus: 1
        -%}

        <div
          class="custom-form__block"
          data-aos="hero"
          data-aos-anchor="{{ animation_anchor }}"
          data-aos-order="{{ animation_order }}"
        >
          <label for="contactFormEmail" class="visually-hidden">{{ email }}</label>
          <input
            type="email"
            id="contactFormEmail"
            class="contactFormEmail field"
            name="contact[email]"
            placeholder="{{ email | append: '*' }}"
            autocorrect="off"
            autocapitalize="off"
            required
          >
        </div>
      {%- endunless -%}

      {%- unless types contains 'body' -%}
        {%- liquid
          assign message = 'contact.form.message' | t
          assign has_required_field = true
          assign animation_order = animation_order | plus: 1
        -%}

        <div
          class="custom-form__block"
          data-aos="hero"
          data-aos-anchor="{{ animation_anchor }}"
          data-aos-order="{{ animation_order }}"
        >
          <label for="ContactFormMessage" class="visually-hidden">{{ message }}</label>
          <textarea
            rows="10"
            id="ContactFormMessage"
            class="contactFormMessage field"
            name="contact[body]"
            placeholder="{{ message | append: '*' }}"
          ></textarea>
        </div>
      {%- endunless -%}

      {%- assign animation_order = animation_order | plus: 1 -%}
      <div
        class="custom-form__block"
        data-aos="hero"
        data-aos-anchor="{{ animation_anchor }}"
        data-aos-order="{{ animation_order }}"
      >
        <button type="submit" class="btn btn--large btn--primary btn--solid btn--full">
          {{ 'contact.form.send' | t }}
        </button>
      </div>

      {%- if has_required_field -%}
        {%- assign animation_order = animation_order | plus: 1 -%}

        <div
          class="custom-form__required-text"
          data-aos="hero"
          data-aos-anchor="{{ animation_anchor }}"
          data-aos-order="{{ animation_order }}"
        >
          {{ 'contact.form.required_field_text' | t }}
        </div>
      {%- endif -%}

      {%- if section.settings.terms -%}
        {%- assign animation_order = animation_order | plus: 1 -%}
        <div
          class="form__legal"
          data-aos="hero"
          data-aos-anchor="{{ animation_anchor }}"
          data-aos-order="{{ animation_order }}"
        >
          {{ 'shopify.online_store.spam_detection.disclaimer_html' | t }}
        </div>
      {%- endif -%}
    {%- endform -%}
  </div>
</section>

{% schema %}
{
  "name": "Contact form",
  "settings": [
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Contact us"
    },
    {
      "type": "select",
      "id": "heading_font_size",
      "label": "Heading size",
      "default": "heading-large",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "checkbox",
      "id": "terms",
      "label": "Show hCAPTCHA terms",
      "default": true,
      "info": "Recommended if your online store preferences have spam protection enabled."
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--narrow",
      "options": [
        {"value": "wrapper", "label": "Normal"},
        {"value": "wrapper--narrow", "label": "Narrow"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 100
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 100
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "automatic",
      "options": [
        {"value": "automatic", "label": "Automatic"},
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    }
  ],
  "blocks": [
    {
      "type": "email",
      "name": "Email",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Label",
          "default": "Email"
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": true
        }
      ]
    },
    {
      "type": "body",
      "name": "Message",
      "limit": 1,
      "settings": [
        {
          "type": "textarea",
          "id": "title",
          "label": "Label",
          "default": "Message"
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": true
        }
      ]
    },
    {
      "type": "text",
      "name": "Text line",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Label",
          "default": "Full Name",
          "info": "Example: Full Name"
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": false
        }
      ]
    },
    {
      "type": "textarea",
      "name": "Text area",
      "settings": [
        {
          "type": "textarea",
          "id": "title",
          "label": "Label",
          "default": "Enter your message",
          "info": "Example: Enter your message"
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": false
        }
      ]
    },
    {
      "type": "telephone",
      "name": "Telephone",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Label",
          "default": "Phone Number",
          "info": "Example: Phone Number"
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": false
        }
      ]
    },
    {
      "type": "formHeading",
      "name": "Heading",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading",
          "info": "Use to break up longer forms"
        }
      ]
    },
    {
      "type": "spacer",
      "name": "Spacer",
      "settings": [
        {
          "type": "checkbox",
          "id": "line",
          "label": "Show line",
          "default": false
        }
      ]
    },
    {
      "type": "checkbox",
      "name": "Single checkbox",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Label",
          "default": "Checkbox option",
          "info": "Example: Subscribe to newsletter"
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": false
        }
      ]
    },
    {
      "type": "select",
      "name": "Select dropdown",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Select an option",
          "info": "Example: Choose a delivery location"
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": false
        },
        {
          "type": "header",
          "content": "Include up to six options",
          "info": "Options with blank text will not appear."
        },
        {
          "type": "text",
          "id": "label-one",
          "default": "Option one",
          "label": "Option one"
        },
        {
          "type": "text",
          "id": "label-two",
          "default": "Option two",
          "label": "Option two"
        },
        {
          "type": "text",
          "id": "label-three",
          "default": "Option three",
          "label": "Option three"
        },
        {
          "type": "text",
          "id": "label-four",
          "default": "Option four",
          "label": "Option four"
        },
        {
          "type": "text",
          "id": "label-five",
          "default": "Option five",
          "label": "Option five"
        },
        {
          "type": "text",
          "id": "label-six",
          "default": "Option six",
          "label": "Option six"
        }
      ]
    },
    {
      "type": "radio",
      "name": "Radio buttons",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Pick an option"
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": false
        },
        {
          "type": "header",
          "content": "Include up to six options",
          "info": "Leave option text blank to exclude"
        },
        {
          "type": "text",
          "id": "label-one",
          "default": "Option one",
          "label": "Option one"
        },
        {
          "type": "text",
          "id": "label-two",
          "default": "Option two",
          "label": "Option two"
        },
        {
          "type": "text",
          "id": "label-three",
          "label": "Option three"
        },
        {
          "type": "text",
          "id": "label-four",
          "label": "Option four"
        },
        {
          "type": "text",
          "id": "label-five",
          "label": "Option five"
        },
        {
          "type": "text",
          "id": "label-six",
          "label": "Option six"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Contact form",
      "blocks": [
        {
          "type": "text",
          "settings": {
            "title": "Full Name"
          }
        },
        {
          "type": "email",
          "settings": {
            "title": "Email"
          }
        },
        {
          "type": "body",
          "settings": {
            "title": "Message"
          }
        },
        {
          "type": "formHeading",
          "settings": {
            "title": "Optional:"
          }
        },
        {
          "type": "checkbox",
          "settings": {
            "title": "This is urgent"
          }
        },
        {
          "type": "select",
          "settings": {
            "title": "Select an option",
            "label-one": "- Pick an option -",
            "label-two": "Option one",
            "label-three": "Option two",
            "label-four": "Option three",
            "label-five": "Option four",
            "label-six": "Option five"
          }
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "footer"]
  }
}
{% endschema %}
