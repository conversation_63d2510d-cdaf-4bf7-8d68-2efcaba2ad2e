<!-- /sections/password.liquid -->

{%- liquid
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign logo = section.settings.logo
  assign logo_max_width = section.settings.logo_max_width
  assign animation_anchor = '#PasswordTemplate--' | append: section.id
  assign animation_order = 1
-%}

<section
  id="PasswordTemplate--{{ section.id }}"
  data-section-id="{{ section.id }}"
  data-section-type="password-template"
  class="index-image-text section-padding text-center {{ color_scheme }}"
  style="{{ style }}"
>
  <popup-component
    {% unless form.errors != blank %}
      open="true"
    {% endunless %}
  >
    <div class="brick__section{% if section.settings.layout == 'right' %} brick__section--reversed{% endif %} screen-height-full screen-height-full--mobile">
      {%- if section.settings.image != blank -%}
        <div class="brick__block brick__block--images is-sticky desktop">
          <div class="brick__block__image">
            {%- render 'image-hero',
              image: section.settings.image,
              desktop_height: 'screen-height-full',
              sizes: '50vw'
            -%}
          </div>
        </div>
      {%- endif -%}

      <div
        class="brick__block brick__block--text brick__block--password"
        {% if style != blank %}
          style="{{ style }}"
        {% endif %}
        {{ block.shopify_attributes }}
      >
        <div class="brick__block__text">
          <div
            class="brick__block__actions"
            data-aos="hero"
            data-aos-anchor="{{ animation_anchor }}"
            data-aos-order="{{ animation_order }}"
          >
            {%- if logo != blank -%}
              <figure class="brick__block__logo__holder">
                <div class="brick__block__logo" style="width: {{ logo_max_width }}px">
                  {%- liquid
                    assign width = logo_max_width | times: 2
                    assign sizes = logo_max_width | append: 'px'
                    capture widths
                      echo logo_max_width
                      echo ', ' | append: width
                    endcapture

                    render 'image', image: logo, width: width, widths: widths, sizes: sizes
                  -%}
                </div>
              </figure>
            {%- else -%}
              <h1 class="brick__block__title">{{ shop.name }}</h1>
            {%- endif -%}
          </div>

          {%- if section.blocks.size > 0 -%}
            <div class="brick__block__content">
              {%- for block in section.blocks -%}
                {%- case block.type -%}
                  {%- when 'text' -%}
                    {%- assign animation_order = animation_order | plus: 1 -%}
                    <div
                      class="brick__block__description"
                      data-aos="hero"
                      data-aos-anchor="{{ animation_anchor }}"
                      data-aos-order="{{ animation_order }}"
                      {{ block.shopify_attributes }}
                    >
                      {%- if block.settings.title != blank -%}
                        <h4 class="h3">{{ block.settings.title }}</h4>
                      {%- endif -%}

                      {%- if block.settings.text != blank -%}
                        {{ block.settings.text }}
                      {%- endif -%}
                    </div>

                  {%- when 'menu' -%}
                    {%- liquid
                      assign animation_order = animation_order | plus: 1

                      assign button_style = block.settings.button_style
                      if button_style == 'btn--text' and block.settings.show_arrow
                        assign button_style = button_style | append: ' btn--text-no-underline'
                      endif
                    -%}
                    <div
                      class="brick__block__menu"
                      data-aos="hero"
                      data-aos-anchor="{{ animation_anchor }}"
                      data-aos-order="{{ animation_order }}"
                      {{ block.shopify_attributes }}
                    >
                      {%- for link in linklists[block.settings.menu].links -%}
                        <a
                          href="{{ link.url }}"
                          class="btn {{ button_style }} {{ block.settings.button_size }} {{ block.settings.button_type }}"
                        >
                          <span>{{ link.title }}</span>

                          {%- if block.settings.show_arrow -%}
                            {%- render 'icon-arrow-right' -%}
                          {%- endif -%}
                        </a>
                      {%- endfor -%}
                    </div>

                  {%- when 'newsletter' -%}
                    {%- liquid
                      assign animation_order = animation_order | plus: 1

                      assign button_style = block.settings.button_style
                      if button_style == 'btn--text' and block.settings.show_arrow
                        assign button_style = button_style | append: ' btn--text-no-underline'
                      endif
                    -%}
                    <div
                      class="brick__block__newsletter"
                      data-aos="hero"
                      data-aos-anchor="{{ animation_anchor }}"
                      data-aos-order="{{ animation_order }}"
                      {{ block.shopify_attributes }}
                    >
                      {% form 'customer', style: style %}
                        {{ form.errors | default_errors }}
                        {% if form.posted_successfully? %}
                          <p>{{ 'password.form.success' | t }}</p>
                        {% else %}
                          <div id="CustomerSignup" class="form-customer-signup">
                            <input type="hidden" name="contact[tags]" value="prospect, password page">
                            <input
                              class="field"
                              type="email"
                              name="contact[email]"
                              id="email"
                              placeholder="{{ 'password.form.placeholder' | t }}"
                            >
                            <button
                              type="submit"
                              class="btn {{ button_style }} {{ block.settings.button_size }} {{ block.settings.button_type }} customer-signup-button"
                            >
                              <span>{{ 'password.buttons.sign_up' | t }}</span>

                              {%- if block.settings.show_arrow -%}
                                {%- render 'icon-arrow-right' -%}
                              {%- endif -%}
                            </button>

                            {% if content_for_header contains 'recaptcha' %}
                              <div class="form__legal">
                                {{ 'shopify.online_store.spam_detection.disclaimer_html' | t }}
                              </div>
                            {% endif %}
                          </div>
                        {% endif %}
                      {% endform %}
                    </div>

                  {%- when 'social-links' -%}
                    {%- assign animation_order = animation_order | plus: 1 -%}
                    <div
                      class="brick__block__social"
                      data-aos="hero"
                      data-aos-anchor="{{ animation_anchor }}"
                      data-aos-order="{{ animation_order }}"
                      {{ block.shopify_attributes }}
                    >
                      {%- render 'social-icons' -%}
                    </div>
                {%- endcase -%}
              {%- endfor -%}
            </div>
          {%- endif -%}

          {%- assign animation_order = animation_order | plus: 1 -%}
          <div
            class="brick__block__actions"
            data-aos="hero"
            data-aos-anchor="{{ animation_anchor }}"
            data-aos-order="{{ animation_order }}"
          >
            <div class="brick__block__links">
              <div class="brick__block__powered">
                <small>
                  {{- 'password.page.powered_by' | t }}
                  <a href="https://www.shopify.com/" rel="noopener" target="_blank" rel="nofollow">Shopify</a></small
                >
              </div>

              <div class="brick__block__links__row">
                <button type="button" data-popup-open class="text-link">
                  {{ 'password.buttons.store_login' | t }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <dialog
      data-password-modal
      aria-modal="true"
      aria-labelledby="{{ 'password.buttons.store_login' | t }}"
      class="modal__overlay modal__overlay--default modal__overlay--password"
    >
      <form method="dialog">
        <button class="visually-hidden no-js" aria-label="{{ 'general.accessibility.close' | t }}"></button>
      </form>

      <div class="modal__body modal--default modal--password" data-modal-body data-scroll-lock-scrollable>
        <div class="modal-section">
          <div class="modal-wrapper modal-wrapper--reverse modal-wrapper--no-image">
            <div class="modal__text">
              <div class="modal__inner">
                <h2 class="h3 modal__title">{{ 'password.buttons.store_login' | t }}</h2>

                {% form 'storefront_password', class: 'newsletter-form', style: style %}
                  {{ form.errors | default_errors }}
                  {% if form.posted_successfully? %}
                  {% else %}
                    <div id="AdminLogin" class="form-admin-login">
                      <div class="input-group">
                        <input
                          class="input-group__field"
                          type="password"
                          name="password"
                          id="password"
                          placeholder="{{ 'password.form.login' | t }}"
                        >
                        <button type="submit" class="caps newsletter__submit input-group__btn">
                          {{ 'password.buttons.login' | t }}
                        </button>
                      </div>
                    </div>
                  {% endif %}
                {% endform %}
              </div>
            </div>
          </div>
        </div>

        <button data-popup-close class="close" title="{{ 'general.accessibility.close' | t }}" autofocus>
          {%- render 'icon-cancel' -%}
        </button>
      </div>
    </dialog>
  </popup-component>
</section>

{% schema %}
{
  "name": "Password page",
  "class": "password",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo",
      "info": "300 x 90px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "range",
      "id": "logo_max_width",
      "label": "Logo width (in pixels)",
      "min": 50,
      "max": 400,
      "default": 200,
      "step": 10,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Featured image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "Hidden on mobile. 2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    }
  ],
  "blocks": [
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Header",
          "default": "Coming Soon"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Sign up for our newsletter to stay up to date on when we officially open</p>"
        }
      ]
    },
    {
      "type": "menu",
      "name": "Menu",
      "settings": [
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu"
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        }
      ]
    },
    {
      "type": "social-links",
      "name": "Social links"
    }
  ]
}
{% endschema %}
