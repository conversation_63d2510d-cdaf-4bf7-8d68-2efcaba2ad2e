<!-- /sections/article.liquid -->

{%- liquid
  assign article_url = article.url
  assign show_sidebar = false
  if section.blocks.size > 0
    assign show_sidebar = true
  endif

  assign show_date = section.settings.show_date
  assign show_author = section.settings.show_author
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign header_color = settings.menu_transparent_color
  assign selected_color_scheme = section.settings.color_scheme | strip
  assign scheme_bg_color = settings.color_schemes[selected_color_scheme].settings.section_bg

  assign transparent = false
  if scheme_bg_color == 'rgba(0,0,0,0)' or scheme_bg_color == blank
    assign transparent = true
  endif
-%}

{%- capture style -%}
  --PT: {{ section.settings.padding_top }}px;
  --PB: {{ section.settings.padding_bottom }}px;

  {%- unless scheme_bg_color == 'rgba(0,0,0,0)' or scheme_bg_color == blank -%}
    --bg: {{ scheme_bg_color }};
  {%- else -%}
    --bg: transparent;
  {%- endunless -%}
{%- endcapture -%}

<section
  class="article-single section-padding {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="article"
  style="{{ style }}"
>
  <div class="article__hero text-center">
    <div class="wrapper">
      <h2 class="article__title h2">{{ article.title }}</h2>

      {%- if show_date or show_author -%}
        <p class="article__meta">
          {%- assign published_at = article.published_at | date: format: 'full_date' -%}

          {%- if show_date -%}
            <time datetime="{{ published_at }}">{{ published_at }}</time>
          {%- endif -%}

          {%- if show_author -%}
            <span class="article__author">{{ 'blogs.article.by_author' | t: author: article.author }}</span>
          {%- endif -%}
        </p>
      {%- endif -%}

      {%- if section.settings.show_image and article.image -%}
        <div class="article__image {{ section.settings.height }} {{ section.settings.mobile_height }}">
          {%- render 'image-hero',
            image: article.image,
            sizes: '(min-width: 750px) 50vw, calc(100vw - 32px)',
            desktop_height: section.settings.height,
            mobile_height: section.settings.mobile_height,
            show_backfill: false
          -%}
        </div>
      {%- endif -%}
    </div>
  </div>

  <div class="wrapper article__wrapper">
    <div class="grid{% if show_sidebar %} grid--article{% else %} grid--1{% endif %}">
      <article class="article article--single">
        <div class="article__content__wrapper">
          <div class="article__content rte">
            {{ article.content }}
          </div>
        </div>

        {%- if article.user.bio and show_author -%}
          <div class="hr"></div>

          <div class="article__author-bio">
            {%- if article.user.image != blank -%}
              <div class="article__author__image">
                {%- render 'image',
                  image: article.user.image,
                  width: 160,
                  widths: '80, 120, 160, 240',
                  sizes: '80px'
                -%}
              </div>
            {%- endif -%}

            <div class="article__author__text">
              <h3>
                {{ article.user.first_name }}
                {{ article.user.last_name }}
              </h3>

              <p class="rte">{{ article.user.bio | newline_to_br }}</p>
            </div>
          </div>
        {%- endif -%}

        {%- if section.settings.tags -%}
          <h4 class="article__tags h3">
            {%- for tag in article.tags -%}
              {%- if forloop.index0 == 0 -%}
                <span class="label">{{ 'blogs.article.tags' | t }}: </span>
              {%- endif -%}
              <a
                href="{{ blog.url }}/tagged/{{ tag | handleize }}"
                title="{{ blog.title | strip_html | escape }} tagged {{ tag | strip_html | escape }}"
              >
                {{- tag -}}
              </a>
              {%- unless forloop.last %},{% endunless %}
            {%- endfor -%}
          </h4>
        {%- endif -%}

        {%- if section.settings.show_sharing -%}
          {%- assign share_url = article.url | prepend: request.origin -%}
          {%- render 'share-button', share_url: share_url %}
        {%- endif -%}

        {%- if section.settings.arrows -%}
          {%- if blog.next_article or blog.previous_article -%}
            <div class="hr"></div>

            <aside class="clearfix">
              {%- if blog.next_article -%}
                <small class="right">
                  {%- for post in blog.articles -%}
                    {%- if post.url == blog.next_article -%}
                      <a
                        href="{{ post.url }}"
                        class="btn btn--text btn--text-no-underline btn--small"
                        title="{{ post.title | strip_html | escape }}"
                      >
                        <span>{{ 'blogs.article.newer_post' | t }}</span>

                        {%- render 'icon-arrow-right' -%}
                      </a>
                    {%- endif -%}
                  {%- endfor -%}
                </small>
              {%- endif -%}

              {%- if blog.previous_article -%}
                <small class="left">
                  {%- for post in blog.articles -%}
                    {%- if post.url == blog.previous_article -%}
                      <a
                        href="{{ post.url }}"
                        class="btn btn--text btn--text-no-underline btn--small"
                        title="{{ post.title | strip_html | escape }}"
                      >
                        {%- render 'icon-arrow-left' -%}

                        <span>{{ 'blogs.article.older_post' | t }}</span>
                      </a>
                    {%- endif -%}
                  {%- endfor -%}
                </small>
              {%- endif -%}
            </aside>
          {%- endif -%}
        {%- endif -%}
      </article>
      <!-- end article -->

      {%- if show_sidebar -%}
        <aside class="sidebar">
          <div class="sidebar__contents">
            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when '@app' -%}
                  {%- render block -%}
                {%- when 'links' -%}
                  <div class="widget widget--categories" {{ block.shopify_attributes }}>
                    {%- if block.settings.blog_linklist != blank -%}
                      <collapsible-elements>
                        {%- assign list_html = '' -%}

                        {%- for link in linklists[block.settings.blog_linklist].links -%}
                          {%- assign current_blog = link.object -%}
                          {%- assign open_submenu = false -%}
                          {%- assign current_blog_articles = '' -%}

                          {%- capture list_html -%}
                            {{ list_html }}

                            {%- if current_blog.articles.size > 0 -%}
                              {%- for article in current_blog.articles limit: 5 -%}
                                {%- if article_url == article.url and open_submenu == false -%}
                                  {%- assign open_submenu = true -%}
                                {%- endif -%}

                                {%- capture current_blog_articles -%}
                                  {{ current_blog_articles }}

                                  <li>
                                    <a href="{{ article.url }}"
                                      title="{{ article.title | strip_html | escape }}"
                                      class="widget__link">{{ article.title }}</a>
                                  </li>
                                {%- endcapture -%}
                              {%- endfor -%}
                            {%- endif -%}

                            <li data-slide="{{ forloop.index0 }}">
                              {% if current_blog_articles != '' %}
                                <details class="widget__link" data-collapsible{% if open_submenu %} open="true"{% endif %}>
                                  <summary class="widget__accordion-title" data-collapsible-trigger>
                                    {{- link.title -}}

                                    {%- render 'icon-plus' -%}
                                    {%- render 'icon-minus' -%}
                                  </summary>

                                  <div data-collapsible-body{% if open_submenu %} style="height: auto;"{% endif %}>
                                    <div class="submenu" data-collapsible-content>
                                      {{ current_blog_articles }}

                                      {% comment %}
                                        If blog posts are more than 5 then show 'See more...' link
                                      {% endcomment %}
                                      {%- if current_blog.articles.size > 5 -%}
                                        <li>
                                          <a href="{{ link.url }}"
                                            title="{{ link.title | strip_html | escape }}"
                                            class="widget__link">{{ 'blogs.sidebar.see_more' | t }}</a>
                                        </li>
                                      {%- endif -%}
                                    </div>
                                  </div>
                                </details>
                              {%- else -%}
                                <a href="{{ link.url }}" title="{{ link.title | strip_html | escape }}" class="widget__link">
                                  {{- link.title -}}
                                </a>
                              {%- endif -%}
                            </li>
                          {%- endcapture -%}
                        {%- endfor -%}

                        {%- if list_html != '' -%}
                          {%- if block.settings.title != blank -%}
                            <h3 class="widget__title h4">{{ block.settings.title }}</h3>
                          {%- endif -%}

                          <ul class="widget__links widget__links--primary">
                            {{ list_html }}
                          </ul>
                        {%- endif -%}
                    {%- elsif blog.all_tags.size > 0 -%}
                      {%- if block.settings.title != blank -%}
                        <h3 class="widget__title h4">{{ block.settings.title }}</h3>
                      {%- endif -%}

                      <ul class="widget__links widget__links--primary">
                        {%- for tag in blog.all_tags -%}
                          <li
                            {% if article.tags contains tag %}
                              class="widget__links--active"
                            {% endif %}
                            data-slide="{{ forloop.index0 }}"
                          >
                            <a class="widget__link" href="{{ blog.url }}/tagged/{{ tag | handle }}">{{ tag }}</a>
                          </li>
                        {%- endfor -%}
                      </ul>
                      </collapsible-elements>
                    {%- endif -%}
                  </div>

                {%- when 'related' -%}
                  {% comment %}
                    Extract product handles from links in article content
                  {% endcomment %}
                  {%- assign article_content = article.content | split: '/products/' -%}

                  {%- if article_content.size > 0 and block.settings.product_list.count < 1 -%}
                    {%- assign starts_with_handle = '' -%}

                    {%- for item in article_content -%}
                      {% comment %}
                        The first item is empty, subsequent items start with a product handle
                        Split on URL encoded string to handle params
                      {% endcomment %}
                      {%- unless forloop.index0 == 0 -%}
                        {%- assign starts_with_handle = item | url_encode | split: '%' -%}
                        {%- assign handles = handles | append: starts_with_handle[0] | append: ',' -%}
                      {%- endunless -%}
                    {%- endfor -%}
                  {%- endif -%}

                  {%- assign handles_arr = handles | split: ',' | compact | uniq -%}

                  <div class="widget" {{ block.shopify_attributes }}>
                    {%- if handles_arr.size > 0 or block.settings.product_list.count > 0 -%}
                      {%- if block.settings.title != blank -%}
                        <h3 class="widget__title h4">{{ block.settings.title }}</h3>
                      {%- endif -%}

                      <div class="widget__products">
                        {%- liquid
                          if block.settings.product_list.count > 0
                            for product in block.settings.product_list
                              render 'product-column-item', product: product
                            endfor
                          elsif handles_arr.size > 0
                            for handle in handles_arr limit: 20
                              assign product = all_products[handle]

                              if product.id != ''
                                render 'product-column-item', product: product
                              endif
                            endfor
                          endif
                        -%}
                      </div>
                    {%- endif -%}
                  </div>
                {%- when 'recent' -%}
                  {%- liquid
                    assign counter = 0
                    assign article_limit = 3
                    assign recent_articles = ''
                  -%}

                  {%- for recent_article in blog.articles -%}
                    {%- if recent_article.handle == article.handle -%}
                      {%- continue -%}
                    {%- endif -%}

                    {%- capture recent_markup -%}
                      <article class="article">
                        {%- if recent_article.image != blank -%}
                          <div class="article__image__outer">
                            <div class="article__image">
                              <a class="article__image-link" href="{{ recent_article.url }}" aria-label="{{ recent_article.title | strip_html | escape }}">
                                {%- render 'image' image: recent_article.image, widths: '320, 480, 560, 640, 780, 960, 1280, 1440, 1960', sizes: '(min-width: 750px) 320px, calc(100vw - 32px)' -%}
                              </a>
                            </div>
                          </div>
                        {%- endif -%}

                        <div class="article__text-wrapper">
                          <h2 class="article__title h4">
                            <a href="{{ recent_article.url }}" title="{{ recent_article.title | strip_html | escape }}">
                              {{ recent_article.title }}
                            </a>
                          </h2>

                          {%- if show_date or show_author -%}
                            <p class="article__meta">
                              {%- if show_date -%}
                                {%- assign published_at = recent_article.published_at | date: format: 'full_date' -%}
                                <time datetime="{{ published_at }}">{{ published_at }}</time>
                              {%- elsif show_author -%}
                                <span class="article__meta__author">{{ 'blogs.article.by_author' | t: author: article.author }} </span>
                              {%- endif -%}
                            </p>
                          {%- endif -%}
                        </div>
                      </article>
                    {%- endcapture -%}

                    {%- assign recent_articles = recent_articles | append: recent_markup -%}
                    {%- assign counter = counter | plus: 1 -%}

                    {%- if counter == article_limit -%}
                      {%- break -%}
                    {%- endif -%}
                  {%- endfor -%}

                  <div class="widget" {{ block.shopify_attributes }}>
                    {%- if recent_articles != '' -%}
                      {%- if block.settings.title != blank -%}
                        <h3 class="widget__title h4">{{ block.settings.title }}</h3>
                      {%- endif -%}

                      <div class="widget__recent">
                        {{ recent_articles }}
                      </div>
                    {%- endif -%}
                  </div>
                {%- when 'image' -%}
                  {%- if article.image -%}
                    <div class="widget widget--image" {{ block.shopify_attributes }}>
                      {%- if block.settings.title != blank -%}
                        <h3 class="widget__title h4">{{ block.settings.title }}</h3>
                      {%- endif -%}

                      <div class="widget__image">
                        {%- render 'image',
                          image: article.image,
                          widths: '320, 480, 560, 640, 780, 960, 1280, 1440, 1960',
                          sizes: '(min-width: 750px) 320px, calc(100vw - 32px)'
                        -%}
                      </div>
                    </div>
                  {%- endif -%}
              {%- endcase -%}
            {%- endfor -%}
          </div>
        </aside>
        <!-- end sidebar -->
      {%- endif -%}
    </div>

    {%- if blog.comments_enabled? -%}
      <div class="grid{% if show_sidebar %} grid--article{% else %} grid--1{% endif %}">
        <div class="comments-container">
          {% paginate article.comments by 5 %}
            {% if article.comments_count > 0 %}
              <div class="comments" id="comments">
                <div class="hr"></div>
                {% assign number_of_comments = article.comments_count %}
                <h3 class="h4">{{ 'blogs.comments.comments_with_count' | t: count: number_of_comments }}</h3>
                <div class="hr"></div>
                <!-- a "just published" comment -->
                {% if comment and comment.created_at %}
                  <div id="comment-{{ comment.id }}" class="comment">
                    <div class="comment-body">
                      <div class="comment-meta">
                        {% assign comment_date = comment.created_at | date: format: 'full_date' %}
                        <span class="author">{{ comment.author }}</span>
                        <span class="label"
                          ><time datetime="{{ comment_date }}">{{ comment_date }}</time></span
                        >
                      </div>
                      {{ comment.content }}
                    </div>
                  </div>
                {% endif %}
                <!-- previous comments -->
                {% for comment in article.comments %}
                  <div id="comment-{{ comment.id }}" class="comment">
                    <div class="comment-body">
                      <div class="comment-meta">
                        {% assign comment_date = comment.created_at | date: format: 'full_date' %}
                        <span class="author">{{ comment.author }}</span>
                        <span class="label"
                          ><time datetime="{{ comment_date }}">{{ comment_date }}</time></span
                        >
                      </div>
                      {{ comment.content }}
                    </div>
                  </div>
                {% endfor %}
              </div>
            {% endif %}

            {% if paginate.pages > 1 %}
              <div class="paginate">
                {% if paginate.previous %}
                  <span class="left">{{ '&larr;' | link_to: paginate.previous.url }}</span>
                {% endif %}
                {% for part in paginate.parts %}
                  {% if part.is_link %}
                    <span class="num">{{ part.title | link_to: part.url }}</span>
                  {% else %}
                    <span class="num current">{{ part.title }}</span>
                  {% endif %}
                {% endfor %}
                {% if paginate.next %}
                  <span class="right">{{ '&rarr;' | link_to: paginate.next.url }}</span>
                {% endif %}
              </div>
              <div class="hr"></div>
            {% endif %}

            <div class="comments-form">
              <div class="hr"></div>
              <h3 class="h4">{{ 'blogs.comments.title' | t }}</h3>
              <div class="hr"></div>
              {% form article %}
                {% if form.posted_successfully? %}
                  {% if blog.moderated? %}
                    <p class="flash">
                      <em>{{ 'blogs.comments.success_moderated' | t }}</em>
                    </p>
                  {% else %}
                    <p class="flash">
                      <em>{{ 'blogs.comments.success' | t }}</em>
                    </p>
                  {% endif %}
                {% endif %}
                {% if form.errors %}
                  <p class="error">{{ 'general.forms.post_error' | t }}</p>
                {% endif %}
                <div class="form-row">
                  <div class="form-field">
                    <label for="comment_author">{{ 'blogs.comments.name' | t }}</label>
                    <input
                      type="text"
                      id="comment_author"
                      name="comment[author]"
                      value="{{ form.author }}"
                      placeholder="{{ 'blogs.comments.name' | t }}"
                    >
                  </div>
                  <div class="form-field">
                    <label for="comment_email">{{ 'blogs.comments.email' | t }}</label>
                    <input
                      type="text"
                      id="comment_email"
                      name="comment[email]"
                      value="{{ form.email }}"
                      placeholder="{{ 'blogs.comments.email' | t }}"
                    >
                  </div>
                </div>
                <div class="form-field">
                  <label for="comment_body">{{ 'blogs.comments.comment' | t }}</label>
                  <textarea
                    id="comment_body"
                    name="comment[body]"
                    rows="5"
                    placeholder="{{ 'blogs.comments.comment' | t }}"
                  >{{ form.body }}</textarea>
                </div>

                <div class="form-field">
                  <div class="form__legal">
                    {{ 'shopify.online_store.spam_detection.disclaimer_html' | t }}
                  </div>
                  {% if blog.moderated? %}
                    <p>
                      <em>{{ 'blogs.comments.moderated' | t }}</em>
                    </p>
                  {% endif %}

                  <input
                    type="submit"
                    value="{{ 'blogs.comments.post' | t }}"
                    id="comment-submit"
                    class="btn btn--primary btn--solid"
                  >

                  {% if form.errors %}
                    <script>
                      window.location.hash = '#add-comment-title';
                    </script>
                  {% endif %}

                  {% if form.posted_successfully? %}
                    <script>
                      window.location.hash = '#comments';
                    </script>
                  {% endif %}
                </div>
              {% endform %}
            </div>
          {% endpaginate %}
        </div>
      </div>
      <!-- end comments -->
    {%- endif -%}
  </div>
</section>

<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Article",
    "articleBody": {{ article.content | strip_html | json }},
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": {{ shop.url | append: page.url | json }}
    },
    "headline": {{ article.title | json }},
    {% if article.excerpt != blank %}
      "description": {{ article.excerpt | strip_html | json }},
    {% endif %}
    {% if article.image %}
      "image": [
        {{ article | image_url: article.image.width | prepend: "https:" | json }}
      ],
    {% endif %}
    "datePublished": {{ article.published_at | date: '%Y-%m-%dT%H:%M:%SZ' | json }},
    "dateCreated": {{ article.created_at | date: '%Y-%m-%dT%H:%M:%SZ' | json }},
    "author": {
      "@type": "Person",
      "name": {{ article.author | json }}
    },
    "publisher": {
      "@type": "Organization",
      "name": {{ shop.name | json }}
    }
  }
</script>

{% schema %}
{
  "name": "Article page",
  "class": "section-overlay-header",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "label": "Show image",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "label": "Show date",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "label": "Show author",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "tags",
      "label": "Show article tags",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_sharing",
      "label": "Show sharing button",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "arrows",
      "label": "Show navigation arrows",
      "default": false
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "height",
      "label": "Height",
      "default": "screen-height-one-third",
      "options": [
        {"value": "image-height", "label": "Image height"},
        {"value": "screen-height-full", "label": "Full screen height"},
        {"value": "screen-height-three-quarters", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds", "label": "2/3 of screen"},
        {"value": "screen-height-one-half", "label": "1/2 of screen"},
        {"value": "screen-height-one-third", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero", "label": "750px"},
        {"value": "sixty-fifty-height-hero", "label": "650px"},
        {"value": "five-fifty-height-hero ", "label": "550px"},
        {"value": "four-fifty-height-hero", "label": "450px"}
      ]
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Height",
      "default": "screen-height-one-third--mobile",
      "options": [
        {"value": "image-height--mobile", "label": "Image height"},
        {"value": "screen-height-full--mobile", "label": "Full screen height"},
        {"value": "screen-height-three-quarters--mobile", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds--mobile", "label": "2/3 of screen"},
        {"value": "screen-height-one-half--mobile", "label": "1/2 of screen"},
        {"value": "screen-height-one-third--mobile", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero--mobile", "label": "750px"},
        {"value": "sixty-fifty-height-hero--mobile", "label": "650px"},
        {"value": "five-fifty-height-hero--mobile", "label": "550px"},
        {"value": "four-fifty-height-hero--mobile", "label": "450px"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 50
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "links",
      "name": "Navigation",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Categories"
        },
        {
          "type": "link_list",
          "id": "blog_linklist",
          "label": "Custom navigation",
          "info": "Overrides tag navigation"
        }
      ]
    },
    {
      "type": "related",
      "name": "Related products",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Related products",
          "info": "Products linked in the article body appear as Related products"
        },
        {
          "type": "product_list",
          "id": "product_list",
          "label": "Products",
          "limit": 5
        }
      ]
    },
    {
      "type": "recent",
      "name": "Recent articles",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Recent articles"
        }
      ]
    },
    {
      "type": "image",
      "name": "Default image",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading"
        }
      ]
    }
  ]
}
{% endschema %}
