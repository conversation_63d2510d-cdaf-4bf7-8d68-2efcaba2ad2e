{% comment %}
  Renders the content of section-columns, section-multicolumn, section-text-row

  Accepts:
  - type: {String} product (optional)

  Usage:
  {% render 'multicolumn', type: 'images' %}
{% endcomment %}

{%- liquid
  assign section_name = 'AccordionGroup'
  assign section_id = section_name | append: '--' | append: section.id
  assign animation_order = 0
  assign color_scheme = 'color-' | append: section.settings.color_scheme

  assign type = type | default: ''
  assign animation_order = 0
  assign animation_anchor = section_id | prepend: '#'
  assign heading = section.settings.title
  assign description = section.settings.description
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign blocks_size = section.blocks.size

  assign layout = section.settings.layout
  assign layout_mobile = section.settings.layout_mobile
  assign grid_columns = section.settings.grid

  assign multicolumn = true
  if layout
    assign multicolumn = false
  endif

  assign enable_slider = false
  if layout == 'slider'
    assign enable_slider = true
  endif

  if multicolumn
    assign column_count = blocks_size | at_least: 2
    assign column_count_float = column_count | times: 1.000000
    assign column_width = 100 | divided_by: column_count_float
    assign one_third_blocks_only = false
    assign one_third_block_count = 0

    for block in section.blocks
      if block.settings.column_width == 33
        assign one_third_block_count = one_third_block_count | plus: 1
      endif
    endfor

    if one_third_block_count == blocks_size
      assign one_third_blocks_only = true
    endif
  else
    assign columns_desktop = grid_columns | plus: 0
    assign columns_medium = 3
    assign columns_small = 2

    if columns_desktop == 2 or columns_desktop == 4
      assign columns_medium = 2
    endif

    assign column_width = 100 | divided_by: columns_desktop
  endif

  assign columns_mobile = layout_mobile | plus: 0

  if heading != blank or description != blank
    assign has_content = true
  endif

  assign eager_images_limit = grid_columns | default: 3

  if type == 'images'
    if grid_columns == 4
      assign eager_images_limit = grid_columns | times: 2
    endif
  endif

  assign min_image_width = section.blocks | map: 'settings' | map: 'image_width' | compact | sort | first | default: 100
  assign max_image_width = section.blocks | map: 'settings' | map: 'image_width' | compact | sort | last | default: 100

  if min_image_width == max_image_width
    assign all_widths_same = true
  endif

  capture layout_class
    echo 'grid'

    if multicolumn
      echo ' multicolumn'
    endif

    if layout == 'slider'
      echo ' grid--slider'
    endif

    if layout_mobile == 'slider'
      echo ' grid--mobile-slider'
    else
      echo ' grid--mobile-vertical'
    endif
  endcapture
-%}

{%- style -%}
  #{{ section_id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;

    {%- unless multicolumn or enable_slider -%}
      --COLUMNS: {{ columns_desktop }};
      --COLUMNS-MEDIUM: {{ columns_medium }};
      --COLUMNS-SMALL: {{ columns_small }};
    {%- endunless -%}

    --COLUMNS-MOBILE: {{ columns_mobile }};
  }
{%- endstyle -%}

<div
  id="{{ section_id }}"
  class="section-columns custom-multicolumn section-padding {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="columns"
>
  <div class="{{ section.settings.width }}">
    
    {% comment %} ----- Header ----- {% endcomment %}

    {%- capture section_header -%}

      {%- if section.settings.title != blank or section.settings.subheading != blank -%}
        <div class="section-header__text">

          {%- if section.settings.eyebrow != blank -%}
            <p class="section-header__eyebrow {{ section.settings.eyebrow_font_size }}">
              {{ section.settings.eyebrow }}
            </p>
          {%- endif -%}

          {%- if section.settings.title != blank -%}
            {%- liquid
              assign heading_tag = section.settings.heading_tag
            -%}
            <{{ heading_tag }} class="section-header__title {{ section.settings.title_font_size }}">
              {{ section.settings.title }}
            </{{ heading_tag }}>
          {%- endif -%}

        </div>
      {%- endif -%}

      {%- if section.settings.header_button_text != blank -%}
        <div class="section-header__actions">
          <a href="{{ section.settings.header_button_link | default: "#" }}"
             class="btn {{ section.settings.header_button_style }} {{ section.settings.header_button_size }} {{ section.settings.header_button_type }}"
          >
            <span>{{ section.settings.header_button_text }}</span>
          </a>
        </div>
      {%- endif -%}

    {%- endcapture -%}

    {%- if section_header != blank -%}
      {%- capture section_header -%}
        <div class="section-header-wrapper">
          <div class="section-header section-header--{{ section.settings.header_layout }}">
            {{ section_header }}
          </div>
        </div>
      {%- endcapture -%}
    {%- endif -%}

    {{ section_header }}

    {%- if blocks_size > 0 -%}
      {%- capture columns_content -%}
        <div class="{{ layout_class }} {{ section.settings.align_columns }} {{ section.settings.column_align_text }}"
          {% if enable_slider %}
            data-grid-slider
          {% endif %}

          {% if request.design_mode %}
            data-block-scroll
          {% endif %}
        >
          {%- for block in section.blocks -%}
            {%- liquid
              assign animation_order = animation_order | plus: 1
              assign column_width = block.settings.column_width | default: column_width
              assign title = block.settings.title
              assign text = block.settings.text
              assign image = block.settings.image
              assign image_width = block.settings.image_width | default: 100
              assign image_aspect_ratio = block.settings.image_aspect_ratio | default: section.settings.image_aspect_ratio | default: 1
              assign image_aspect_ratio_mobile = block.settings.mobile_image_aspect_ratio | default: section.settings.mobile_image_aspect_ratio | default: aspect_ratio
              assign aspect_ratio = 1 | divided_by: image_aspect_ratio
              assign aspect_ratio_mobile = 1 | divided_by: image_aspect_ratio_mobile
              assign button_text = block.settings.button_text
              assign icon_color = block.settings.icon_color
              assign icon_size = block.settings.icon_size

              if forloop.index > eager_images_limit and layout != 'slider' or forloop.index > 4 and layout == 'slider'
                assign loading = 'lazy'
              endif

              if one_third_blocks_only or enable_slider
                assign column_width = 33.333333
              endif

              assign column_width_multiplier = column_width | times: 0.01

              assign button_style = block.settings.button_style
              if button_style == 'btn--text' and block.settings.show_arrow
                assign button_style = button_style | append: ' btn--text-no-underline'
              endif

              capture image_shape_class
                case block.settings.image_shape
                  when 'circle'
                    echo ' column__image--circle'

                  when 'rounded'
                    echo ' column__image--rounded'

                  when 'blob-one'
                    echo ' column__image--blob-one'

                  when 'blob-two'
                    echo ' column__image--blob-two'

                  when 'blob-three'
                    echo ' column__image--blob-three'
                endcase
              endcapture

              capture image_sizes
                echo '(min-width: 990px) calc((100vw - 100px) * ' | append: column_width_multiplier | append: '), (min-width: 750px) calc((100vw - 60px) / 2), calc(100vw - 32px - 50px)'
              endcapture

              capture block_style
                echo 'style="'

                if multicolumn
                  echo '--desktop-width: ' | append: column_width | append: '%;'
                endif

                if block.type == 'image'
                  echo '--image-width: ' | append: image_width | append: '%;'
                endif

                if icon_color.alpha != 0.0 and icon_color != blank
                  echo ' --icons: ' | append: icon_color | append: ';'
                endif

                echo '"'
              endcapture
            -%}

            {%- case block.type -%}
              {%- when 'image' -%}
                <div class="grid-item"
                  {{ block_style }}
                  {% if enable_slider %}
                    data-grid-item
                  {% endif %}
                  {{ block.shopify_attributes }}
                >
                  <div class="column__inner"
                    data-aos="hero"
                    data-aos-anchor="{{ animation_anchor }}"
                    data-aos-order="{{ animation_order }}">
                    <div class="column__image{{ image_shape_class }}" data-column-image>

                      {%- assign class_desktop = 'block-radius overflow-hidden' -%}
                      {%- assign class_mobile = 'block-radius overflow-hidden' -%}

                      {%- if aspect_ratio != aspect_ratio_mobile -%}
                        {%- assign class_desktop = class_desktop | append: ' desktop' -%}
                        {%- assign class_mobile = class_mobile | append: ' mobile' -%}
                      {%- endif -%}
                      
                      {%- capture column_image -%}

                        {%- capture overlay_text -%}
                          
                        {%- endcapture -%}

                        {%- capture overlay_inner -%}

                          {%- if overlay_text != blank -%}
                            {{ overlay_text }}
                          {%- endif -%}

                          {%- capture link_tag -%}
                            {%- if block.settings.image_button_url != blank and overlay_text == blank -%}
                              div
                            {%- else -%}
                              a
                            {%- endif -%}
                          {%- endcapture -%}

                          {%- capture link_attributes -%}
                            {%- if block.settings.image_button_url != blank and overlay_text == blank -%}
                              tabindex="0"
                            {%- else -%}
                              href="{{ block.settings.image_button_text }}"
                            {%- endif -%}
                          {%- endcapture -%}

                          {%- if block.settings.image_button_text != blank -%}
                            <div class="overlay__btn">
                              <{{ link_tag }} {{ link_attributes }} href="{{ block.settings.image_button_url | default: '#!' }}" class="btn {{ block.settings.image_button_style }} {{ block.settings.image_button_size }} {{ block.settings.image_button_type }} {% if block.settings.image_button_fullwidth == true %}btn--full{% endif %}" aria-label="{{ block.settings.image_button_text }}">
                                <span>{{ block.settings.image_button_text }}</span>
                              </{{ link_tag }}>
                            </div>
                          {%- endif -%}

                        {%- endcapture -%}

                        {%- if overlay_inner != blank -%}

                          {%- capture overlay_tag -%}
                            {%- if block.settings.image_button_url != blank and overlay_text == blank -%}
                              a
                            {%- else -%}
                              div
                            {%- endif -%}
                          {%- endcapture -%}

                          {%- capture overlay_attributes -%}
                            {%- if block.settings.image_button_url != blank and overlay_text == blank -%}
                              href="{{ block.settings.image_button_url }}"
                            {%- else -%}
                              
                            {%- endif -%}
                          {%- endcapture -%}

                          <{{ overlay_tag }} {{ overlay_attributes }} class="column__image-overlay overlay justify--{{ block.settings.image_overlay_alignment }}">
                            {{ overlay_inner }}
                          </{{ overlay_tag }}>

                        {%- endif -%}

                        {%- if image -%}
                          {%- render 'image' image: image, sizes: image_sizes, aspect_ratio: aspect_ratio, loading: loading, modifier: class_desktop -%}
                          {%- if aspect_ratio != aspect_ratio_mobile -%}
                            {%- render 'image' image: image, sizes: image_sizes, aspect_ratio: aspect_ratio_mobile, loading: loading, modifier: class_mobile -%}
                          {%- endif -%}
                        {%- else -%}
                          {%- render 'image' placeholder: 'image', aspect_ratio: aspect_ratio, modifier: class_desktop -%}
                          {%- if aspect_ratio != aspect_ratio_mobile -%}
                            {%- render 'image' placeholder: 'image', aspect_ratio: aspect_ratio_mobile, modifier: class_mobile -%}
                          {%- endif -%}
                        {%- endif -%}

                      {%- endcapture -%}

                      {%- if block.settings.button_url != blank and block.settings.button_text == blank -%}
                        <a href="{{ block.settings.button_url }}" aria-label="{{ image.alt | default: title | default: text | strip_html | escape }}">
                          {{ column_image }}
                        </a>
                      {%- else -%}
                        {{ column_image }}
                      {%- endif -%}
                    </div>

                    {%- capture column_content -%}
                      {%- if title != blank -%}
                        {%- liquid
                          assign heading_tag = 'h2'

                          unless block.settings.heading_tag == 'automatic' or block.settings.heading_tag == blank
                            assign heading_tag = block.settings.heading_tag
                          endunless
                        -%}

                        <{{ heading_tag }} class="column__heading {{ section.settings.column_align_heading }} {{ block.settings.heading_font_size }}">
                          {{- title -}}
                        </{{ heading_tag }}>
                      {%- endif -%}

                      {%- if text != blank -%}
                        <div class="column__text rte {{ block.settings.text_font_size }}">
                          {{- text -}}
                        </div>
                      {%- endif -%}

                      {%- if block.settings.button_text != blank -%}
                        <div class="column__btn">
                          <a href="{{ block.settings.button_url | default: '#!' }}" class="btn {{ button_style }} {{ block.settings.button_size }} {{ block.settings.button_type }}" aria-label="{{ block.settings.button_text }}">
                            <span>{{ block.settings.button_text }}</span>

                            {%- if block.settings.show_arrow -%}
                              {%- render 'icon-arrow-right' -%}
                            {%- endif -%}
                          </a>
                        </div>
                      {%- endif -%}
                    {%- endcapture -%}

                    {%- if column_content != blank -%}
                      <div class="column__content">
                        {{ column_content }}  
                      </div>
                    {%- endif -%}
                  </div>
                </div>

              {%- when 'icon' -%}
                {%- capture icon_style -%}
                  --icon-size: {{ icon_size }}px;

                  {%- if title != blank -%}
                    --icon-line-height: calc(1.2 * var(--font-{{ block.settings.heading_font_size }}));
                  {%- elsif text != blank-%}
                    --icon-line-height: calc(1.5 * var(--font-{{ block.settings.text_font_size }}));
                  {%- endif -%}
                {%- endcapture -%}

                <div class="grid-item"
                  {{ block_style }}
                  {% if enable_slider %}
                    data-grid-item
                  {% endif %}
                  {{ block.shopify_attributes }}
                >
                  <div class="column__inner"
                    data-aos="hero"
                    data-aos-anchor="{{ animation_anchor }}"
                    data-aos-order="{{ animation_order }}">
                    <div class="column__icon {{ block.settings.icon_alignment | default: section.settings.icon_alignment }}" style="{{ icon_style }}">
                      <div class="icon__animated{% if image != blank %} icon__animated--image{% endif %}">
                        {%- liquid
                          if image
                            assign icon_width = icon_size
                            assign icon_width_retina = icon_width | times: 2
                            assign icon_sizes = icon_width | append: 'px'
                            assign icon_widths = icon_width | append: ', ' | append: icon_width_retina

                            render 'image' image: image, width: icon_width_retina, sizes: icon_sizes, widths: icon_widths, loading: loading
                          else
                            render 'animated-icon', filename: block.settings.icon_name
                          endif
                        -%}
                      </div>

                      {%- capture column_content -%}
                        {%- if title != blank -%}
                          {%- liquid
                            assign heading_tag = 'h2'

                            unless block.settings.heading_tag == 'automatic' or block.settings.heading_tag == blank
                              assign heading_tag = block.settings.heading_tag
                            endunless
                          -%}

                          <{{ heading_tag }} class="column__heading {{ section.settings.column_align_heading }} {{ block.settings.heading_font_size }}">
                            {{ title }}
                          </{{ heading_tag }}>
                        {%- endif -%}

                        {%- if text != blank -%}
                          <div class="column__text rte {{ block.settings.text_font_size }}">
                            {{ text }}
                          </div>
                        {%- endif -%}
                      {%- endcapture -%}

                      {%- if column_content != blank -%}
                        <div class="column__content">
                          {{ column_content }}
                        </div>
                      {%- endif -%}

                    </div>
                  </div>
                </div>

              {%- when 'text' -%}
                <div class="grid-item"
                  {{ block_style }}
                  {% if enable_slider %}
                    data-grid-item
                  {% endif %}
                  {{ block.shopify_attributes }}
                >
                  <div class="column__inner"
                    data-aos="hero"
                    data-aos-anchor="{{ animation_anchor }}"
                    data-aos-order="{{ animation_order }}">

                    {%- capture column_content -%}
                      {%- if title != blank -%}
                        {%- liquid
                          assign heading_tag = 'h2'

                          unless block.settings.heading_tag == 'automatic' or block.settings.heading_tag == blank
                            assign heading_tag = block.settings.heading_tag
                          endunless
                        -%}

                        <{{ heading_tag }} class="column__heading {{ section.settings.column_align_heading }} {{ block.settings.heading_font_size }}">
                          {{ title }}
                        </{{ heading_tag }}>
                      {%- endif -%}

                      {%- if text != blank -%}
                        <div class="column__text rte {{ block.settings.text_font_size }}">
                          {{ text }}
                        </div>
                      {%- endif -%}

                      {%- if button_text != blank -%}
                        <div class="column__btn">
                          <a href="{{ block.settings.button_url | default: '#!' }}" class="btn {{ button_style }} {{ block.settings.button_size }} {{ block.settings.button_type }}" aria-label="{{ button_text }}">
                            <span>{{ button_text }}</span>

                            {%- if block.settings.show_arrow -%}
                              {%- render 'icon-arrow-right' -%}
                            {%- endif -%}
                          </a>
                        </div>
                      {%- endif -%}
                    {%- endcapture -%}

                    {%- if column_content != blank -%}
                      <div class="column__content">
                        {{ column_content }}  
                      </div>
                    {%- endif -%}
                  </div>
                </div>

              {%- when 'menu' -%}
                {%- assign menu_linklist = block.settings.menu | default: 'main-menu' -%}

                <div class="grid-item" {{ block_style }} {{ block.shopify_attributes }}>
                  <div class="column__inner"
                    data-aos="hero"
                    data-aos-anchor="{{ animation_anchor }}"
                    data-aos-order="{{ animation_order }}">
                    <div class="column__links font-heading">
                      {%- for link in linklists[menu_linklist].links -%}
                        <div class="column__links__item {{ block.settings.heading_font_size }}">
                          <a href="{{ link.url }}">{{ link.title }}</a>
                          {%- render 'superscript', link_collection: link -%}
                        </div>
                      {%- endfor -%}
                    </div>
                  </div>
                </div>

              {%- when 'product' -%}
                {%- liquid
                  capture product_attributes
                    echo 'data-aos="hero" '
                    echo 'data-aos-anchor="' | append: animation_anchor | append: '" '
                    echo 'data-aos-order="' | append: animation_order | append: '" '
                    echo block_style
                    echo block.shopify_attributes
                  endcapture

                  assign product = all_products[block.settings.product]
                  if product != blank
                    render 'product-grid-item', product: product, index: forloop.index, attributes: product_attributes
                  else
                    render 'onboarding-product-grid-item', index: forloop.index, attributes: product_attributes
                  endif
                -%}

            {%- endcase -%}
          {%- endfor -%}
        </div>
      {%- endcapture -%}

      {%- if enable_slider -%}
        <grid-slider
          align-arrows
          {% unless all_widths_same %}
            images-widths-different
          {% endunless %}
        >
          {{ columns_content }}
        </grid-slider>
      {%- else -%}
        {{ columns_content }}
      {%- endif -%}
    {%- endif -%}

    {%- if blocks_size == 0 -%}
      {%- render 'no-blocks' -%}
    {%- endif -%}
  </div>
</div>

{% schema %}
{
  "name": "💄 Multi column",
  "class": "index-section",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full-padded",
      "options": [
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"}
      ]
    },
    {
      "type": "header",
      "content": "Header"
    },
    {
      "type": "select",
      "id": "header_layout",
      "label": "Layout",
      "default": "horizontal",
      "options": [
        {"value": "horizontal", "label": "Horizontal"},
        {"value": "vertical", "label": "Vertical"}
      ]
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "select",
      "id": "title_font_size",
      "label": "Title font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": ""
    },
    {
      "type": "text",
      "id": "eyebrow",
      "label": "Eyebrow text"
    },
    {
      "type": "select",
      "id": "eyebrow_font_size",
      "label": "Eyebrow font size",
      "options": [
        {"value": "", "label": "Default"},
        {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
        {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
        {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
        {"value": "heading-mini", "label": "Mini", "group": "Headings"},
        {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
        {"value": "heading-small", "label": "Small", "group": "Headings"},
        {"value": "heading-medium", "label": "Medium", "group": "Headings"},
        {"value": "heading-large", "label": "Large", "group": "Headings"},
        {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
        {"value": "body-x-small", "label": "Extra small", "group": "Body"},
        {"value": "body-small", "label": "Small", "group": "Body"},
        {"value": "body-medium", "label": "Medium", "group": "Body"},
        {"value": "body-large", "label": "Large", "group": "Body"},
        {"value": "body-x-large", "label": "Extra large", "group": "Body"},
        {"value": "text-badge", "label": "Badge", "group": "Misc."},
        {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
      ],
      "default": "text-badge-lg"
    },
    {
      "type": "url",
      "id": "header_button_link",
      "label": "Button link"
    },
    {
      "type": "text",
      "id": "header_button_text",
      "label": "Button text"
    },
    {
      "type": "select",
      "id": "header_button_type",
      "label": "Button color",
      "default": "btn--primary",
      "options": [
        {"value": "btn--black", "label": "Black"},
        {"value": "btn--white", "label": "White"},
        {"value": "btn--primary", "label": "Primary"},
        {"value": "btn--secondary", "label": "Secondary"}
      ]
    },
    {
      "type": "select",
      "id": "header_button_size",
      "label": "Button size",
      "default": "",
      "options": [
        {"label": "Small", "value": "btn--small"},
        {"label": "Medium", "value": ""},
        {"label": "Large", "value": "btn--large"}
      ]
    },
    {
      "type": "select",
      "id": "header_button_style",
      "label": "Button style",
      "default": "btn--solid",
      "options": [
        {"label": "Solid", "value": "btn--solid"},
        {"label": "Outline", "value": "btn--outline"},
        {"label": "Text", "value": "btn--text"}
      ]
    },
    {
      "type": "header",
      "content": "Columns"
    },
    {
      "type": "select",
      "id": "column_align_heading",
      "label": "Heading",
      "default": "text-left",
      "options": [
        {
          "value": "text-left",
          "label": "Left"
        },
        {
          "value": "text-center",
          "label": "Centered"
        }
      ]
    },
    {
      "type": "select",
      "id": "column_align_text",
      "label": "Text",
      "default": "text-left",
      "options": [
        {
          "value": "text-left",
          "label": "Left"
        },
        {
          "value": "text-center",
          "label": "Centered"
        }
      ]
    },
    {
      "type": "select",
      "id": "align_columns",
      "label": "Columns",
      "default": "flex-align-top",
      "options": [
        {
          "value": "flex-align-top",
          "label": "Top"
        },
        {
          "value": "flex-align-center",
          "label": "Centered"
        }
      ]
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "select",
      "id": "layout_mobile",
      "label": "Layout",
      "options": [
        {
          "value": "1",
          "label": "1 item per row"
        },
        {
          "value": "2",
          "label": "2 items per row"
        },
        {
          "value": "slider",
          "label": "Slider"
        }
      ],
      "default": "slider"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 50
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "automatic",
      "options": [
        {"value": "automatic", "label": "Automatic"},
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "range",
          "id": "column_width",
          "label": "Column width",
          "unit": "%",
          "min": 10,
          "max": 100,
          "step": 1,
          "default": 33,
          "info": "Desktop only"
        },
        {
          "type": "header",
          "content": "Image"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1400 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "range",
          "id": "image_width",
          "label": "Image width",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "default": 100
        },
        {
          "type": "range",
          "id": "image_aspect_ratio",
          "min": 0.5,
          "max": 1.5,
          "step": 0.1,
          "unit": ":1",
          "label": "Desktop aspect ratio",
          "info": "Wide to tall",
          "default": 1
        },
        {
          "type": "range",
          "id": "mobile_image_aspect_ratio",
          "min": 0.5,
          "max": 1.5,
          "step": 0.1,
          "unit": ":1",
          "label": "Mobile aspect ratio",
          "info": "Wide to tall",
          "default": 1
        },
        {
          "type": "select",
          "id": "image_shape",
          "label": "Image shape",
          "options": [
            {
              "value": "normal",
              "label": "Normal"
            },
            {
              "value": "circle",
              "label": "Circle"
            },
            {
              "value": "rounded",
              "label": "Rounded"
            },
            {
              "value": "blob-one",
              "label": "Blob one"
            },
            {
              "value": "blob-two",
              "label": "Blob two"
            },
            {
              "value": "blob-three",
              "label": "Blob three"
            }
          ],
          "default": "normal",
          "info": "1:1 aspect ratio recommended"
        },
        {
          "type": "select",
          "id": "image_overlay_alignment",
          "label": "Image overlay alignment",
          "default": "flex-end",
          "options": [
            {"value": "flex-start", "label": "Top"},
            {"value": "center", "label": "Center"},
            {"value": "flex-end", "label": "Bottom"}
          ]
        },
        {
          "type": "text",
          "id": "image_button_text",
          "label": "Image button text",
          "info": "Leave blank to link entire image"
        },
        {
          "type": "url",
          "id": "image_button_url",
          "label": "Image button link"
        },
        {
          "type": "select",
          "id": "image_button_type",
          "label": "Image button color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "image_button_style",
          "label": "Image button style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "select",
          "id": "image_button_size",
          "label": "Image button size",
          "default": "btn--large",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "checkbox",
          "id": "image_button_fullwidth",
          "label": "Image button full width",
          "default": true
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-small",
          "options": [
            {"value": "", "label": "Default"},
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
            {"value": "heading-mini", "label": "Mini", "group": "Headings"},
            {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
            {"value": "heading-small", "label": "Small", "group": "Headings"},
            {"value": "heading-medium", "label": "Medium", "group": "Headings"},
            {"value": "heading-large", "label": "Large", "group": "Headings"},
            {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
            {"value": "body-x-small", "label": "Extra small", "group": "Body"},
            {"value": "body-small", "label": "Small", "group": "Body"},
            {"value": "body-medium", "label": "Medium", "group": "Body"},
            {"value": "body-large", "label": "Large", "group": "Body"},
            {"value": "body-x-large", "label": "Extra large", "group": "Body"},
            {"value": "text-badge", "label": "Badge", "group": "Misc."},
            {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "", "label": "Default"},
            {"value": "subheading", "label": "Subheading", "group": "Subheadings"},
            {"value": "subheading-eyebrow", "label": "Subheading (Eyebrow)", "group": "Subheadings"},
            {"value": "subheading-eyebrow-2", "label": "Subheading (Eyebrow 2)", "group": "Subheadings"},
            {"value": "heading-mini", "label": "Mini", "group": "Headings"},
            {"value": "heading-x-small", "label": "Extra small", "group": "Headings"},
            {"value": "heading-small", "label": "Small", "group": "Headings"},
            {"value": "heading-medium", "label": "Medium", "group": "Headings"},
            {"value": "heading-large", "label": "Large", "group": "Headings"},
            {"value": "heading-x-large", "label": "Extra large", "group": "Headings"},
            {"value": "body-x-small", "label": "Extra small", "group": "Body"},
            {"value": "body-small", "label": "Small", "group": "Body"},
            {"value": "body-medium", "label": "Medium", "group": "Body"},
            {"value": "body-large", "label": "Large", "group": "Body"},
            {"value": "body-x-large", "label": "Extra large", "group": "Body"},
            {"value": "text-badge", "label": "Badge", "group": "Misc."},
            {"value": "text-badge-lg", "label": "Badge (Large)", "group": "Misc."}
          ]
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Text",
          "info": "Leave blank to link entire image"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Button size",
          "default": "btn--large",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    },
    {
      "type": "icon",
      "name": "Icon",
      "settings": [
        {
          "type": "range",
          "id": "column_width",
          "label": "Column width",
          "unit": "%",
          "min": 10,
          "max": 100,
          "step": 1,
          "default": 33,
          "info": "Desktop only"
        },
        {
          "type": "header",
          "content": "Icon"
        },
        {
          "type": "select",
          "id": "icon_name",
          "label": "Icon",
          "default": "icon-award",
          "options": [
            {"label": "Award", "value": "icon-award"},
            {"label": "Box", "value": "icon-box"},
            {"label": "Chat", "value": "icon-chat"},
            {"label": "Check", "value": "icon-check"},
            {"label": "Check circle", "value": "icon-check-circle"},
            {"label": "Cloud", "value": "icon-cloud"},
            {"label": "Diameter", "value": "icon-diameter"},
            {"label": "Discount", "value": "icon-discount"},
            {"label": "Donation", "value": "icon-donation"},
            {"label": "Droplet", "value": "icon-droplet"},
            {"label": "Info", "value": "icon-info-empty"},
            {"label": "Email", "value": "icon-email"},
            {"label": "Fast shipment", "value": "icon-fast-shipment"},
            {"label": "Flare", "value": "icon-flare"},
            {"label": "Flower", "value": "icon-flower"},
            {"label": "Gift", "value": "icon-gift"},
            {"label": "Green shipment", "value": "icon-green-shipment"},
            {"label": "Heart", "value": "icon-heart"},
            {"label": "Leaf", "value": "icon-leaf"},
            {"label": "Lightning", "value": "icon-lightning"},
            {"label": "Location", "value": "icon-location"},
            {"label": "Mail", "value": "icon-mail"},
            {"label": "Notes", "value": "icon-notes"},
            {"label": "Pants", "value": "icon-pants"},
            {"label": "Peace", "value": "icon-peace"},
            {"label": "Pin", "value": "icon-pin"},
            {"label": "Planet", "value": "icon-planet"},
            {"label": "Phone", "value": "icon-phone"},
            {"label": "Recycle", "value": "icon-recycle"},
            {"label": "Ruler", "value": "icon-ruler"},
            {"label": "Shield", "value": "icon-shield"},
            {"label": "Smile", "value": "icon-smile"},
            {"label": "Star", "value": "icon-star"},
            {"label": "Tree", "value": "icon-tree"},
            {"label": "Trophy", "value": "icon-trophy"},
            {"label": "Truck", "value": "icon-truck"},
            {"label": "Vegan", "value": "icon-vegan"},
            {"label": "Wash", "value": "icon-wash"},
            {"label": "Washing machine", "value": "icon-washing-machine"}
          ]
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Alternative icon/image",
          "info": "160 x 160px .svg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "range",
          "id": "icon_size",
          "label": "Size",
          "unit": "px",
          "min": 20,
          "max": 80,
          "step": 5,
          "default": 20
        },
        {
          "type": "select",
          "id": "icon_alignment",
          "label": "Placement",
          "default": "icon--top",
          "options": [
            {"value": "icon--top", "label": "Top"},
            {"value": "icon--left", "label": "Left"}
          ]
        },
        {
          "type": "color",
          "id": "icon_color",
          "label": "Color"
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Title"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-small",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Share details about your shipping policies, item returns, or customer service.</p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "range",
          "id": "column_width",
          "label": "Column width",
          "unit": "%",
          "min": 10,
          "max": 100,
          "step": 1,
          "default": 33,
          "info": "Desktop only"
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Add a title or tagline"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-medium",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this text to describe products, share details on availability and style, or as a space to display recent reviews or FAQs.</p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Text",
          "default": "Learn more"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    },
    {
      "type": "menu",
      "name": "Menu",
      "settings": [
        {
          "type": "range",
          "id": "column_width",
          "label": "Column width",
          "unit": "%",
          "min": 10,
          "max": 100,
          "step": 1,
          "default": 33,
          "info": "Desktop only"
        },
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu",
          "info": "This menu won't show dropdown items."
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-small",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        }
      ]
    },
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "range",
          "id": "column_width",
          "label": "Column width",
          "unit": "%",
          "min": 10,
          "max": 100,
          "step": 1,
          "default": 33,
          "info": "Desktop only"
        },
        {
          "label": "Product",
          "id": "product",
          "type": "product"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "💄 Multi column",
      "blocks": [
        {
          "type": "icon",
          "settings": {
            "title": "Secure transactions",
            "icon_name": "icon-shield",
            "text": "<p>Transactions are handled with bank-grade security.</p>"
          }
        },
        {
          "type": "icon",
          "settings": {
            "title": "Simple checkout",
            "icon_name": "icon-trophy",
            "text": "<p>Our secure checkout is quick and easy to use.</p>"
          }
        },
        {
          "type": "icon",
          "settings": {
            "title": "Get in touch",
            "icon_name": "icon-chat",
            "text": "<p>Have questions? Get in touch with us at any time.</p>"
          }
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "footer"]
  }
}
{% endschema %}
