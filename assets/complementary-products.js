!function(){"use strict";const t="complementary-products",e="data-url",n="data-slider";class s extends HTMLElement{constructor(){super()}connectedCallback(){new IntersectionObserver(((s,r)=>{s[0].isIntersecting&&(r.unobserve(this),this.hasAttribute(e)&&""!==this.getAttribute(e)&&fetch(this.getAttribute(e)).then((t=>t.text())).then((e=>{const s=document.createElement("div");s.innerHTML=e;const r=s.querySelector(t);r&&r.innerHTML.trim().length&&(this.innerHTML=r.innerHTML,this.hasAttribute(n)&&this.dispatchEvent(new CustomEvent("theme:complementary:loaded",{bubbles:!0,detail:{container:this}})))})).catch((t=>{console.error(t)})))}).bind(this),{rootMargin:"0px 0px 400px 0px"}).observe(this)}}customElements.get("complementary-products")||customElements.define("complementary-products",s)}();
