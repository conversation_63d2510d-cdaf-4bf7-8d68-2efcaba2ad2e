!function(){"use strict";customElements.get("quantity-counter")||customElements.define("quantity-counter",class extends HTMLElement{constructor(){super()}connectedCallback(){this.input=this.querySelector("input"),this.changeEvent=new Event("change",{bubbles:!0}),this.buttonClickEvent=this.onButtonClick.bind(this),this.onQuantityChangeEvent=this.onQuantityChange.bind(this),this.input.addEventListener("change",this.onQuantityChangeEvent),this.querySelectorAll("button").forEach((t=>t.addEventListener("click",this.buttonClickEvent)))}onButtonClick(t){t.preventDefault();const e=this.input.value,n="BUTTON"==t.target.nodeName?t.target:t.target.closest("button");"increase"===n.name&&this.input.stepUp(),"decrease"===n.name&&this.input.stepDown(),e!==this.input.value&&this.input.dispatchEvent(this.changeEvent)}onQuantityChange(){"updates[]"==this.input.name&&this.updateCart()}updateCart(){""!==this.quantityValue&&this.dispatchEvent(new CustomEvent("theme:cart:update",{bubbles:!0,detail:{id:this.input.dataset.id,quantity:this.input.value}}))}})}();
