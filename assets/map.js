!function(){"use strict";var e={};function t(t){return e[t]}e.basic=[],e.light=[{featureType:"administrative",elementType:"labels",stylers:[{visibility:"simplified"},{lightness:"64"},{hue:"#ff0000"}]},{featureType:"administrative",elementType:"labels.text.fill",stylers:[{color:"#bdbdbd"}]},{featureType:"administrative",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"landscape",elementType:"all",stylers:[{color:"#f0f0f0"},{visibility:"simplified"}]},{featureType:"landscape.natural.landcover",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"landscape.natural.terrain",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry.fill",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"labels",stylers:[{lightness:"100"}]},{featureType:"poi.park",elementType:"all",stylers:[{visibility:"on"}]},{featureType:"poi.park",elementType:"geometry",stylers:[{saturation:"-41"},{color:"#e8ede7"}]},{featureType:"poi.park",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"all",stylers:[{saturation:"-100"}]},{featureType:"road",elementType:"labels",stylers:[{lightness:"25"},{gamma:"1.06"},{saturation:"-100"}]},{featureType:"road.highway",elementType:"all",stylers:[{visibility:"simplified"}]},{featureType:"road.highway",elementType:"geometry.fill",stylers:[{gamma:"10.00"}]},{featureType:"road.highway",elementType:"geometry.stroke",stylers:[{weight:"0.01"},{visibility:"simplified"}]},{featureType:"road.highway",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"road.highway",elementType:"labels.text.fill",stylers:[{weight:"0.01"}]},{featureType:"road.highway",elementType:"labels.text.stroke",stylers:[{weight:"0.01"}]},{featureType:"road.arterial",elementType:"geometry.fill",stylers:[{weight:"0.8"}]},{featureType:"road.arterial",elementType:"geometry.stroke",stylers:[{weight:"0.01"}]},{featureType:"road.arterial",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road.local",elementType:"geometry.fill",stylers:[{weight:"0.01"}]},{featureType:"road.local",elementType:"geometry.stroke",stylers:[{gamma:"10.00"},{lightness:"100"},{weight:"0.4"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"simplified"},{weight:"0.01"},{lightness:"39"}]},{featureType:"road.local",elementType:"labels.text.stroke",stylers:[{weight:"0.50"},{gamma:"10.00"},{lightness:"100"}]},{featureType:"transit",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"water",elementType:"all",stylers:[{color:"#cfe5ee"},{visibility:"on"}]}],e.white_label=[{featureType:"all",elementType:"all",stylers:[{visibility:"simplified"}]},{featureType:"all",elementType:"labels",stylers:[{visibility:"simplified"}]},{featureType:"administrative",elementType:"labels",stylers:[{gamma:"3.86"},{lightness:"100"}]},{featureType:"administrative",elementType:"labels.text.fill",stylers:[{color:"#cccccc"}]},{featureType:"landscape",elementType:"all",stylers:[{color:"#f2f2f2"}]},{featureType:"poi",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"road",elementType:"all",stylers:[{saturation:-100},{lightness:45}]},{featureType:"road.highway",elementType:"all",stylers:[{visibility:"simplified"}]},{featureType:"road.highway",elementType:"geometry.fill",stylers:[{weight:"0.8"}]},{featureType:"road.highway",elementType:"geometry.stroke",stylers:[{weight:"0.8"}]},{featureType:"road.highway",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"road.highway",elementType:"labels.text.fill",stylers:[{weight:"0.8"}]},{featureType:"road.highway",elementType:"labels.text.stroke",stylers:[{weight:"0.01"}]},{featureType:"road.arterial",elementType:"geometry.stroke",stylers:[{weight:"0"}]},{featureType:"road.arterial",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"road.local",elementType:"geometry.stroke",stylers:[{weight:"0.01"}]},{featureType:"road.local",elementType:"labels.text",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"water",elementType:"all",stylers:[{color:"#e4e4e4"},{visibility:"on"}]}],e.dark_label=[{featureType:"all",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"all",elementType:"labels.text.fill",stylers:[{saturation:36},{color:"#000000"},{lightness:40}]},{featureType:"all",elementType:"labels.text.stroke",stylers:[{visibility:"on"},{color:"#000000"},{lightness:16}]},{featureType:"all",elementType:"labels.icon",stylers:[{visibility:"off"}]},{featureType:"administrative",elementType:"geometry.fill",stylers:[{color:"#000000"},{lightness:20}]},{featureType:"administrative",elementType:"geometry.stroke",stylers:[{color:"#000000"},{lightness:17},{weight:1.2}]},{featureType:"administrative",elementType:"labels",stylers:[{visibility:"simplified"},{lightness:"-82"}]},{featureType:"administrative",elementType:"labels.text.stroke",stylers:[{invert_lightness:!0},{weight:"7.15"}]},{featureType:"landscape",elementType:"geometry",stylers:[{color:"#000000"},{lightness:20}]},{featureType:"landscape",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"poi",elementType:"geometry",stylers:[{color:"#000000"},{lightness:21}]},{featureType:"road",elementType:"labels",stylers:[{visibility:"simplified"}]},{featureType:"road.highway",elementType:"geometry.fill",stylers:[{color:"#000000"},{lightness:17},{weight:"0.8"}]},{featureType:"road.highway",elementType:"geometry.stroke",stylers:[{color:"#000000"},{lightness:29},{weight:"0.01"}]},{featureType:"road.highway",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"road.arterial",elementType:"geometry",stylers:[{color:"#000000"},{lightness:18}]},{featureType:"road.arterial",elementType:"geometry.stroke",stylers:[{weight:"0.01"}]},{featureType:"road.local",elementType:"geometry",stylers:[{color:"#000000"},{lightness:16}]},{featureType:"road.local",elementType:"geometry.stroke",stylers:[{weight:"0.01"}]},{featureType:"road.local",elementType:"labels",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:"all",stylers:[{visibility:"off"}]},{featureType:"transit",elementType:"geometry",stylers:[{color:"#000000"},{lightness:19}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#000000"},{lightness:17}]}];const l={};function s(e={}){if(e.type||(e.type="json"),e.url)return l[e.url]?l[e.url]:function(e,t){const s=new Promise(((l,s)=>{"text"===t?fetch(e).then((e=>e.text())).then((e=>{l(e)})).catch((e=>{s(e)})):function(e,t,l){let s=document.getElementsByTagName("head")[0],i=!1,r=document.createElement("script");r.src=e,r.onload=r.onreadystatechange=function(){i||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState?l():(i=!0,t())},s.appendChild(r)}(e,(function(){l()}),(function(){s()}))}));return l[e]=s,s}(e.url,e.type);if(e.json)return l[e.json]?Promise.resolve(l[e.json]):window.fetch(e.json).then((e=>e.json())).then((t=>(l[e.json]=t,t)));if(e.name){const t="".concat(e.name,e.version);return l[t]?l[t]:function(e){const t="".concat(e.name,e.version),s=new Promise(((t,l)=>{try{window.Shopify.loadFeatures([{name:e.name,version:e.version,onLoad:e=>{!function(e,t,l){l?t(l):e()}(t,l,e)}}])}catch(e){l(e)}}));return l[t]=s,s}(e)}return Promise.reject()}window.theme.mapAPI=window.theme.mapAPI||null,customElements.get("map-component")||customElements.define("map-component",class extends HTMLElement{constructor(){super(),this.key=this.getAttribute("data-api-key"),this.styleString=this.getAttribute("data-style")||"",this.zoomString=this.getAttribute("data-zoom")||14,this.address=this.getAttribute("data-address"),this.enableCorrection=this.getAttribute("data-latlong-correction"),this.lat=this.getAttribute("data-lat"),this.long=this.getAttribute("data-long")}connectedCallback(){this.key&&this.initMaps()}initMaps(){(function(e){if(null===window.theme.mapAPI){const t=`https://maps.googleapis.com/maps/api/js?key=${e}`;window.theme.mapAPI=s({url:t})}return window.theme.mapAPI})(this.key).then((()=>{return"true"===this.enableCorrection&&""!==this.lat&&""!==this.long?new google.maps.LatLng(this.lat,this.long):(e=this.address,new Promise(((t,l)=>{(new google.maps.Geocoder).geocode({address:e},(function(e,s){if("OK"==s){var i={lat:e[0].geometry.location.lat(),lng:e[0].geometry.location.lng()};t(i)}else l(s)}))})));var e})).then((e=>{const l=function(e,t){var l=new google.maps.Map(e,t),s=l.getCenter();return new google.maps.Marker({map:l,position:s}),google.maps.event.addDomListener(window,"resize",(function(){google.maps.event.trigger(l,"resize"),l.setCenter(s)})),l}(this,{zoom:parseInt(this.zoomString,10),styles:t(this.styleString),center:e,draggable:!0,clickableIcons:!1,scrollwheel:!1,zoomControl:!1,disableDefaultUI:!0});return l})).then((e=>{this.map=e})).catch((e=>{console.log("Failed to load Google Map"),console.log(e)}))}disconnectedCallback(){void 0!==window.google&&google.maps.event.clearListeners(this.map,"resize")}})}();
