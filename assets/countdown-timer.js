!function(){"use strict";const t="time",s="[data-days]",e="[data-hours]",i="[data-minutes]",n="[data-seconds]",o=".shopify-section",h="[data-countdown-block]",a="data-expiration-behavior",r="show-message",d="hidden",u="hide-section",c="show-message";customElements.get("countdown-timer")||customElements.define("countdown-timer",class extends HTMLElement{constructor(){super(),this.section=this.closest(o),this.countdownParent=this.closest(h)||this.section,this.expirationBehavior=this.getAttribute(a),this.time=this.querySelector(t),this.days=this.querySelector(s),this.hours=this.querySelector(e),this.minutes=this.querySelector(i),this.seconds=this.querySelector(n),this.endDate=Date.parse(this.time.dateTime),this.daysInMs=864e5,this.hoursInMs=this.daysInMs/24,this.minutesInMs=this.hoursInMs/60,this.secondsInMs=this.minutesInMs/60,this.shouldHideOnComplete=this.expirationBehavior===u,this.shouldShowMessage=this.expirationBehavior===c,this.update=this.update.bind(this)}connectedCallback(){isNaN(this.endDate)||this.endDate<=Date.now()?this.onComplete():(this.update(),this.interval=setInterval(this.update,1e3))}disconnectedCallback(){this.stopTimer()}convertTime(t){const s=this.formatDigits(parseInt(t/this.daysInMs,10));t-=s*this.daysInMs;const e=this.formatDigits(parseInt(t/this.hoursInMs,10));t-=e*this.hoursInMs;const i=this.formatDigits(parseInt(t/this.minutesInMs,10));t-=i*this.minutesInMs;return{days:s,hours:e,minutes:i,seconds:this.formatDigits(parseInt(t/this.secondsInMs,10))}}formatDigits(t){return t<10&&(t="0"+t),t}render(t){this.days.textContent=t.days,this.hours.textContent=t.hours,this.minutes.textContent=t.minutes,this.seconds.textContent=t.seconds}stopTimer(){clearInterval(this.interval)}onComplete(){this.render({days:0,hours:0,minutes:0,seconds:0}),this.shouldHideOnComplete&&(this.countdownParent?.classList.add(d),this.countdownParent?.dispatchEvent(new CustomEvent("theme:countdown:hide",{detail:{element:this},bubbles:!0}))),this.shouldShowMessage&&(this.classList?.add(r),this.countdownParent?.dispatchEvent(new CustomEvent("theme:countdown:expire",{bubbles:!0})))}update(){const t=(new Date).getTime(),s=this.endDate-t;s<1e3&&(this.stopTimer(),this.onComplete());const e=this.convertTime(s);this.render(e)}})}();
