!function(){"use strict";const e={marquee:".announcement__bar-holder--marquee",slide:"[data-slide]",slider:"[data-slider]",ticker:"ticker-bar",tickerSlide:".announcement__slide"};customElements.get("announcement-bar")||customElements.define("announcement-bar",class extends HTMLElement{constructor(){super(),this.slider=this.querySelector(e.slider),this.slidesCount=this.querySelectorAll(e.tickerSlide).length,this.resizeEvent=e=>this.resize(e)}connectedCallback(){this.addEventListener("theme:slider:loaded",(()=>{this.querySelectorAll(e.tickerBar)?.forEach((e=>{e.dispatchEvent(new CustomEvent("theme:ticker:refresh"))}))})),this.addEventListener("theme:countdown:hide",(t=>{if(window.Shopify.designMode)return;const s=t.target.closest(e.marquee);if(this.slidesCount<2){this.querySelector(e.ticker).style.display="none"}if(s){const s=t.target.closest(e.tickerSlide);this.removeTickerText(s)}else{const s=t.target.closest(e.slide);this.removeSlide(s)}})),this.addEventListener("theme:countdown:expire",(()=>{this.querySelectorAll(e.ticker)?.forEach((e=>{e.dispatchEvent(new CustomEvent("theme:ticker:refresh"))}))})),document.addEventListener("theme:resize:width",this.resizeEvent),document.dispatchEvent(new CustomEvent("theme:announcement:init",{bubbles:!0}))}resize(){this.slider.dispatchEvent(new CustomEvent("theme:slider:init",{bubbles:!1})),this.slider.dispatchEvent(new CustomEvent("theme:slider:reposition",{bubbles:!1}))}removeSlide(e){this.slider.dispatchEvent(new CustomEvent("theme:slider:remove-slide",{bubbles:!1,detail:{slide:e}}))}removeTickerText(t){const s=t.closest(e.ticker);t.remove(),s.dispatchEvent(new CustomEvent("theme:ticker:refresh"))}disconnectedCallback(){document.removeEventListener("theme:resize:width",this.sliderResizeEvent)}})}();
