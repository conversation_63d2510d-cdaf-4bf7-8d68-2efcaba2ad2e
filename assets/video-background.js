!function(){"use strict";const e="[data-video-template]",t="is-loading",i="data-video-id";customElements.get("video-background")||customElements.define("video-background",class extends HTMLElement{constructor(){super()}connectedCallback(){this.videoId=this.getAttribute(i),this.videoTemplate=this.querySelector(e),this.video=null,this.powerSaverVideoPlay=this.powerSaverVideoPlay.bind(this),this.videoId&&this.renderVideo()}powerSaverVideoPlay(){this.video?.play()}renderVideo(){this.videoTemplateObserver=new IntersectionObserver(((e,i)=>{e.forEach((e=>{if(e.isIntersecting){const o=this.videoTemplate.innerHTML;this.innerHTML=o,this.classList.remove(t),this.video=this.querySelector("video"),this.observeVideoPlayToggle(),this.video.play().then((()=>{})).catch((()=>{document.addEventListener("click",this.powerSaverVideoPlay,{passive:!0,bubbles:!0,once:!0}),document.addEventListener("touchstart",this.powerSaverVideoPlay,{passive:!0,bubbles:!0,once:!0})})),i.unobserve(e.target)}}))}),{root:null,rootMargin:"300px",threshold:[0,.1,.25,.5,.75,1]}),this.videoTemplateObserver.observe(this)}observeVideoPlayToggle(){if(!this.video)return;this.videoPlayObserver=new IntersectionObserver((e=>{e.forEach((e=>{const t=e.isIntersecting;if(t&&"function"==typeof this.video.play){const e=this.video.play();void 0!==e&&e.then((e=>{})).catch((e=>{}))}t||"function"!=typeof this.video.pause||this.video.pause()}))}),{rootMargin:"0px",threshold:[0,1]}),this.videoPlayObserver.observe(this.video)}disconnectedCallback(){this.videoTemplateObserver&&this.videoTemplateObserver.disconnect(),this.videoPlayObserver&&this.videoPlayObserver.disconnect(),this.videoId&&(document.removeEventListener("click",this.powerSaverVideoPlay),document.removeEventListener("touchstart",this.powerSaverVideoPlay))}})}();
