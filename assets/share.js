!function(){"use strict";const t="data-section-type",e="[data-share-button]",s="[data-share-message]",a="is-visible";customElements.get("share-button")||customElements.define("share-button",class extends HTMLElement{constructor(){super(),this.container=this.closest(`[${t}]`),this.shareButton=this.querySelector(e),this.shareMessage=this.querySelector(s),this.urlToShare=this.shareButton.dataset.shareUrl?this.shareButton.dataset.shareUrl:document.location.href,this.init(),this.updateShareLink()}init(){navigator.share?this.shareButton.addEventListener("click",(()=>{navigator.share({url:this.urlToShare,title:document.title})})):this.shareButton.addEventListener("click",this.copyToClipboard.bind(this))}updateShareLink(){"product"==this.container.getAttribute(t)&&this.container.addEventListener("theme:variant:change",(t=>{t.detail.variant&&(this.urlToShare=`${this.urlToShare.split("?")[0]}?variant=${t.detail.variant.id}`)}))}copyToClipboard(){navigator.clipboard.writeText(this.urlToShare).then((()=>{this.shareMessage.classList.add(a);const t=()=>{this.shareMessage.classList.remove(a),this.shareMessage.removeEventListener("animationend",t)};this.shareMessage.addEventListener("animationend",t)}))}})}();
