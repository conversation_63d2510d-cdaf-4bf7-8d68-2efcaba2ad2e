/*
* Broadcast Theme
*
* Use this file to add custom Javascript to Broadcast.  Keeping your custom
* Javascript in this fill will make it easier to update <PERSON>. In order
* to use this file you will need to open layout/theme.liquid and uncomment
* the custom.js script import line near the bottom of the file.
*/


(function() {
  // Add custom code below this line

  // Variant-specific accordion functionality
  function initVariantAccordions() {
    const variantAccordions = document.querySelectorAll('[data-variant-accordion]');

    if (variantAccordions.length === 0) return;

    // Listen for variant changes
    document.addEventListener('theme:variant:change', function(event) {
      console.log('Variant change event received:', event.detail);
      const variant = event.detail.variant;

      variantAccordions.forEach(function(accordion) {
        updateVariantAccordionContent(accordion, variant);
      });
    });
  }

  function updateVariantAccordionContent(accordion, variant) {
    console.log('Updating accordion content for variant:', variant);
    const contentElement = accordion.querySelector('[data-variant-accordion-content]');
    const metafieldsScript = accordion.parentElement.querySelector('[data-variant-accordion-metafields]');

    console.log('Content element found:', !!contentElement);
    console.log('Metafields script found:', !!metafieldsScript);

    if (!contentElement || !metafieldsScript || !variant) return;

    try {
      const metafieldsData = JSON.parse(metafieldsScript.textContent);
      console.log('Metafields data:', metafieldsData);
      const variantData = metafieldsData.find(function(data) {
        return data.variant_id === variant.id.toString();
      });

      console.log('Variant data found:', variantData);

      if (variantData) {
        // Update the accordion content with the variant-specific metafield value
        const newContent = variantData.metafield_value.replace(/\\n/g, '\n').replace(/\\"/g, '"');
        console.log('Updating content to:', newContent);
        contentElement.innerHTML = newContent;
      }
    } catch (error) {
      console.warn('Error updating variant accordion content:', error);
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initVariantAccordions);
  } else {
    initVariantAccordions();
  }

  // ^^ Keep your scripts inside this IIFE function call to
  // avoid leaking your variables into the global scope.
})();
