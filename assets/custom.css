@charset "UTF-8";
/* 1. Variables */
/* ==================== BROADCAST THEME 7.0.0 ==================== */
/*  ------------------------------
    Grid Variables
    ------------------------------ */
/**
 * Define your breakpoints. The first value is the prefix that shall be used for
 * your classes (e.g. `.small--one-half`), the second value is the media query
 * that the breakpoint fires at.
 */
/**
 * Define which namespaced breakpoints you would like to generate for each of
 * widths, push and pull. This is handy if you only need pull on, say, desk, or
 * you only need a new width breakpoint at mobile sizes. It allows you to only
 * compile as much CSS as you need. All are turned on by default, but you can
 * add and remove breakpoints at will.
 *
 * Push and pull shall only be used if `$push` and/or `$pull` and `$responsive`
 * have been set to ‘true’.
 */
/*

$breakpoint-has-widths: (
  'small',
  'small-up',
  'small-down',
  'medium',
  'medium-up',
  'medium-down',
  'large',
  'large-up',
  'large-down'
  'xlarge',
  'xlarge-up',
  'xlarge-down'
  ) !default;

$breakpoint-has-push: (
  'small',
  'small-up',
  'small-down',
  'medium',
  'medium-up',
  'medium-down',
  'large',
  'large-up',
  'large-down'
  'xlarge',
  'xlarge-up',
  'xlarge-down'
  ) !default;

$breakpoint-has-pull: (
  'small',
  'small-up',
  'small-down',
  'medium',
  'medium-up',
  'medium-down',
  'large',
  'large-up',
  'large-down'
  'xlarge',
  'xlarge-up',
  'xlarge-down'
  ) !default;

*/
/* =============== Colors =============== */
/* =============== Layout =============== */
/* =============== Utilites =============== */
/* 2. Mixins */
/*

  1. Utilities
  2. Responsive
  3. UI Elements
    3.1. Buttons

*/
/*  ==============================
    1. Utilities
    ============================== */
/*  ==============================
    2. Responsive
    ============================== */
/*  ==============================
    3. UI Elements
    ============================== */
/*  ------------------------------
    3.1. Buttons
    ------------------------------ */
/* ------------------------------
   Headings
   ------------------------------ */
/* ------------------------------
   Labels
   ------------------------------ */
/* ------------------------------
   Inputs
   ------------------------------ */
/*  ------------------------------
    3.3. Shopify
    ------------------------------ */
/* 3. Fonts  */
/* 4. Basic Styles */
/* ----- Text  ----- */
/* ----- Subheadings  ----- */
/* ----- Product Cards  ----- */
/* ========== Fonts ========== */
.text-larger {
  font-size: 1.15em;
}

.text-smaller {
  font-size: 0.85em;
}

/* ----- Heading Font 1 - URW DIN  ----- */
.heading-font-1 {
  font-family: var(--font-family-heading-1);
}

/* ----- Heading Font 2 - Maison Neue Extended  ----- */
.heading-font-2 {
  font-family: var(--font-family-heading-2);
}

/* ----- Body Font 1 - Maison Neue  ----- */
.body-font {
  font-family: var(--font-family-body);
}

.font-heading {
  text-transform: unset;
}

/* ========== Typography ========== */
/* ----- Headings  ----- */
.h0,
.heading-x-large,
.h1, .text-h1 {
  font-family: var(--font-family-heading-1);
  font-size: var(--font-size-h1);
  font-weight: 600;
  line-height: 100%;
  letter-spacing: -0.03em;
  text-transform: uppercase;
}

.heading-large,
.h2, .text-h2 {
  font-family: var(--font-family-heading-1);
  font-size: var(--font-size-h2);
  font-weight: 600;
  line-height: 105%;
  letter-spacing: -0.01em;
  text-transform: uppercase;
}

.heading-medium,
.h3, .text-h3 {
  font-family: var(--font-family-heading-1);
  font-size: var(--font-size-h3);
  font-style: normal;
  font-weight: 600;
  line-height: 105%;
  letter-spacing: -0.01em;
  text-transform: uppercase;
}

.heading-small,
.h4, .text-h4 {
  font-family: var(--font-family-heading-2);
  font-size: var(--font-size-h4);
  font-style: normal;
  font-weight: 300;
  line-height: 115%;
  letter-spacing: -0.02em;
}

.heading-x-small,
.h5, .text-h5 {
  font-family: var(--font-family-heading-2);
  font-size: var(--font-size-h5);
  font-style: normal;
  font-weight: 300;
  line-height: 115%;
  letter-spacing: -0.01em;
}

.heading-mini,
.h6, .text-h6 {
  font-family: var(--font-family-heading-2);
  font-size: var(--font-size-h6);
  font-style: normal;
  font-weight: 300;
  line-height: 135%;
  letter-spacing: -0.01em;
}

@media only screen and (max-width: 750px) {
  .heading-mobile-mini {
    font-family: var(--font-family-heading-2);
    font-size: var(--font-size-h6);
    font-style: normal;
    font-weight: 300;
    line-height: 135%;
    letter-spacing: -0.01em;
  }
  .heading-mobile-x-small {
    font-family: var(--font-family-heading-2);
    font-size: var(--font-size-h5);
    font-style: normal;
    font-weight: 300;
    line-height: 115%;
    letter-spacing: -0.01em;
  }
  .heading-mobile-small {
    font-family: var(--font-family-heading-2);
    font-size: var(--font-size-h4);
    font-style: normal;
    font-weight: 300;
    line-height: 115%;
    letter-spacing: -0.02em;
  }
  .heading-mobile-medium {
    font-family: var(--font-family-heading-1);
    font-size: var(--font-size-h3);
    font-style: normal;
    font-weight: 600;
    line-height: 105%;
    letter-spacing: -0.01em;
    text-transform: uppercase;
  }
  .heading-mobile-large {
    font-family: var(--font-family-heading-1);
    font-size: var(--font-size-h2);
    font-weight: 600;
    line-height: 105%;
    letter-spacing: -0.01em;
    text-transform: uppercase;
  }
  .heading-mobile-x-large {
    font-family: var(--font-family-heading-1);
    font-size: var(--font-size-h1);
    font-weight: 600;
    line-height: 100%;
    letter-spacing: -0.03em;
    text-transform: uppercase;
  }
}
@media only screen and (min-width: 750px) {
  .heading-desktop-mini {
    font-family: var(--font-family-heading-2);
    font-size: var(--font-size-h6);
    font-style: normal;
    font-weight: 300;
    line-height: 135%;
    letter-spacing: -0.01em;
  }
  .heading-desktop-x-small {
    font-family: var(--font-family-heading-2);
    font-size: var(--font-size-h5);
    font-style: normal;
    font-weight: 300;
    line-height: 115%;
    letter-spacing: -0.01em;
  }
  .heading-desktop-small {
    font-family: var(--font-family-heading-2);
    font-size: var(--font-size-h4);
    font-style: normal;
    font-weight: 300;
    line-height: 115%;
    letter-spacing: -0.02em;
  }
  .heading-desktop-medium {
    font-family: var(--font-family-heading-1);
    font-size: var(--font-size-h3);
    font-style: normal;
    font-weight: 600;
    line-height: 105%;
    letter-spacing: -0.01em;
    text-transform: uppercase;
  }
  .heading-desktop-large {
    font-family: var(--font-family-heading-1);
    font-size: var(--font-size-h2);
    font-weight: 600;
    line-height: 105%;
    letter-spacing: -0.01em;
    text-transform: uppercase;
  }
  .heading-desktop-x-large {
    font-family: var(--font-family-heading-1);
    font-size: var(--font-size-h1);
    font-weight: 600;
    line-height: 100%;
    letter-spacing: -0.03em;
    text-transform: uppercase;
  }
}
/* ----- Body  ----- */
/* ----- Subheadings ----- */
.subheading {
  font-family: var(--font-family-heading-2);
  font-size: var(--font-size-subheading);
  font-style: normal;
  font-weight: var(--font-weight-subheading);
  letter-spacing: var(--letter-spacing-subheading);
  line-height: 115%;
}

.subheading-eyebrow {
  font-family: var(--font-family-heading-1-alt);
  font-size: var(--font-size-eyebrow);
  font-style: normal;
  font-weight: var(--font-weight-eyebrow);
  line-height: 125%;
  letter-spacing: var(--letter-spacing-eyebrow);
  text-transform: uppercase;
}

.subheading-eyebrow-2 {
  font-family: var(--font-family-heading-2);
  font-size: var(--font-size-eyebrow-2);
  font-style: normal;
  font-weight: var(--font-weight-eyebrow-2);
  line-height: 115%;
  letter-spacing: var(--letter-spacing-eyebrow-2);
  text-transform: uppercase;
}

/* ----- Body Text Styles ----- */
.body-x-small,
.text-xs {
  font-size: var(--font-size-text-xs);
}

.body-small,
.text-sm {
  font-size: var(--font-size-text-sm);
}

.p,
.body-medium,
.text-body {
  font-size: var(--font-size-text);
}

.body-large,
.text-lg {
  font-size: var(--font-size-text-lg);
}

.body-x-large,
.text-xl {
  font-size: var(--font-size-text-xl);
}

@media only screen and (max-width: 750px) {
  .text-xs--mobile {
    font-size: var(--font-size-text-xs);
  }
  .text-sm--mobile {
    font-size: var(--font-size-text-sm);
  }
  .text--mobile {
    font-size: var(--font-size-text);
  }
  .text-lg--mobile {
    font-size: var(--font-size-text-lg);
  }
  .text-xl--mobile {
    font-size: var(--font-size-text-xl);
  }
}
/* ----- Misc. Text Styles ----- */
.text-caption {
  font-size: var(--font-size-caption);
}

.text-navigation, .page-header .header__desktop .header__menu .navlink.navlink--toplevel {
  font-family: var(--font-family-body);
  font-size: var(--font-size-navigation);
  font-weight: var(--font-weight-body-bold);
  text-transform: uppercase;
}

.text-badge {
  font-family: var(--font-family-heading-2-alt);
  font-size: var(--font-size-badge);
  font-weight: var(--font-weight-badge);
  text-transform: uppercase;
}

.text-badge-lg, .page-header .header__desktop .header__menu .navlink.navlink--child {
  font-family: var(--font-family-heading-2-alt);
  font-size: var(--font-size-badge-lg);
  font-weight: var(--font-weight-badge-lg);
  text-transform: uppercase;
}

/* ----- Product Card Text Styles ----- */
.text-product-title {
  font-family: var(--font-family-heading-1);
  font-size: var(--font-size-product-card-title);
  font-weight: var(--font-weight-product-title);
  text-transform: uppercase;
}
.text-product-title.text-product-title--large {
  font-size: var(--font-size-product-card-title-large);
}

.text-product-price {
  font-family: var(--font-family-product-price);
  font-size: var(--font-size-product-card-price);
  font-weight: var(--font-weight-product-price);
}

/* ========== Lists ========== */
/* ----- Unordered ----- */
ul.ul--ticks li, ul.ul--ticks .li, .ul.ul--ticks li, .ul.ul--ticks .li {
  --marker-size: 20px;
  --marker-size: 20px;
  --marker-gutter: 10px;
  position: relative;
  list-style: none;
  margin-block: 0.8rem;
}
ul.ul--ticks li:last-of-type, ul.ul--ticks .li:last-of-type, .ul.ul--ticks li:last-of-type, .ul.ul--ticks .li:last-of-type {
  margin-bottom: 0;
}
ul.ul--ticks li::before, ul.ul--ticks .li::before, .ul.ul--ticks li::before, .ul.ul--ticks .li::before {
  --offset-y: -.2em;
  --offset-x: calc(-100% - var(--marker-gutter));
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  display: block;
  width: var(--marker-size);
  height: var(--marker-size);
  transform: translateX(var(--offset-x)) translateY(var(--offset-y));
  background: var(--icon-check);
}

/*  ==============================
    1. Root Styles
    ============================== */
* {
  box-sizing: border-box;
}

/* 5. Layout */
/* 6. Sections */
.page-header {
  /* ----- Toolbar ----- */
  /* ----- Mobile ----- */
  /* ----- Desktop ----- */
}
.page-header .toolbar {
  padding: 0;
}
.page-header .toolbar .toolbar__utility,
.page-header .toolbar .popout__toggle__text,
.page-header .toolbar .navlink {
  font-weight: normal;
}
.page-header .toolbar ticker-bar {
  width: auto;
}
.page-header .toolbar .navlink--toplevel {
  position: relative;
}
@media only screen and (min-width: 750px) {
  .page-header .toolbar .navlink--toplevel {
    padding-block: 15px;
  }
}
.page-header .toolbar .navlink--toplevel::after {
  content: "";
  display: block;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: currentColor;
  opacity: 0;
  transition: opacity 0.25s ease-out;
}
.page-header .toolbar .navlink--toplevel:hover::after {
  opacity: 1;
}
.page-header .toolbar .navlink--active {
  pointer-events: none;
  cursor: pointer;
}
.page-header .toolbar .navlink--active::after {
  opacity: 1;
}
.page-header .header__mobile {
  padding-top: 15px;
  padding-bottom: 15px;
}
.page-header .header__desktop .header__desktop__upper {
  min-height: 85px;
}
.page-header .header__desktop .header__menu > .menu__item > .navlink--highlight .navtext {
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 10px;
  background: var(--color-basic-offwhite);
  border: 1px solid var(--color-brand-6);
  border-radius: var(--corner-radius);
}
.page-header .header__desktop .header__menu > .menu__item > .navlink--highlight .navtext:after {
  content: none;
}
.page-header .header__backfill {
  margin-top: -1px;
}
.page-header .header__desktop__buttons .header__menu {
  margin-right: 0;
}

.site-footer.custom-site-footer .footer__quicklinks li {
  margin-bottom: 1em;
}
.site-footer.custom-site-footer .footer__block .accordion__title {
  padding: 1.2em 0;
}
.site-footer.custom-site-footer .footer__blocks .footer__block ~ .footer__block {
  border-top-width: 0;
}
.site-footer.custom-site-footer .footer-socials-container {
  margin-block: 2em;
}
.site-footer.custom-site-footer .socials {
  --icon-size: 22px;
  gap: 10px;
}
.site-footer.custom-site-footer .socials li {
  margin: 0;
}
@media only screen and (min-width: 750px) {
  .site-footer.custom-site-footer .socials {
    --icon-size: 28px;
    gap: 20px;
  }
}

.supporting-menu.custom-supporting-menu {
  /* ----- Menu Items ----- */
}
.supporting-menu.custom-supporting-menu .popout-footer {
  margin: 0;
  flex: 1 0 0;
}
.supporting-menu.custom-supporting-menu .popout-list {
  background: var(--COLOR-BG-ACCENT);
}
.supporting-menu.custom-supporting-menu .popout__toggle {
  min-height: 20px;
  padding: 10px;
  margin: 0;
}
@media only screen and (min-width: 750px) {
  .supporting-menu.custom-supporting-menu .popout__toggle {
    min-height: 40px;
  }
}
.supporting-menu.custom-supporting-menu .supporting-menu__inner {
  min-height: 50px;
  padding-inline: var(--LAYOUT-OUTER-MEDIUM);
  gap: 0;
  -moz-column-gap: 10px;
       column-gap: 10px;
  background: var(--COLOR-BG-ACCENT);
}
@media only screen and (max-width: 750px) {
  .supporting-menu.custom-supporting-menu .supporting-menu__inner {
    padding: var(--LAYOUT-OUTER-SMALL);
    row-gap: 10px;
  }
}
@media only screen and (min-width: 750px) {
  .supporting-menu.custom-supporting-menu .supporting-menu__inner {
    row-gap: 20px;
  }
}
.supporting-menu.custom-supporting-menu .supporting-menu__wrapper {
  margin-top: -1px;
  padding-bottom: var(--LAYOUT-OUTER-MEDIUM);
}
.supporting-menu.custom-supporting-menu .supporting-menu__item {
  flex: unset;
}
@media only screen and (min-width: 750px) {
  .supporting-menu.custom-supporting-menu .supporting-menu__item {
    display: flex;
    align-items: center;
    min-height: 60px;
  }
}
.supporting-menu.custom-supporting-menu .supporting-menu__item--copyright {
  order: 0;
}
@media only screen and (max-width: 750px) {
  .supporting-menu.custom-supporting-menu .supporting-menu__copyright li {
    padding: 0 var(--gap);
  }
}
.supporting-menu.custom-supporting-menu .supporting-menu__item--localization {
  order: 2;
}
.supporting-menu.custom-supporting-menu .supporting-menu__item--payment {
  order: 1;
}
.supporting-menu.custom-supporting-menu .supporting-menu__item--credit {
  order: 3;
}
@media only screen and (max-width: 750px) {
  .supporting-menu.custom-supporting-menu .popout-footer,
  .supporting-menu.custom-supporting-menu .supporting-menu__copyright,
  .supporting-menu.custom-supporting-menu .supporting-menu__payment,
  .supporting-menu.custom-supporting-menu .supporting-menu__copyright {
    justify-content: center;
  }
}

collection-component.collection .collection__nav {
  border: none;
}
collection-component.collection .collection__nav .popout__toggle {
  border: none;
  padding-block: var(--inner);
}
collection-component.collection .collection__sidebar__slider {
  border: none;
}
collection-component.collection .grid-outer {
  padding-top: 0;
}
collection-component.collection .filter-group__heading {
  padding-bottom: 10px;
}

/* ========== Collections Hover ========== */
.custom-collection-list-hover {
  position: relative;
}

.collection-hover__button {
  text-transform: unset;
}

.floating-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.floating-header__title {
  margin: 0;
}

.index-product {
  /* =========== Product Blocks =========== */
  /* =========== Features =========== */
  /* =========== Features =========== */
}
@media only screen and (min-width: 990px) {
  .index-product .product__page {
    display: flex;
  }
}
@media only screen and (min-width: 990px) {
  .index-product .product__images {
    width: 100%;
  }
}
@media only screen and (min-width: 990px) {
  .index-product .product__content .form__width {
    max-width: 40vw !important;
  }
}
.index-product .product__block {
  /* ----- Product Variant Picker ----- */
  /* ----- Product Meta ----- */
  /* ----- Radios ----- */
  /* ----- Selector Wrapper ----- */
  /* ----- Accordion ----- */
  /* ----- Accordion ----- */
}
.index-product .product__block.block-padding {
  --block-padding-top: 10px;
  --block-padding-bottom: 10px;
}
.index-product .product__block.block-padding:not(.block__icon__container--half):first-of-type {
  --block-padding-top: 0;
}
.index-product .product__block.block-padding:not(.block__icon__container--half) > * {
  margin: 0;
}
.index-product .product__block.product__block--variant-picker {
  border-top: none;
}
.index-product .product__block.product__block--variant-picker .selector-wrapper--swatches .radio__legend__option-name {
  display: none;
}
.index-product .product__block.product__block--variant-picker .selector-wrapper--swatches .radio__legend__value {
  font-family: var(--font-family-heading-2-alt);
  font-size: var(--font-size-badge-lg);
  font-weight: var(--font-weight-badge-lg);
  text-transform: uppercase;
}
.index-product .product__block.product__block--meta .product-meta__top {
  display: flex;
  justify-content: space-between;
}
.index-product .product__block.product__block--meta .meta-volume__field + .meta-volume__field:before {
  content: "/";
}
.index-product .product__block .radio__buttons {
  text-align: right;
}
.index-product .product__block .selector-wrapper.selector-wrapper--swatches .radio__legend {
  display: flex;
  align-items: center;
  align-items: flex-start;
}
.index-product .product__block.block__icon__container {
  margin-bottom: var(--gap);
}
.index-product .product__block.product__block--accordion {
  margin-top: 0;
}
.index-product .product__block .product__title__wrapper {
  padding: 0;
}
.index-product .product__block .block__icon {
  margin-right: 5px;
}
.index-product .product-accordion .accordion:first-of-type {
  border-top: 0;
}
.index-product .product__feature {
  padding: var(--inner);
}
.index-product .product__feature__content .btn--text {
  padding-bottom: 0;
}

.logos.custom-logos .logos__slider-text {
  padding: 0;
  margin-bottom: 50px;
}
@media only screen and (min-width: 750px) {
  .logos.custom-logos .logos__slider-text {
    margin-bottom: 80px;
  }
}

.highlights.custom-highlights {
  /*
  .highlights__items {
    --gap: 5px;

    margin: 0;

    @include respond-to($medium-up) {
      display: grid;
      gap: var(--gap);
      grid-template-columns: repeat(var(--columns-desktop), minmax(0, 1fr));
    }

  }

  .highlights__item {
    margin: var(--gap) 0;

    @include respond-to($large-up) {
      margin: 0;
      padding: 0;
    }
  }
    */
  /* ----- Mobile Grid ----- */
  /* ----- Mobile Slider ----- */
}
.highlights.custom-highlights .highlights__items {
  --gap: 5px;
}
@media only screen and (min-width: 750px) {
  .highlights.custom-highlights .highlights__items {
    --gap: 10px;
  }
}
.highlights.custom-highlights a.highlights__item-inner {
  transition: opacity var(--transition-duration) var(--transition-ease);
}
.highlights.custom-highlights a.highlights__item-inner:hover, .highlights.custom-highlights a.highlights__item-inner:focus {
  opacity: 0.8;
}
.highlights.custom-highlights .highlights__items--mobile-grid {
  margin: 0 calc(-1 * var(--gap));
}
.highlights.custom-highlights .highlights__items--mobile-grid .highlights__items {
  margin: 0;
}
@media only screen and (min-width: 750px) {
  .highlights.custom-highlights .highlights__items--mobile-grid .highlights__items {
    display: grid;
    gap: var(--gap);
    grid-template-columns: repeat(var(--columns-desktop), minmax(0, 1fr));
  }
}
@media only screen and (max-width: 750px) {
  .highlights.custom-highlights .highlights__items--mobile-grid .highlights__item {
    flex: 1 0 50%;
    margin: 0 !important;
    padding-bottom: calc(var(--gap) * 2);
  }
}
.highlights.custom-highlights .highlights__items--mobile-slider .highlights__item {
  margin: 0 !important;
  padding-bottom: calc(var(--gap) * 2);
}
@media only screen and (max-width: 750px) {
  .highlights.custom-highlights .highlights__items--mobile-slider {
    gap: calc(var(--gutter) / 2);
    padding-inline: calc(var(--gutter) / 2);
  }
  .highlights.custom-highlights .highlights__items--mobile-slider .highlights__item {
    flex: 1 0 50%;
  }
  .highlights.custom-highlights .highlights__items--mobile-slider .highlights__item {
    width: calc(50% - var(--gutter));
    margin: 0 !important;
  }
  .highlights.custom-highlights .highlights__items--mobile-slider:after {
    content: none;
  }
}

.section-columns.custom-section-columns .grid__heading-holder.additional-padding {
  margin: 0 0 6rem;
}
.section-columns.custom-section-columns .column__icon-background {
  width: calc(var(--icon-size, 24px) * 2);
  height: calc(var(--icon-size, 24px) * 2);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--icon-background);
  border-radius: 100%;
}
.section-columns.custom-section-columns .column__icon-background .icon__animated {
  margin: 0;
  width: var(--icon-size, 24px);
  height: var(--icon-size, 24px);
}
.section-columns.custom-section-columns #PBarNextFrameWrapper {
  display: none;
}

.index-image-text.custom-index-image-text {
  /* ----- Accordions ----- */
}
.index-image-text.custom-index-image-text.index-image-text--flush-padding .brick__block__text {
  flex-basis: 100%;
  margin: 0;
}
.index-image-text.custom-index-image-text .hero__content {
  height: 100%;
}
.index-image-text.custom-index-image-text .inner-color-scheme > .brick__block {
  overflow: hidden;
}
.index-image-text.custom-index-image-text .inner-color-scheme > .brick__block:first-child {
  border-top-right-radius: var(--block-radius);
  border-bottom-right-radius: var(--block-radius);
}
.index-image-text.custom-index-image-text .inner-color-scheme > .brick__block:last-child {
  border-top-left-radius: var(--block-radius);
  border-bottom-left-radius: var(--block-radius);
}
.index-image-text.custom-index-image-text .inner-color-scheme.brick__section--reversed > .brick__block:first-child {
  border-top-left-radius: var(--block-radius);
  border-bottom-left-radius: var(--block-radius);
}
.index-image-text.custom-index-image-text .inner-color-scheme.brick__section--reversed > .brick__block:last-child {
  border-top-right-radius: var(--block-radius);
  border-bottom-right-radius: var(--block-radius);
}
.index-image-text.custom-index-image-text collapsible-elements {
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 100%;
}

@media only screen and (min-width: 750px) {
  .accordion-group.custom-accordion-group .accordion-group--columns {
    display: grid;
    grid-template-columns: minmax(0, 1fr) minmax(0, 3fr);
    gap: var(--gutter);
    position: relative;
  }
  .accordion-group.custom-accordion-group .accordion-group--columns .section-header {
    display: inline-flex;
    position: sticky;
    top: var(--gap);
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
  }
}

.section-columns.custom-multicolumn .column__image {
  position: relative;
  margin-bottom: 0;
}
.section-columns.custom-multicolumn .column__image + .column__content {
  margin-top: var(--inner);
}
.section-columns.custom-multicolumn .column__image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--gap);
  padding: var(--gap);
}

section.custom-custom-content .hero__content {
  height: 100%;
}
section.custom-custom-content .brick__block__text {
  width: 100%;
}
section.custom-custom-content .brick__block__text .hero__rte > p {
  max-width: 450px;
  margin: auto;
}

.text-promo.custom-text-promo .hero__content {
  flex-direction: column;
  gap: var(--gap);
}
@media only screen and (min-width: 480px) {
  .text-promo.custom-text-promo .hero__content {
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
  }
}
.text-promo.custom-text-promo .hero__content-left,
.text-promo.custom-text-promo .hero__content-right {
  display: grid;
  grid-auto-flow: row;
  gap: var(--gap);
  width: 100%;
}
@media only screen and (min-width: 480px) {
  .text-promo.custom-text-promo .hero__content-left,
  .text-promo.custom-text-promo .hero__content-right {
    max-width: 320px;
  }
}
.text-promo.custom-text-promo .hero__content-left > *,
.text-promo.custom-text-promo .hero__content-right > * {
  margin: 0;
}
.bespoke-products-carousel .grid-item {
  --aspect-ratio: calc(640/360);
  --item-width: calc(100vw - var(--outer) * 2 - 50px);
  --item-height: calc(var(--item-width) * var(--aspect-ratio));
  flex: 0 0 var(--item-width);
  max-width: var(--item-width);
  margin-right: var(--gap);
}
@media only screen and (min-width: 750px) {
  .bespoke-products-carousel .grid-item {
    --item-width: 360px;
  }
}
.bespoke-products-carousel .grid--mobile-slider .grid-item {
  scroll-snap-align: center;
}

section.bespoke-product-compare {
  /* ----- Compare Grid ----- */
  /* ----- Compare Table ----- */
}
section.bespoke-product-compare .compare-wrapper {
  display: grid;
}
@media only screen and (min-width: 990px) {
  section.bespoke-product-compare .compare-wrapper {
    grid-template-columns: minmax(0, 1fr) minmax(0, 2fr);
  }
}
@media only screen and (max-width: 990px) {
  section.bespoke-product-compare .compare-wrapper {
    grid-auto-flow: row;
    gap: var(--gap);
  }
}
section.bespoke-product-compare .compare-sidebar {
  padding: var(--outer);
}
section.bespoke-product-compare .compare-content {
  padding: var(--inner);
}
section.bespoke-product-compare .compare-grid {
  display: flex;
  align-items: flex-end;
}
section.bespoke-product-compare .compare-grid__item {
  display: flex;
  flex-direction: column;
}
@media only screen and (max-width: 750px) {
  section.bespoke-product-compare .compare-grid__item {
    flex: 1 0 50%;
  }
}
section.bespoke-product-compare .compare-grid__item-field {
  display: flex;
  align-items: center;
  min-height: 40px;
}
section.bespoke-product-compare .compare-grid__item-field:nth-child(2n-1) {
  background: var(--bg);
}
@media only screen and (min-width: 750px) {
  section.bespoke-product-compare .compare-grid__item-field {
    height: 40px;
  }
}
section.bespoke-product-compare .compare-grid__item-field--spacer {
  text-align: right;
}
section.bespoke-product-compare .compare-grid__item-field--label {
  text-align: right;
}
@media only screen and (max-width: 750px) {
  section.bespoke-product-compare .compare-table {
    flex-wrap: wrap;
  }
}
section.bespoke-product-compare .compare-table__legend {
  flex: 0 1 230px;
}
section.bespoke-product-compare .compare-table__legend .compare-grid__item-field {
  justify-content: flex-end;
  text-align: end;
}
@media only screen and (min-width: 750px) {
  section.bespoke-product-compare .compare-table__legend .compare-grid__item-field {
    border-top-left-radius: var(--corner-radius);
    border-bottom-left-radius: var(--corner-radius);
  }
}
@media only screen and (min-width: 750px) {
  section.bespoke-product-compare .compare-table__legend .compare-grid__item-field {
    border-top-left-radius: var(--corner-radius);
    border-bottom-left-radius: var(--corner-radius);
  }
}
@media only screen and (min-width: 750px) {
  section.bespoke-product-compare .compare-table__product:last-of-type .product-compare-field {
    border-top-right-radius: var(--corner-radius);
    border-bottom-right-radius: var(--corner-radius);
  }
}
section.bespoke-product-compare .compare-table__spacer {
  flex: 1 0 20px;
}
@media only screen and (min-width: 750px) {
  section.bespoke-product-compare .compare-table__product .compare-grid__item-image {
    min-width: 300px;
    height: 440px;
  }
}
section.bespoke-product-compare .product-compare-item__product {
  overflow: hidden;
}
section.bespoke-product-compare .product-compare-item__product > .product-item {
  height: 100%;
}
section.bespoke-product-compare .product-compare-item__product .product-item__image {
  max-height: 100%;
}
@media only screen and (min-width: 750px) {
  section.bespoke-product-compare .product-compare-field {
    overflow: hidden;
    width: 100%;
    flex: 1 0 auto;
    white-space: nowrap;
    padding-right: 1em;
  }
}
@media only screen and (max-width: 750px) {
  section.bespoke-product-compare .product-compare-field:nth-child(2n) .product-compare-field__label {
    display: none;
  }
}
@media only screen and (max-width: 750px) {
  section.bespoke-product-compare .product-compare-field {
    --padding: 10px;
    position: relative;
    align-items: flex-start;
    gap: calc(var(--padding) * 2);
    min-height: 50px !important;
    padding: var(--padding);
  }
  section.bespoke-product-compare .product-compare-field .product-compare-field__label {
    position: absolute;
    top: var(--padding);
    left: var(--padding);
  }
  section.bespoke-product-compare .product-compare-field .product-compare-field__value {
    padding-top: calc(var(--padding) * 2);
  }
}
@media only screen and (max-width: 750px) and (min-width: 750px) {
  section.bespoke-product-compare .product-compare-field .product-compare-field__value {
    display: block;
    width: 100%;
  }
}

.bespoke-reviews-carousel .grid-item {
  --item-width: calc(100vw - var(--outer) * 2 - 50px);
  flex: 0 0 var(--item-width);
  margin-right: var(--gap);
}
@media only screen and (min-width: 750px) {
  .bespoke-reviews-carousel .grid-item {
    --item-width: calc(70vw - var(--outer) * 2 - 50px);
  }
}
@media only screen and (min-width: 1400px) {
  .bespoke-reviews-carousel .grid-item {
    --item-width: calc(50vw - var(--outer) * 2);
  }
}
.bespoke-reviews-carousel .grid--mobile-slider .grid-item {
  scroll-snap-align: center;
}

.bespoke-tabbed-gallery .grid-item {
  height: var(--item-height);
  margin-right: var(--gap);
}
.bespoke-tabbed-gallery .grid--mobile-slider .grid-item {
  scroll-snap-align: center;
}

/* ==============================
   Sections
   ============================== */
.section-header {
  display: flex;
  gap: var(--gap);
  margin-bottom: var(--gutter);
}
@media only screen and (max-width: 750px) {
  .section-header {
    flex-direction: column;
    text-align: center;
    align-items: center;
  }
}
.section-header .section-header__actions > *,
.section-header .section-header__text > * {
  margin: 0;
}
.section-header .section-header__text {
  display: grid;
  grid-auto-flow: row;
  gap: calc(var(--gap) / 2);
}
.section-header.section-header--vertical {
  flex-direction: column;
  text-align: center;
  align-items: center;
}
.section-header.section-header--horizontal {
  justify-content: space-between;
  align-items: flex-end;
}
@media only screen and (max-width: 750px) {
  .section-header.section-header--horizontal {
    flex-direction: column;
    text-align: left;
    align-items: flex-start;
    gap: calc(var(--gap) * 2);
  }
}

/* ----- Wrappers ----- */
.wrapper--small {
  max-width: 870px;
  margin: 0 auto;
  padding-left: var(--outer);
  padding-right: var(--outer);
}

/* 7. Page-Specific Styles */
/* 8. Components */
/**
 * Icons
 */
.icon {
  width: var(--icon-size, 24px);
  height: var(--icon-size, 24px);
}

.icon.custom-icon circle,
.icon.custom-icon ellipse,
.icon.custom-icon g,
.icon.custom-icon line,
.icon.custom-icon path,
.icon.custom-icon polygon,
.icon.custom-icon polyline,
.icon.custom-icon rect {
  fill: currentColor;
  stroke: none;
}
.icon.icon--fill {
  stroke: var(--icons, currentColor);
  stroke-width: 0.5;
  fill: var(--icons, currentColor);
}

/* --- Fieldset --- */
/* --- Legend --- */
.radio__legend__option-name {
  font-family: var(--font-family-heading-2-alt);
  font-size: var(--font-size-badge-lg);
  font-weight: var(--font-weight-badge-lg);
  text-transform: uppercase;
}

.radio__legend__value {
  font-size: var(--font-size-text-xs);
}

/* --- Radio Buttons --- */
.radio__buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: flex-start;
  gap: calc(var(--gap) / 4);
}
.radio__buttons .radio__button {
  padding: 0;
}

.radio__fieldset {
  display: flex;
}
.radio__fieldset .radio__legend {
  flex: 0 0 120px;
}
.radio__fieldset .radio__buttons {
  margin: 0;
  flex: 1 0 0;
}
.radio__fieldset .radio__button label {
  padding: 0.5em 0.8em;
}

.btn {
  padding: 1em 1.8em;
  line-height: var(--line-height-button);
  font-size: var(--font-size-button);
  cursor: pointer;
  /* --- Sizes --- */
  /* --- Layout --- */
  /* --- Type --- */
  /* --- Style --- */
  /* --- Elements --- */
}
.btn .price {
  font-size: calc(var(--font-size-product-card-price) - 0.2rem);
}
.btn.btn--small {
  padding: 0.6em 1.4em;
}
.btn.btn--large {
  padding: 1.4em 2em;
  font-size: var(--font-size-button-lg);
}
.btn.btn--huge {
  padding: 1.8em 2.4em;
  font-size: var(--font-size-button-lg);
}
.btn.btn--text {
  padding-inline: 0;
}
.btn.btn--full {
  width: 100%;
}
.btn.btn--no-padding-x {
  padding-left: 0;
  padding-right: 0;
}
.btn.btn--secondary {
  --btn-border: var(--BTN-SECONDARY-BORDER);
  --btn-border-hover: var(--BTN-SECONDARY-BORDER);
  --btn-bg: var(--BTN-SECONDARY-BG);
  --btn-text: var(--BTN-SECONDARY-TEXT);
}
.btn .btn__price::before {
  content: "•";
  margin: 0 5px;
  visibility: hidden;
}

/* --- Button Outer --- */
.btn__outer > button, .btn__outer .btn {
  box-shadow: 0 0 0 1px var(--border) inset;
  border-radius: 100px;
}
.btn__outer .btn__plus {
  --icon-outer-size: 32px;
  --icon-size: 30px;
  --icon-offset: 4px;
  margin: 0 0 0 var(--icon-offset);
  -webkit-mask-image: var(--icon-plus);
          mask-image: var(--icon-plus);
  transition: width var(--transition-duration) var(--transition-ease), opacity var(--transition-duration) var(--transition-ease);
  /*
  &.btn__plus--preorder {
    --icon-size: 24px;
    mask-image: var(--icon-add-cart);
  }
  */
}
.btn__outer .btn__plus > button {
  justify-content: inherit;
}
.btn__outer .btn__plus + .btn__text {
  margin-left: 4px;
}
.btn__outer .btn__plus:hover, .btn__outer .btn__plus:focus {
  opacity: 0.5;
}
.btn__outer .btn__plus.btn__plus--preorder, .btn__outer .btn__plus.btn__plus--quick-add {
  --icon-size: 20px;
  --icon-offset: 6px;
  -webkit-mask-image: var(--icon-add-cart);
          mask-image: var(--icon-add-cart);
}
.btn__outer .btn__plus .btn__text {
  margin-left: 2px;
  font-size: var(--font-size-text-xs);
}

/* ========== Form Elements ========== */
input,
textarea,
select,
.popout__toggle,
.input-group {
  margin: 0;
  background: var(--bg);
}

/* ----- Custom Form ----- */
.custom-form__label {
  margin-bottom: 0.5em;
}

/* ----- Input Group ----- */
.input-group {
  display: flex;
  gap: var(--gap);
  border: none;
}
.input-group.input-group--bordered {
  border: 1px solid var(--border);
}
.input-group .input-group__field {
  align-items: center;
  flex: 1 0 auto;
}
.input-group .input-group__input {
  align-items: center;
}
.input-group .input-group__btn {
  display: flex;
  align-items: center;
}
.input-group .input-group__btn > span {
  line-height: 1;
}

/* ----- Field ----- */
.field {
  --border: var(--COLOR-BORDER);
  padding: 1em;
  border: 1px solid var(--border);
}
.field:hover {
  border: 1px solid var(--border-light);
}
.field:focus {
  border: 1px solid var(--border-dark);
}

.product-information .price,
.price {
  font-family: var(--font-family-product-price);
  font-weight: var(--font-weight-product-price);
  font-size: var(--font-size-product-card-price);
  color: var(--color-price);
}

.product-information .new-price,
.new-price {
  color: var(--color-price--sale);
}

.product-information .old-price,
.product__price--strike,
.old-price {
  color: var(--color-price--compare);
  text-decoration: line-through;
}

.new-price {
  margin-right: 4px;
}

.badge-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
@media only screen and (min-width: 750px) {
  .badge-list {
    gap: 6px;
  }
}

.badge {
  --badge-border: RGBA(var(--COLOR-BORDER--RGB) / 0.4);
  padding: 2px 4px;
  border-width: 1px;
  font-family: var(--font-family-badge);
  font-style: normal;
  font-weight: var(--font-weight-badge);
  font-size: var(--font-size-badge);
  text-transform: uppercase;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  color: var(--text);
  background: var(--bg-accent);
  border-color: var(--bg-accent);
  border-radius: var(--corner-radius-sm);
  border-style: solid;
  /* ----- Sizes ----- */
  /* ----- Styles ----- */
  /* ----- Styles ----- */
}
@media only screen and (min-width: 750px) {
  .badge {
    padding: 4px 8px;
  }
}
.badge.badge--small {
  padding: 3px 8px;
}
.badge.badge--xs {
  padding: 2px 6px;
}
.badge.badge--reversed {
  color: var(--text);
  background: var(--bg);
  border-color: var(--badge-border);
}
.badge.badge--white {
  color: var(--text);
  background: var(--color-basic-white);
}
.badge.badge--secondary {
  color: var(--text);
  background: var(--bg-accent-lighten);
  border-color: var(--bg-accent-lighten);
}
.badge.badge--soldout, .badge.badge--darken {
  color: var(--text);
  background: var(--bg-accent-darken);
  border-color: var(--bg-accent-darken);
}
.badge.badge--lighten {
  color: var(--text);
  background: var(--bg-accent-lighten);
  border-color: var(--bg-accent-lighten);
}
.badge.badge--border {
  border-color: var(--text);
}

[data-collapsible-trigger] .icon {
  right: 0;
}

.accordion {
  border-top: none;
}
.accordion__title {
  gap: 0.8em;
  padding: 1rem 0 1rem 0;
}

.accordion-icon.accordion-icon--number {
  --icon-inner-size: 28px;
  position: relative;
  margin-right: var(--icon-inner-size);
  height: 100%;
}
.accordion-icon.accordion-icon--number .accordion-icon__inner {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--icon-inner-size);
  width: var(--icon-inner-size);
  background: var(--bg-accent);
  border-radius: 100px;
  transform: translateY(-50%);
}

.image-overlay__content {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 0.5rem;
  width: 100%;
  margin-top: auto;
}
.image-overlay__content > * {
  margin: 0;
}

.image-overlay__product {
  margin: auto;
  min-width: 300px;
  width: 40%;
}

.image-overlay__actions {
  margin-top: auto;
}

.custom-products-image .image-overlay {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: var(--gutter);
  opacity: 1;
  background-color: RGBA(var(--overlay-color--rgb), var(--overlay-opacity));
}

@media only screen and (max-width: 750px) {
  .product-quick-add__form__inner {
    flex-basis: auto;
  }
}

.rating-dots {
  --dot-size: 8px;
  display: flex;
  gap: calc(var(--dot-size) / 2);
}
.rating-dots .rating-dot {
  width: var(--dot-size);
  height: var(--dot-size);
  border-radius: 100px;
  background: var(--text);
  opacity: 0.5;
}
.rating-dots .rating-dot--fill {
  opacity: 1;
}

.product__block--lines {
  border-color: var(--border-light);
}

.product__submit .btn__price:before {
  visibility: hidden;
}

.product-item {
  --aspect-ratio-mobile: calc(var(--PRODUCT-GRID-ASPECT-RATIO--MOBILE) + var(--product-item-image-background-padding--mobile));
  --aspect-ratio-desktop: calc(var(--PRODUCT-GRID-ASPECT-RATIO) + var(--product-item-image-background-padding));
  overflow: hidden;
  border-radius: var(--block-radius);
  /* ===== Variations ===== */
  /* ===== Elements ===== */
  /* --- Image --- */
  /* --- Product Info --- */
  /* --- Swatches --- */
  /* --- Quick-Add --- */
}
.product-item.product-item--left .radio__fieldset--swatches .swatch__button {
  --swatch-size: 12px;
  margin-right: 5px;
}
@media only screen and (max-width: 750px) {
  .product-item.product-item--featured .grid__heading-holder {
    padding-top: var(--aspect-ratio-mobile);
  }
}
@media only screen and (max-width: 750px) {
  .product-item.product-item--aligned .grid__heading-holder {
    padding-top: var(--aspect-ratio-desktop);
  }
}
.product-item .product-item__image {
  padding-top: var(--aspect-ratio-mobile);
}
@media only screen and (min-width: 750px) {
  .product-item .product-item__image {
    padding-top: var(--aspect-ratio-desktop);
  }
}
.product-item .product-item__badge-list {
  position: absolute;
  top: 8px;
  left: 8px;
}
@media only screen and (max-width: 750px) {
  .product-item hover-images {
    display: none;
  }
}
@media only screen and (max-width: 750px) {
  .product-item .product-item__bg__slider {
    height: 100%;
  }
}
@media only screen and (max-width: 750px) {
  .product-item .product-item__bg__slide:not(:first-child) {
    display: none;
  }
}
.product-item .product-item__bg,
.product-item .product-item__bg__under {
  background: var(--product-item-image-background-color);
}
@media only screen and (max-width: 990px) {
  .product-item .product-information {
    padding-left: 0;
    padding-right: 0;
  }
}
.product-item .product-item__title {
  font-family: var(--font-family-heading-1);
  font-size: var(--font-size-product-card-title);
  font-weight: var(--font-weight-product-title);
  text-transform: uppercase;
}
.product-item .product-item__description {
  font-size: var(--font-size-product-card-description);
  font-weight: var(--font-weight-product-description);
}
.product-item .product-item__price {
  font-family: var(--font-family-product-price);
  font-size: var(--font-size-product-card-price);
  font-weight: var(--font-weight-product-price);
}
.product-item .product-item__info {
  padding-inline: 10px;
}
.product-item .product-item__info-bottom {
  margin-top: 0.5em;
  padding-top: 0.5em;
  border-top: 1px solid var(--border);
}
@media only screen and (max-width: 750px) {
  .product-item .product-item__info-bottom {
    display: none;
  }
}
.product-item .product-item__info-bottom-inner {
  display: flex;
}
.product-item .product-item__info-top {
  display: grid;
  grid-auto-flow: row;
  gap: 4px;
}
.product-item .product-item__info-top > * {
  margin: 0;
}
.product-item .selector-wrapper__scrollbar {
  min-height: 22px;
  padding-block: 0;
  padding-bottom: 5px;
}
.product-item .product-item__swatches__holder {
  min-height: 22px;
  padding-top: 5px;
}
.product-item .product-item__swatches__holder .radio__fieldset__arrow--prev {
  transform: translateX(-150%);
  visibility: hidden;
}
.product-item .product-item__swatches__holder .radio__fieldset {
  padding: 0;
}
.product-item .quick-add__holder {
  left: 10px;
  right: 10px;
  width: unset;
}
.product-item .quick-add__holder .btn {
  border: 1px solid var(--border);
}
@media only screen and (max-width: 750px) {
  .product-item .quick-add__holder {
    left: 0;
    right: unset;
  }
}

.product-upsell {
  --upsell-image-width: 90px;
  min-height: 120px;
  flex-wrap: nowrap;
}
.product-upsell .product-upsell__image__thumb {
  padding: 0;
}
.product-upsell .product-upsell__content {
  padding: calc(var(--gap) / 2) var(--gap);
  width: calc(100% - (var(--gap)));
}
.product-upsell .product-upsell__holder--button,
.product-upsell .product-upsell__content {
  padding-right: calc(var(--gap) / 2 + var(--outer)) !important;
}
.product-upsell .product-upsell__link {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: var(--gap);
}
.product-upsell .product-upsell__title {
  margin-bottom: 0.25em;
}
.product-upsell .product-upsell__image {
  flex: 0 0 var(--upsell-image-width);
  width: var(--upsell-image-width);
}
.product-upsell .product-upsell__content-bottom {
  margin-top: auto;
}
.product-upsell .product-upsell__price {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: calc(var(--gap) / 2);
}
.product-upsell .product-upsell__price > * {
  margin: 0;
}

.product-carousel-item {
  position: relative;
  min-height: var(--item-height);
  background: var(--bg);
}
.product-carousel-item .btn__outer {
  bottom: calc(var(--gap) / 2);
  right: calc(var(--gap) / 2);
}

@media (pointer: fine) {
  .product-carousel-item--link {
    transition: opacity var(--transition-duration) var(--transition-ease);
  }
  .product-carousel-item--link:focus, .product-carousel-item--link:hover {
    opacity: 0.8;
  }
}

.product-carousel-item__overlay {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: calc(var(--gap) / 2);
  background: RGBA(0, 0, 0, 0);
}

.product-carousel-item__overlay-content {
  position: relative;
  z-index: 1;
  display: flex;
  margin-top: auto;
  background: var(--bg);
}

.product-carousel-item__overlay-text {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 100%;
  padding: calc(var(--gap) / 2);
}
.product-carousel-item__overlay-text > * {
  margin: 0;
}

.product-carousel-item__overlay-thumbnail {
  max-width: 80px;
  height: 100%;
  flex: 1 0 80px;
}
.product-carousel-item__overlay-thumbnail > figure {
  height: 100%;
}

.product-carousel-item__price {
  margin-top: auto;
}

.product-carousel-item__link {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.product-carousel-item__background {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
}

@media only screen and (max-width: 750px) {
  .grid--mobile-slider .grid-item {
    width: 65%;
  }
}

.grid__heading-holder.grid__heading-holder--split {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
}

grid-slider {
  --gap: 20px;
}

.featured-review {
  --content-width: 320px;
  /* ----- LAYOUT ----- */
  /* ----- MEDIA ----- */
  /* ----- CONTENT ----- */
}
.featured-review .featured-review__media,
.featured-review .featured-review__content {
  width: 100%;
  max-width: var(--section-width);
}
.featured-review .featured-review__inner {
  display: flex;
  height: 100%;
}
@media only screen and (max-width: 750px) {
  .featured-review .featured-review__inner {
    flex-direction: column;
  }
}
.featured-review .featured-review__media {
  position: relative;
}
@media only screen and (max-width: 750px) {
  .featured-review .featured-review__media {
    min-height: var(--content-width);
  }
}
@media only screen and (min-width: 750px) {
  .featured-review .featured-review__media {
    min-height: 470px;
  }
}
.featured-review .featured-review__rating .icon {
  width: 16px;
}
.featured-review .featured-review__content {
  display: flex;
  flex-direction: column;
  gap: var(--gap);
  height: 100%;
  padding: var(--gap);
}
.featured-review .featured-review__content-top,
.featured-review .featured-review__content-bottom {
  display: grid;
  gap: 1rem;
  grid-auto-flow: row;
}
.featured-review .featured-review__content-top > *,
.featured-review .featured-review__content-bottom > * {
  margin: 0;
}
.featured-review .featured-review__content-top {
  margin-bottom: auto;
}
.featured-review .featured-review__content-bottom {
  margin-top: auto;
}
.featured-review .featured-review__text p {
  margin-top: 0;
}
.featured-review .featured-review__author {
  color: var(--text-light);
}
.featured-review .featured-review__caption {
  display: flex;
  align-items: flex-start;
  gap: 0.5em;
  padding: 0.8em;
  background: var(--bg-accent);
  font-size: var(--text-sm);
  border-radius: var(--corner-radius);
}

tabs-component.tabs-collections .tabs {
  padding: 0;
  border-bottom: 1px solid var(--border);
}
tabs-component.tabs-collections .tabs .tab-link {
  font-family: var(--font-family-heading-2-alt);
  font-size: var(--font-size-badge-lg);
  font-weight: var(--font-weight-badge-lg);
  text-transform: uppercase;
  padding: 0;
  padding-bottom: 6px;
  margin-right: 15px;
}
tabs-component.tabs-collections .tabs .tab-link > span {
  position: static;
}
tabs-component.tabs-collections .tabs .tab-link > span:after {
  bottom: 0;
}
.tab-content {
  padding: var(--gap) 0;
}

.tabbed-gallery-image {
  position: relative;
  width: var(--item-width, 300px);
  height: var(--item-height, auto);
  background: var(--bg);
}
.tabbed-gallery-image .btn__outer {
  bottom: calc(var(--gap) / 2);
  right: calc(var(--gap) / 2);
}

@media (pointer: fine) {
  .tabbed-gallery-image--link {
    transition: opacity var(--transition-duration) var(--transition-ease);
  }
  .tabbed-gallery-image--link:focus, .tabbed-gallery-image--link:hover {
    opacity: 0.8;
  }
}

.hero__spacer {
  border: 0;
  min-height: 30px;
  margin: auto;
}

.hero__max-width {
  max-width: var(--block-max-width);
}
@media only screen and (max-width: 990px) {
  .hero__max-width {
    max-width: calc(var(--block-max-width) * 1.5);
  }
}
@media only screen and (max-width: 480px) {
  .hero__max-width {
    max-width: none;
  }
}

.hero__content {
  padding: calc(var(--gutter) / 2);
  width: 100%;
}
@media only screen and (min-width: 750px) {
  .hero__content {
    padding: var(--gutter);
  }
}
.hero__content.hero__content--compact {
  margin-bottom: 0;
}
@media only screen and (min-width: 750px) {
  .hero__content.hero__content--compact {
    padding: 0;
  }
}

@media only screen and (min-width: 990px) {
  .hero__description {
    max-width: var(--content-max-width, 100%);
    margin-inline: auto;
  }
}

.hero__button {
  margin: 0;
}

/* ========== Brick Section ========== */
.brick__block {
  position: relative;
  /* ----- Products ----- */
  /* ----- Text ----- */
  /* ----- Background Image ----- */
}
.brick__block.brick__block--products {
  --inner: calc(var(--gutter) / 4);
}
@media only screen and (min-width: 750px) {
  .brick__block.brick__block--products {
    --inner: calc(var(--gutter) / 2);
    padding-right: calc(var(--gutter) / 2);
  }
}
.brick__block.brick__block--text .brick__block__text {
  margin: 0;
  flex-basis: unset;
}
@media only screen and (min-width: 750px) {
  .brick__block.brick__block--text .brick__block__text {
    padding: var(--gutter);
  }
}
.brick__block.brick__block--text .hero__subheading,
.brick__block.brick__block--text .hero__rte {
  margin: var(--block-padding-bottom, var(--line)) 0;
}
.brick__block.brick__block--background-image .hero__content,
.brick__block.brick__block--background-image .brick__block__text {
  background: none;
}
.brick__block.brick__block--background-image .brick__block__background-image {
  opacity: var(--background-image-opacity);
}
.brick__block.brick__block--background-image .brick__block__text {
  position: relative;
  z-index: 1;
}

.brick__block__text {
  justify-content: center;
}

@media only screen and (min-width: 750px) {
  .brick__block.brick__section__extra-padding {
    padding-right: var(--outer);
  }
}
@media only screen and (max-width: 750px) {
  .brick__block.brick__section__extra-padding {
    max-width: none;
    margin: 0 auto;
    padding: var(--outer);
  }
}

.brick__block__background-image {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
}

.newsletter__wrapper {
  margin-top: 0.5em;
}

newsletter-component > .newsletter-form {
  margin: 0;
  max-width: none;
}

.loyalty-points {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: calc(var(--gap) / 2);
}
.loyalty-points .loyalty-points__icon {
  display: block;
  width: 20px;
}
.loyalty-points .loyalty-points__icon svg {
  width: 100%;
}

@media only screen and (max-width: 750px) {
  .search-results-item__image {
    padding-top: var(--PRODUCT-GRID-ASPECT-RATIO--MOBILE);
  }
}

/* ----- Content Box ----- */
/*

.content-box {

  --padding: var(--spacing-6);

  padding: var(--padding);
  background: #fff;
  box-shadow: var(--section-shadow);
  border-radius: var(--block-radius);

  @include respond-to($medium-up) {
    --padding: var(--spacing-12);
  }

}
*/
.media-container .video__poster {
  height: 100%;
}

/* ----- HRs ----- */
hr, .hr {
  width: 100%;
  margin: 1em 0;
  border-width: 0.5px;
  border-bottom: 0;
  border-style: solid;
  border-color: var(--border);
}
hr.hr--light, .hr.hr--light {
  border-color: var(--border-light);
}
hr.hr--dark, .hr.hr--dark {
  border-color: var(--border-dark);
}
hr.hr--clear, .hr.hr--clear {
  border-color: transparent;
}
hr.hr--small, .hr.hr--small {
  margin: 0.5em 0;
}

/* ----- UI Elements ----- */
/* ----- Accordions ----- */
/* ----- Note ----- */
.note {
  --note-color: var(--text);
  --note-background-color: var(--bg-accent);
  --note-border-color: var(--border);
  --note-font-size: var(--font-size-text);
  display: flex;
  padding: 0.6em 1em;
  font-size: var(--note-font-size);
  color: var(--note-color);
  background-color: var(--note-background-color);
  border-radius: var(--corner-radius);
  /* ----- layout ----- */
  /* ----- Styles ----- */
  /* ----- Sizes ----- */
}
.note p {
  color: var(--note-color);
}
.note p:last-child {
  margin: 0;
}
.note.note--inline {
  display: inline-flex;
}
.note.note--sm {
  --note-font-size: var(--font-size-text-sm);
}

/* ----- Quotes ----- */
.text-quotes::before {
  content: "“";
}
.text-quotes::after {
  content: "”";
}

/* ----- Swatches ----- */
.simple-swatch {
  --swatch-size: 16px;
  display: inline-flex;
  min-width: var(--swatch-size);
  min-height: var(--swatch-size);
  background: var(--swatch-color);
  border-radius: 100%;
}

/* ----- Links ----- */
.link {
  position: relative;
}
.link:after {
  --main-color: var(--link);
  --hover-color: var(--link-a70);
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 1px;
  width: 100%;
  background: linear-gradient(to right, var(--hover-color) 0% 50%, var(--main-color) 50% 100%);
  background-size: 200% 100%;
  background-position: 100% 0;
  transition: background-position 0.3s cubic-bezier(0.39, 0.575, 0.565, 1);
  pointer-events: none;
}

.link-animated {
  position: relative;
}
.link-animated:after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: currentColor;
  transition: transform 0.3s cubic-bezier(0.39, 0.575, 0.565, 1);
  transform: scaleX(0);
  transform-origin: left;
}
@media (hover: hover) {
  .link-animated:hover:after {
    transform: scaleX(1);
  }
}

.cart-bar {
  z-index: 6001 !important;
}

.span--comma:not(:last-of-type)::after {
  content: ", ";
}

/* 9. Apps  */
#chat-button {
  z-index: 6000 !important;
}

.htusb-ui-coll-boost {
  z-index: 1 !important;
}

/* 10. Utility Classes */
/* ==================== Variables ==================== */
/* ==================== Layout ==================== */
@media only screen and (max-width: 480px) {
  .hidden--small-down {
    display: none !important;
  }
  .visually-hidden--small-down {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--small-down {
    padding: 0 !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 749px) {
  .hidden--small {
    display: none !important;
  }
  .visually-hidden--small {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--small {
    padding: 0 !important;
  }
}
@media only screen and (min-width: 480px) {
  .hidden--small-up {
    display: none !important;
  }
  .visually-hidden--small-up {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--small-up {
    padding: 0 !important;
  }
}
@media only screen and (max-width: 750px) {
  .hidden--medium-down {
    display: none !important;
  }
  .visually-hidden--medium-down {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--medium-down {
    padding: 0 !important;
  }
}
@media only screen and (min-width: 750px) and (max-width: 989px) {
  .hidden--medium {
    display: none !important;
  }
  .visually-hidden--medium {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--medium {
    padding: 0 !important;
  }
}
@media only screen and (min-width: 750px) {
  .hidden--medium-up {
    display: none !important;
  }
  .visually-hidden--medium-up {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--medium-up {
    padding: 0 !important;
  }
}
@media only screen and (max-width: 990px) {
  .hidden--large-down {
    display: none !important;
  }
  .visually-hidden--large-down {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--large-down {
    padding: 0 !important;
  }
}
@media only screen and (min-width: 990px) and (max-width: 1399px) {
  .hidden--large {
    display: none !important;
  }
  .visually-hidden--large {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--large {
    padding: 0 !important;
  }
}
@media only screen and (min-width: 990px) {
  .hidden--large-up {
    display: none !important;
  }
  .visually-hidden--large-up {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--large-up {
    padding: 0 !important;
  }
}
@media only screen and (min-width: 1400px) and (max-width: 1399px) {
  .hidden--xlarge {
    display: none !important;
  }
  .visually-hidden--xlarge {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--xlarge {
    padding: 0 !important;
  }
}
@media only screen and (min-width: 1400px) {
  .hidden--xlarge-up {
    display: none !important;
  }
  .visually-hidden--xlarge-up {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--xlarge-up {
    padding: 0 !important;
  }
}
@media only screen and (max-width: 1600px) {
  .hidden--xxlarge-down {
    display: none !important;
  }
  .visually-hidden--xxlarge-down {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--xxlarge-down {
    padding: 0 !important;
  }
}
@media only screen and (min-width: 1600px) and (max-width: 1599px) {
  .hidden--xxlarge {
    display: none !important;
  }
  .visually-hidden--xxlarge {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--xxlarge {
    padding: 0 !important;
  }
}
@media only screen and (min-width: 1600px) {
  .hidden--xxlarge-up {
    display: none !important;
  }
  .visually-hidden--xxlarge-up {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    max-width: 1px;
    max-height: 1px;
    font-size: 0;
    margin: -1px;
    padding: 0;
    border: 0;
  }
  .no-padding--xxlarge-up {
    padding: 0 !important;
  }
}
.no-margin {
  margin: 0 !important;
}

.no-margin--top {
  margin-top: 0 !important;
}

.margin-top--10 {
  margin-top: 10px !important;
}

.margin-top--20 {
  margin-top: 20px !important;
}

.margin-top--30 {
  margin-top: 30px !important;
}

.margin-top--40 {
  margin-top: 40px !important;
}

.margin-top--50 {
  margin-top: 50px !important;
}

.no-margin--bottom {
  margin-bottom: 0 !important;
}

.margin-bottom--10 {
  margin-bottom: 10px !important;
}

.margin-bottom--20 {
  margin-bottom: 20px !important;
}

.margin-bottom--30 {
  margin-bottom: 30px !important;
}

.margin-bottom--40 {
  margin-bottom: 40px !important;
}

.margin-bottom--50 {
  margin-bottom: 50px !important;
}

.no-margin--left {
  margin-left: 0 !important;
}

.margin-left--10 {
  margin-left: 10px !important;
}

.margin-left--20 {
  margin-left: 20px !important;
}

.margin-left--30 {
  margin-left: 30px !important;
}

.margin-left--40 {
  margin-left: 40px !important;
}

.margin-left--50 {
  margin-left: 50px !important;
}

.no-margin--right {
  margin-right: 0 !important;
}

.margin-right--10 {
  margin-right: 10px !important;
}

.margin-right--20 {
  margin-right: 20px !important;
}

.margin-right--30 {
  margin-right: 30px !important;
}

.margin-right--40 {
  margin-right: 40px !important;
}

.margin-right--50 {
  margin-right: 50px !important;
}

.no-padding {
  padding: 0 !important;
}

.no-padding--top {
  padding-top: 0 !important;
}

.padding-top--10 {
  padding-top: 10px !important;
}

.padding-top--20 {
  padding-top: 20px !important;
}

.padding-top--30 {
  padding-top: 30px !important;
}

.padding-top--40 {
  padding-top: 40px !important;
}

.padding-top--50 {
  padding-top: 50px !important;
}

.no-padding--bottom {
  padding-bottom: 0 !important;
}

.padding-bottom--10 {
  padding-bottom: 10px !important;
}

.padding-bottom--20 {
  padding-bottom: 20px !important;
}

.padding-bottom--30 {
  padding-bottom: 30px !important;
}

.padding-bottom--40 {
  padding-bottom: 40px !important;
}

.padding-bottom--50 {
  padding-bottom: 50px !important;
}

.no-padding--left {
  padding-left: 0 !important;
}

.padding-left--10 {
  padding-left: 10px !important;
}

.padding-left--20 {
  padding-left: 20px !important;
}

.padding-left--30 {
  padding-left: 30px !important;
}

.padding-left--40 {
  padding-left: 40px !important;
}

.padding-left--50 {
  padding-left: 50px !important;
}

.no-padding--right {
  padding-right: 0 !important;
}

.padding-right--10 {
  padding-right: 10px !important;
}

.padding-right--20 {
  padding-right: 20px !important;
}

.padding-right--30 {
  padding-right: 30px !important;
}

.padding-right--40 {
  padding-right: 40px !important;
}

.padding-right--50 {
  padding-right: 50px !important;
}

/* --- Overflow --- */
.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

/* ==================== Typography ==================== */
/* --- Text Directions --- */
.text-align--left {
  text-align: left !important;
}

@media only screen and (max-width: 480px) {
  .text-align--left--mobile {
    text-align: left !important;
  }
}

.text-align--center {
  text-align: center !important;
}

@media only screen and (max-width: 480px) {
  .text-align--center--mobile {
    text-align: center !important;
  }
}

.text-align--right {
  text-align: right !important;
}

@media only screen and (max-width: 480px) {
  .text-align--right--mobile {
    text-align: right !important;
  }
}

/* --- Text Style --- */
.text--subdued {
  opacity: 0.7;
}

.strong,
.font-weight--bold {
  font-weight: var(--font-weight-body-bold) !important;
}

.font-weight--normal {
  font-weight: var(--font-weight-body) !important;
}

.text-transform--uppercase {
  text-transform: uppercase !important;
}

.text-transform--none {
  text-transform: none !important;
}

.italic {
  font-style: italic;
}

.strikethrough {
  text-decoration: line-through;
}

/* ==================== Colors ==================== */
/* --- Text --- */
.color--default {
  color: var(---color--default) !important;
}

.color--primary {
  color: var(---color--primary) !important;
}

.color--secondary {
  color: var(---color--secondary) !important;
}

.color--tertiary {
  color: var(---color--tertiary) !important;
}

.color--success {
  color: var(---color--success) !important;
}

.color--warning {
  color: var(---color--warning) !important;
}

.color--danger {
  color: var(---color--danger) !important;
}

.color--info {
  color: var(---color--info) !important;
}

.color--link {
  color: var(---color--link) !important;
}

.color--special {
  color: var(---color--special) !important;
}

/* --- Background --- */
.background-color--default {
  background: RGB(var(---color--default));
}

.background-color--primary {
  background: RGB(var(---color--primary));
}

.background-color--secondary {
  background: RGB(var(---color--secondary));
}

.background-color--tertiary {
  background: RGB(var(---color--tertiary));
}

.background-color--success {
  background: RGB(var(---color--success));
}

.background-color--warning {
  background: RGB(var(---color--warning));
}

.background-color--danger {
  background: RGB(var(---color--danger));
}

.background-color--info {
  background: RGB(var(---color--info));
}

.background-color--link {
  background: RGB(var(---color--link));
}

.background-color--special {
  background: RGB(var(---color--special));
}

/* --- Object Position --- */
.object-position--top {
  -o-object-position: top !important;
     object-position: top !important;
}

.object-position--bottom {
  -o-object-position: bottom !important;
     object-position: bottom !important;
}

.object-position--left {
  -o-object-position: left !important;
     object-position: left !important;
}

.object-position--right {
  -o-object-position: right !important;
     object-position: right !important;
}

.object-position--center {
  -o-object-position: center !important;
     object-position: center !important;
}

/* --- Flex - Justify --- */
.justify--start {
  justify-content: start !important;
}

.justify--end {
  justify-content: end !important;
}

.justify--flex-start {
  justify-content: flex-start !important;
}

.justify--flex-end {
  justify-content: flex-end !important;
}

.justify--self-start {
  justify-content: self-start !important;
}

.justify--self-end {
  justify-content: self-end !important;
}

.justify--stretch {
  justify-content: stretch !important;
}

.justify--space-between {
  justify-content: space-between !important;
}

.justify--space-around {
  justify-content: space-around !important;
}

.justify--anchor-center {
  justify-content: anchor-center !important;
}

.align--start {
  align-items: start !important;
}

.align--end {
  align-items: end !important;
}

.align--flex-start {
  align-items: flex-start !important;
}

.align--flex-end {
  align-items: flex-end !important;
}

.align--self-start {
  align-items: self-start !important;
}

.align--self-end {
  align-items: self-end !important;
}

.align--stretch {
  align-items: stretch !important;
}

.align--space-between {
  align-items: space-between !important;
}

.align--space-around {
  align-items: space-around !important;
}

.align--anchor-center {
  align-items: anchor-center !important;
}

@media only screen and (min-width: 750px) {
  .columns--1 {
    -moz-columns: 1;
         columns: 1;
    gap: var(--spacing-8);
  }
}

@media only screen and (min-width: 750px) {
  .columns--2 {
    -moz-columns: 2;
         columns: 2;
    gap: var(--spacing-8);
  }
}

@media only screen and (min-width: 750px) {
  .columns--3 {
    -moz-columns: 3;
         columns: 3;
    gap: var(--spacing-8);
  }
}

/*  ==============================
    Effects
    ============================== */
.corner-radius-sm {
  border-radius: var(--corner-radius-sm);
}

.corner-radius {
  border-radius: var(--corner-radius);
}

.corner-radius-lg {
  border-radius: var(--corner-radius-lg);
}

.block-radius-sm {
  border-radius: var(--block-radius-sm);
}

.block-radius {
  border-radius: var(--block-radius);
}

.block-radius-lg {
  border-radius: var(--block-radius-lg);
}

.section-radius-sm {
  border-radius: var(--section-radius-sm);
}

.section-radius {
  border-radius: var(--section-radius);
}

.section-radius-lg {
  border-radius: var(--section-radius-lg);
}

/* ========== Backgrounds ========== */
.background--accent {
  background: var(--accent);
}

.background--accent-fade {
  background: var(--accent-fade);
}

.background--accent-hover {
  background: var(--accent-hover);
}

.background--icons {
  background: var(--icons);
}

.background--bg {
  background: var(--bg);
}

.background--bg-accent {
  background: var(--bg-accent);
}

.background--bg-accent-lighten {
  background: var(--bg-accent-lighten);
}

.background--bg-accent-darken {
  background: var(--bg-accent-darken);
}

/* ========== Borders ========== */
.border-color {
  background: var(--border);
}

.border-color--dark {
  background: var(--border-dark);
}

.border-color--light {
  background: var(--border-light);
}

.border-color--hairline {
  background: var(--border-hairline);
}

/* ========== Colors ========== */
.color--icons {
  color: var(--icons, currentColor);
}

.color--link {
  color: var(--link, currentColor);
}

.color--link-a50 {
  color: var(--link-a50, currentColor);
}

.color--link-a70 {
  color: var(--link-a70, currentColor);
}

.color--link-hover {
  color: var(--link-hover, currentColor);
}

.color--link-opposite {
  color: var(--link-opposite, currentColor);
}

.color--text {
  color: var(--text, currentColor);
}

.color--text-dark {
  color: var(--text-dark, currentColor);
}

.color--text-light {
  color: var(--text-light, currentColor);
}

.color--text-hover {
  color: var(--text-hover, currentColor);
}

.color--text-a5 {
  color: var(--text-a5, currentColor);
}

.color--text-a35 {
  color: var(--text-a35, currentColor);
}

.color--text-a50 {
  color: var(--text-a50, currentColor);
}

.color--text-a80 {
  color: var(--text-a80, currentColor);
}

/* 11. Third-Party Styles */
/* 12. Animations */