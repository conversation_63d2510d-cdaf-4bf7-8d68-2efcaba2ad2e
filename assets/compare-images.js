!function(){"use strict";const e="[data-image-holder]",t="[data-image-element]",s="[data-range-button]",i="[data-range-input]";customElements.get("compare-images")||customElements.define("compare-images",class extends HTMLElement{constructor(){super(),this.imageHolder=this.querySelector(e),this.imageElement=this.querySelector(t),this.rangeButton=this.querySelector(s),this.rangeInput=this.querySelector(i),this.setOverlapImageSize=this.setOverlapImageSize.bind(this)}connectedCallback(){this.setOverlapImageSize(),this.setImagePosition(),this.rangeInput.addEventListener("input",(()=>this.setImagePosition())),document.addEventListener("theme:resize",this.setOverlapImageSize)}disconnectedCallback(){document.removeEventListener("theme:resize",this.setOverlapImageSize)}setImagePosition(){const e=this.rangeInput.value,t=this.imageElement.offsetWidth,s=this.rangeButton.offsetWidth;this.rangeButton.style.left=`${e}%`,this.imageHolder.style.width=(t-s)*(100-e)/100+s/2+"px"}setOverlapImageSize(){const e=this.offsetWidth;this.imageElement.style.width=`${e}px`,this.setImagePosition()}})}();
