!function(){"use strict";const t="[data-slider-logos]",s="[data-slider-text]",e="[data-slide]",i="is-selected",l="is-initialized",o="flickity-enabled",a="data-slide",h="data-slide-index";customElements.get("logos-component")||customElements.define("logos-component",class extends HTMLElement{constructor(){super(),this.slideshowNav=this.querySelector(t),this.slideshowText=this.querySelector(s),this.setSlideshowNavStateOnResize=()=>this.setSlideshowNavState(),this.flkty=null,this.flktyNav=null,this.logoSlides=this.slideshowNav.querySelectorAll(e),this.logoSlidesWidth=this.getSlidesWidth(),this.bindEvents()}connectedCallback(){this.initSlideshowText(),this.initSlideshowNav()}getSlidesWidth(){return 200*this.logoSlides.length}initSlideshowText(){this.slideshowText&&(this.flkty=new window.theme.FlickityFade(this.slideshowText,{fade:!0,autoPlay:!1,prevNextButtons:!1,cellAlign:"left",contain:!0,pageDots:!1,wrapAround:!1,selectedAttraction:.2,friction:.6,draggable:!1,accessibility:!1,on:{ready:()=>this.sliderAccessibility(),change:()=>this.sliderAccessibility()}}))}sliderAccessibility(){const t=this.slideshowText.querySelectorAll(`${e} a, ${e} button`);t.length&&t.forEach((t=>{const s=t.closest(e);if(s){const e=s.classList.contains(i)?0:-1;t.setAttribute("tabindex",e)}}))}initSlideshowNav(){this.slideshowNav&&(this.logoSlides.length&&this.logoSlides.forEach((t=>{t.addEventListener("click",(()=>{const s=parseInt(t.getAttribute(h)),e=this.slideshowNav.classList.contains(o);if(this.flkty&&this.flkty.select(s),e)this.flktyNav.select(s),this.slideshowNav.classList.contains(i)||this.flktyNav.playPlayer();else{const s=this.slideshowNav.querySelector(`.${i}`);s&&s.classList.remove(i),t.classList.add(i)}}))})),this.setSlideshowNavState(),document.addEventListener("theme:resize",this.setSlideshowNavStateOnResize))}setSlideshowNavState(){const t=this.slideshowNav.classList.contains(o);if(this.logoSlidesWidth>window.theme.getWindowWidth()){if(!t){this.slideshowNav.classList.add(l);const t=this.slideshowNav.querySelector(`.${i}`);t&&t.classList.remove(i),this.logoSlides[0].classList.add(i),this.flktyNav||(this.flktyNav=new window.theme.Flickity(this.slideshowNav,{autoPlay:4e3,prevNextButtons:!1,contain:!1,pageDots:!1,wrapAround:!0,watchCSS:!0,selectedAttraction:.05,friction:.8,initialIndex:0}),this.flktyNav.on("deactivate",(()=>{this.slideshowNav.querySelector(e).classList.add(i),this.flkty&&this.flkty.select(0)})),this.flkty&&(this.flkty.select(0),this.flktyNav.on("change",(t=>this.flkty.select(t)))))}}else t&&this.slideshowNav.classList.remove(l)}onBlockSelect(t){if(!this.slideshowNav)return;const s=this.slideshowNav.querySelector(`[${a}="${t.detail.blockId}"]`),e=parseInt(s.getAttribute(h));this.slideshowNav.classList.contains(o)?(this.flktyNav.select(e),this.flktyNav.stopPlayer(),this.slideshowNav.classList.add(i)):s.dispatchEvent(new Event("click"))}onBlockDeselect(){this.slideshowNav&&this.slideshowNav.classList.contains(o)&&(this.flktyNav.playPlayer(),this.slideshowNav.classList.remove(i))}bindEvents(){this.addEventListener("theme:slider-logos:select",(t=>this.onBlockSelect(t.detail.evt))),this.addEventListener("theme:slider-logos:deselect",(()=>this.onBlockDeselect()))}disconnectedCallback(){document.removeEventListener("theme:resize",this.setSlideshowNavStateOnResize)}})}();
