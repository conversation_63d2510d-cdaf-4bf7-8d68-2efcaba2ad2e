!function(){"use strict";const t="[data-collection-sidebar]",e="[data-range-left]",i="[data-range-right]",s="[data-range-line]",h="[data-range-holder]",n="data-se-min",o="data-se-max",a="data-se-min-value",r="data-se-max-value",c="data-se-step",u="data-range-filter-update",f="[data-field-price-min]",l="[data-field-price-max]",d="is-initialized";customElements.get("range-slider")||customElements.define("range-slider",class extends HTMLElement{constructor(){super(),this.sidebar=this.closest(t),this.sidebarTransitionEvent=t=>this.onSidebarTransitionEnd(t),this.resizeEvent=()=>{this.connectedCallback(),this.sidebar.addEventListener("transitionend",this.sidebarTransitionEvent)},this.onMoveEvent=t=>this.onMove(t),this.onStopEvent=t=>this.onStop(t),this.onStartEvent=t=>this.onStart(t),this.startX=0,this.x=0,this.touchLeft=this.querySelector(e),this.touchRight=this.querySelector(i),this.lineSpan=this.querySelector(s),this.min=parseFloat(this.getAttribute(n)),this.max=parseFloat(this.getAttribute(o)),this.step=0,this.normalizeFact=26,document.addEventListener("theme:resize:width",this.resizeEvent)}connectedCallback(){let t=this.min;this.hasAttribute(a)&&(t=parseFloat(this.getAttribute(a)));let e=this.max;this.hasAttribute(r)&&(e=parseFloat(this.getAttribute(r))),t<this.min&&(t=this.min),e>this.max&&(e=this.max),t>e&&(t=e),this.getAttribute(c)&&(this.step=Math.abs(parseFloat(this.getAttribute(c)))),this.reset(),this.maxX=this.offsetWidth-this.touchRight.offsetWidth,this.selectedTouch=null,this.initialValue=this.lineSpan.offsetWidth-this.normalizeFact,this.setMinValue(t),this.setMaxValue(e),this.touchLeft.addEventListener("mousedown",this.onStartEvent),this.touchRight.addEventListener("mousedown",this.onStartEvent),this.touchLeft.addEventListener("touchstart",this.onStartEvent,{passive:!0}),this.touchRight.addEventListener("touchstart",this.onStartEvent,{passive:!0}),this.classList.add(d)}reset(){this.touchLeft.style.left="0px",this.touchRight.style.left=this.offsetWidth-this.touchLeft.offsetWidth+"px",this.lineSpan.style.marginLeft="0px",this.lineSpan.style.width=this.offsetWidth-this.touchLeft.offsetWidth+"px",this.startX=0,this.x=0}setMinValue(t){const e=(t-this.min)/(this.max-this.min);this.touchLeft.style.left=Math.ceil(e*(this.offsetWidth-(this.touchLeft.offsetWidth+this.normalizeFact)))+"px",this.lineSpan.style.marginLeft=this.touchLeft.offsetLeft+"px",this.lineSpan.style.width=this.touchRight.offsetLeft-this.touchLeft.offsetLeft+"px",this.setAttribute(a,t)}setMaxValue(t){const e=(t-this.min)/(this.max-this.min);this.touchRight.style.left=Math.ceil(e*(this.offsetWidth-(this.touchLeft.offsetWidth+this.normalizeFact))+this.normalizeFact)+"px",this.lineSpan.style.marginLeft=this.touchLeft.offsetLeft+"px",this.lineSpan.style.width=this.touchRight.offsetLeft-this.touchLeft.offsetLeft+"px",this.setAttribute(r,t)}onStart(t){let e=t;t.touches&&(e=t.touches[0]),t.currentTarget===this.touchLeft?this.x=this.touchLeft.offsetLeft:t.currentTarget===this.touchRight&&(this.x=this.touchRight.offsetLeft),this.startX=e.pageX-this.x,this.selectedTouch=t.currentTarget,document.addEventListener("mousemove",this.onMoveEvent),document.addEventListener("mouseup",this.onStopEvent),document.addEventListener("touchmove",this.onMoveEvent,{passive:!0}),document.addEventListener("touchend",this.onStopEvent,{passive:!0})}onMove(t){let e=t;if(t.touches&&(e=t.touches[0]),this.x=e.pageX-this.startX,this.selectedTouch===this.touchLeft?(this.x>this.touchRight.offsetLeft-this.selectedTouch.offsetWidth+10?this.x=this.touchRight.offsetLeft-this.selectedTouch.offsetWidth+10:this.x<0&&(this.x=0),this.selectedTouch.style.left=this.x+"px"):this.selectedTouch===this.touchRight&&(this.x<this.touchLeft.offsetLeft+this.touchLeft.offsetWidth-10?this.x=this.touchLeft.offsetLeft+this.touchLeft.offsetWidth-10:this.x>this.maxX&&(this.x=this.maxX),this.selectedTouch.style.left=this.x+"px"),this.lineSpan.style.marginLeft=this.touchLeft.offsetLeft+"px",this.lineSpan.style.width=this.touchRight.offsetLeft-this.touchLeft.offsetLeft+"px",this.calculateValue(),this.getAttribute("on-change")){new Function("min, max",this.getAttribute("on-change"))(this.getAttribute(a),this.getAttribute(r))}this.onChange(this.getAttribute(a),this.getAttribute(r))}onStop(){document.removeEventListener("mousemove",this.onMoveEvent),document.removeEventListener("mouseup",this.onStopEvent),document.removeEventListener("touchmove",this.onMoveEvent),document.removeEventListener("touchend",this.onStopEvent),this.selectedTouch=null,this.calculateValue(),this.onChanged(this.getAttribute(a),this.getAttribute(r))}onChange(t,e){const i=this.closest(h);if(i){const s=i.querySelector(f),h=i.querySelector(l);s&&h&&(s.value=t,h.value=e)}}onChanged(t,e){this.hasAttribute(u)&&this.dispatchEvent(new CustomEvent("theme:range:update",{bubbles:!0}))}calculateValue(){const t=(this.lineSpan.offsetWidth-this.normalizeFact)/this.initialValue;let e=this.lineSpan.offsetLeft/this.initialValue,i=e+t;if(e=e*(this.max-this.min)+this.min,i=i*(this.max-this.min)+this.min,0!==this.step){let t=Math.floor(e/this.step);e=this.step*t,t=Math.floor(i/this.step),i=this.step*t}this.selectedTouch===this.touchLeft&&this.setAttribute(a,e),this.selectedTouch===this.touchRight&&this.setAttribute(r,i)}onSidebarTransitionEnd(t){t.target==this.sidebar&&"min-width"==t.propertyName&&(this.sidebar.removeEventListener("transitionend",this.sidebarTransitionEvent),this.connectedCallback())}disconnectedCallback(){this.sidebar.removeEventListener("transitionend",this.sidebarTransitionEvent),this.resizeEvent&&document.removeEventListener("theme:resize:width",this.resizeEvent)}})}();
