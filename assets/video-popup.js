!function(){"use strict";const e="data-video-play";customElements.get("video-popup")||customElements.define("video-popup",class extends HTMLElement{constructor(){super()}connectedCallback(){this.querySelectorAll(`[${e}]`)?.forEach((t=>{t.addEventListener("click",(t=>{const n=t.currentTarget;if(""!==n.getAttribute(e).trim()){t.preventDefault();const o=[{html:n.getAttribute(e)}],s={mainClass:"pswp--video"};new window.theme.LoadPhotoswipe(o,s),window.a11y.lastElement=n}}))}))}})}();
