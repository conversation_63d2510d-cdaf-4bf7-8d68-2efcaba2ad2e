!function(){"use strict";const e="[data-collection-sidebar]",t="[data-collection-sidebar-slide-out]",i="[data-collection-sidebar-close]",s="[data-aria-toggle]",o="drawer--animated",l="expanded",n="no-mobile-animation",c="is-focused";customElements.get("collection-component")||customElements.define("collection-component",class extends HTMLElement{constructor(){super(),this.collectionSidebar=this.querySelector(e),this.groupTagsButton=this.querySelector(s),this.a11y=window.theme.a11y,this.groupTagsButtonClickEvent=e=>this.groupTagsButtonClick(e),this.sidebarResizeEvent=()=>this.toggleSidebarSlider(),this.collectionSidebarCloseEvent=e=>this.collectionSidebarClose(e)}connectedCallback(){if(null!==this.groupTagsButton){document.addEventListener("theme:resize:width",this.sidebarResizeEvent),this.groupTagsButton.addEventListener("click",this.groupTagsButtonClickEvent),this.collectionSidebar&&setTimeout((()=>{this.collectionSidebar.classList.remove(n)}),1e3);new MutationObserver((e=>{for(const t of e)if("attributes"===t.type){"true"==t.target.getAttribute("aria-expanded")&&this.showSidebarCallback()}})).observe(this.groupTagsButton,{attributes:!0,childList:!1,subtree:!1})}this.addEventListener("keyup",(e=>{"Escape"===e.code&&this.hideSidebar()})),this.collectionSidebar&&(this.collectionSidebar.addEventListener("transitionend",(()=>{this.collectionSidebar.classList.contains(l)||this.collectionSidebar.classList.remove(o)})),this.toggleSidebarSlider(),this.addEventListener("theme:filter:close",this.collectionSidebarCloseEvent))}showSidebarCallback(){const e=this.querySelector(t),s=document.documentElement.hasAttribute("data-scroll-locked"),l=window.theme.isMobile();this.collectionSidebar.classList.add(o),null===e&&!l&&s&&(this.a11y.removeTrapFocus(),document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0}))),(l||null!==e)&&(e&&this.a11y.trapFocus(this.collectionSidebar,{elementToFocus:this.collectionSidebar.querySelector(i)}),document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})))}hideSidebar(){const e=this.querySelector(t),i=document.documentElement.hasAttribute("data-scroll-locked");this.groupTagsButton.setAttribute("aria-expanded","false"),this.collectionSidebar.classList.remove(l),e&&this.a11y.removeTrapFocus(),i&&document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0}))}toggleSidebarSlider(){window.theme.isMobile()?this.hideSidebar():this.collectionSidebar.classList.contains(l)&&this.showSidebarCallback()}collectionSidebarClose(e){e.preventDefault(),this.hideSidebar(),document.body.classList.contains(c)&&this.groupTagsButton&&this.groupTagsButton.focus()}groupTagsButtonClick(){document.documentElement.hasAttribute("data-scroll-locked")&&document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0}))}disconnectedCallback(){null!==this.groupTagsButton&&(document.removeEventListener("theme:resize:width",this.sidebarResizeEvent),this.groupTagsButton.removeEventListener("click",this.groupTagsButtonClickEvent)),this.collectionSidebar&&this.removeEventListener("theme:filter:close",this.collectionSidebarCloseEvent)}})}();
