!function(){"use strict";const t="popup-component",i="[data-popup-close]",o="[data-notification-form]",e="[data-product-notification-heading]",s="has-success",n="hidden";customElements.get("product-notification")||customElements.define("product-notification",class extends HTMLElement{constructor(){super(),this.notificationForm=this.querySelector(o),this.preventSubmit=!0,this.popup=this.closest("dialog"),this.popupClose=this.popup.querySelector(i)}connectedCallback(){this.checkState(),this.notificationForm.addEventListener("submit",(t=>this.notificationSubmitEvent(t))),this.popupClose.addEventListener("click",(()=>{this.removeStorage()}))}checkState(){if(-1!==window.location.search.indexOf("?contact_posted=true")){this.querySelector(e).classList.add(n),this.closest(t).classList.add(s),this.popup.removeAttribute("inert"),"function"==typeof this.popup.showModal?this.popup.showModal():this.popup.setAttribute("open","")}}notificationSubmitEvent(t){this.preventSubmit&&(t.preventDefault(),this.removeStorage(),this.writeStorage(),this.preventSubmit=!1,this.notificationForm.submit())}writeStorage(){void 0!==window.sessionStorage&&window.sessionStorage.setItem("notification_form_id",this.notificationForm.id)}removeStorage(){window.sessionStorage.removeItem("notification_form_id")}})}();
