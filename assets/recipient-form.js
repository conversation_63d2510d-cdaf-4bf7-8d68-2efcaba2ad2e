!function(){"use strict";const e='input:not([type="checkbox"]):not([type="hidden"]), textarea',t='input[type="email"]',i='input[type="checkbox"]',n="[data-form-wrapper]";customElements.get("recipient-form")||customElements.define("recipient-form",class extends HTMLElement{constructor(){super(),this.fieldCheckbox=this.querySelector(i),this.fieldEmail=this.querySelector(t),this.fields=this.querySelectorAll(e),this.form=this.closest(n),this.onChangeEvent=e=>this.onChange(e)}connectedCallback(){this.fieldCheckbox&&(this.fieldCheckbox.addEventListener("change",this.onChangeEvent),this.form&&this.form.addEventListener("theme:product:add",(()=>{this.fieldCheckbox.checked=!1,this.fieldCheckbox.dispatchEvent(new Event("change"))})))}clearInputValues(){this.fields.length&&this.fields.forEach((e=>{e.value=""}))}onChange(e){this.fieldEmail.required=Boolean(e.target.checked),e.target.checked||this.clearInputValues()}disconnectedCallback(){this.fieldCheckbox.removeEventListener("change",this.onChangeEvent)}})}();
