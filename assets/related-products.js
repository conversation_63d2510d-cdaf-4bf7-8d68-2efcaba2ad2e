!function(){"use strict";const e="[data-api-related-template]",t=".product-item",i="[data-grid-item]",s="[data-related-section]",r="recently-viewed",d="grid-slider",n="[data-recently-viewed-wrapper]",l="data-limit",c="data-product-id",a="hidden",o="grid--mobile-slider";customElements.get("related-products")||customElements.define("related-products",class extends HTMLElement{constructor(){super(),this.sectionId=this.id,this.relatedItems=0,this.wrapper=this.querySelector(n),this.recentlyViewed=this.querySelector(r)}connectedCallback(){this.loadRelatedProducts(),this.loadRecentlyViewedProducts()}loadRelatedProducts(){const e=this.querySelector(s);if(!e)return;const t=e.getAttribute(c),i=e.getAttribute(l),r=`${window.theme.routes.product_recommendations_url}?section_id=api-product-recommendation&limit=${i}&product_id=${t}&intent=related`;fetch(r).then((e=>e.text())).then((t=>this.handleRelatedProductsResponse(t,e))).catch((()=>this.hideSection(e)))}handleRelatedProductsResponse(t,s){const r=document.createElement("div");r.innerHTML=(new DOMParser).parseFromString(t,"text/html").querySelector(e).innerHTML;const d=r.querySelectorAll(i).length;if(d>0){s.innerHTML=r.innerHTML,this.relatedItems=d;if(0===parseInt(s.style.getPropertyValue("--COLUMNS-MOBILE"))){s.querySelector(i).parentElement.classList.add(o)}}else this.hideSection(s);this.updateVisibility()}loadRecentlyViewedProducts(){this.recentlyViewed.addEventListener("theme:recently-viewed:loaded",(()=>{this.handleRecentlyViewedResponse()}))}handleRecentlyViewedResponse(){const e=parseInt(this.recentlyViewed.dataset.minimum)||1,i=this.recentlyViewed.querySelectorAll(t),s=this.recentlyViewed.querySelector(d),r=!this.wrapper&&i.length>0,n=this.wrapper&&i.length>=e;(r||n)&&(n&&this.wrapper.classList.remove(a),this.recentlyViewed.classList.remove(a),this.recentlyViewed.dispatchEvent(new CustomEvent("theme:tab:check",{bubbles:!0})),s&&s.dispatchEvent(new CustomEvent("theme:grid-slider:init",{bubbles:!0}))),this.updateVisibility()}hideSection(e){e.dispatchEvent(new CustomEvent("theme:tab:hide",{bubbles:!0}))}updateVisibility(){const e=Shopify.Products.getConfig().howManyToShow<1&&this.relatedItems<1;this.classList.toggle(a,e)}})}();
