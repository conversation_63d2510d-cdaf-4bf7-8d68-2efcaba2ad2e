!function(){"use strict";class t extends window.theme.DeferredMedia{constructor(){super()}loadContent(){super.loadContent(),Shopify.loadFeatures([{name:"model-viewer-ui",version:"1.0",onLoad:this.setupModelViewerUI.bind(this)}])}setupModelViewerUI(t){t||(this.modelViewerUI=new Shopify.ModelViewerUI(this.querySelector("model-viewer")))}}window.ProductModel={loadShopifyXR(){Shopify.loadFeatures([{name:"shopify-xr",version:"1.0",onLoad:this.setupShopifyXR.bind(this)}])},setupShopifyXR(t){t||(window.ShopifyXR?(document.querySelectorAll('[id^="ModelJSON-"]').forEach((t=>{window.ShopifyXR.addModels(JSON.parse(t.textContent)),t.remove()})),window.ShopifyXR.setupXRElements()):document.addEventListener("shopify_xr_initialized",(()=>this.setupShopifyXR())))}},window.addEventListener("DOMContentLoaded",(()=>{window.ProductModel&&window.ProductModel.loadShopifyXR()}));const e="[data-add-to-cart]",r="[data-product-json]",o="[data-product-form]",i="#cart-bar",s="[data-popup-open]",a=".product__submit__add",n="[data-form-wrapper]",d="[data-product-variants]",c="is-loading",l="is-visible",h="data-cart-bar-enabled",u="data-add-to-cart-bar",p="data-cart-bar-scroll",m="data-cart-bar-product-notification",f="data-sticky-enabled";customElements.get("product-component")||customElements.define("product-component",class extends HTMLElement{constructor(){super(),this.stickyEnabled="true"===this.getAttribute(f),this.formWrapper=this.querySelector(n),this.cartBarEnabled=this.hasAttribute(h),this.cartBar=this.querySelector(i),this.setCartBarHeight=this.setCartBarHeight.bind(this),this.scrollToTop=this.scrollToTop.bind(this),this.toggleCartBarOnScroll=this.toggleCartBarOnScroll.bind(this),this.unlockTimer=0}connectedCallback(){const t=this.querySelector(r);if(t&&!t.innerHTML||!t)return;const e=JSON.parse(t.innerHTML).handle;let i={};e&&(i={handle:e}),Shopify.Products.recordRecentlyViewed(i),Shopify.Products&&Shopify.Products.recordRecentlyViewed&&Shopify.Products.recordRecentlyViewed(i),this.form=this.querySelector(o),this.cartBarEnabled&&(this.initCartBar(),this.setCartBarHeight(),document.addEventListener("theme:scroll",this.toggleCartBarOnScroll),document.addEventListener("theme:resize",this.setCartBarHeight))}initCartBar(){this.cartBarBtns=this.cartBar.querySelectorAll(a),this.cartBarBtns.length>0&&this.cartBarBtns.forEach((t=>{t.addEventListener("click",(t=>{t.preventDefault(),t.currentTarget.hasAttribute(u)?(this.cartBarEnabled&&(t.currentTarget.classList.add(c),t.currentTarget.setAttribute("disabled","disabled")),this.form.querySelector(e).dispatchEvent(new Event("click",{bubbles:!0}))):t.currentTarget.hasAttribute(p)?this.scrollToTop():t.currentTarget.hasAttribute(m)&&this.form.querySelector(s)?.dispatchEvent(new Event("click"))})),t.hasAttribute(u)&&document.addEventListener("theme:product:add-error",this.scrollToTop)})),this.setCartBarHeight()}scrollToTop(){const t=this.querySelector(d),e=(window.theme.isMobile()?t||this.form:this).getBoundingClientRect().top;window.theme.scrollTo(window.theme.isMobile()?e-10:e)}toggleCartBarOnScroll(){const t=window.scrollY,e=theme.variables.productPageSticky&&this.formWrapper?this.formWrapper:this.form;if(e&&this.cartBar){const r=t>e.offsetTop+e.offsetHeight;this.cartBar.classList.toggle(l,r)}}setCartBarHeight(){const t=this.cartBar.offsetHeight;document.documentElement.style.setProperty("--cart-bar-height",`${t}px`)}disconnectedCallback(){document.removeEventListener("theme:product:add-error",this.scrollToTop),this.cartBarEnabled&&(document.removeEventListener("theme:scroll",this.toggleCartBarOnScroll),document.removeEventListener("theme:resize",this.setCartBarHeight))}}),customElements.get("product-model")||customElements.define("product-model",t)}();
