!function(){"use strict";const t="[data-scroll-spy]",e="is-selected",i="data-scroll-spy-container",s="data-scroll-spy",o="data-scroll-spy-mobile",r="data-scroll-spy-desktop",l="data-scroll-trigger-point";customElements.get("scroll-spy")||customElements.define("scroll-spy",class extends HTMLElement{constructor(){super(),this.container=this?.closest(this?.getAttribute(i))||document,this.scrollSpyButton=this.querySelector(t),this.elementToSpy=this.container.querySelector(this.scrollSpyButton.getAttribute(s)),this.anchorSelector=`[${s}="#${this.elementToSpy.id}"]`,this.anchor=this.container.querySelector(this.anchorSelector),this.anchorSiblings=this.container.querySelectorAll(`[${s}]`),this.initialized=!1,this.anchor&&(this.triggerPoint=this.anchor.getAttribute(l),this.scrollCallback=()=>this.onScroll(),this.toggleScrollObserver=this.toggleScrollObserver.bind(this))}connectedCallback(){this.toggleScrollObserver(),document.addEventListener("theme:resize:width",this.toggleScrollObserver)}toggleScrollObserver(){this.isEligible()?this.initialized||(document.addEventListener("theme:scroll",this.scrollCallback),this.initialized=!0):(document.removeEventListener("theme:scroll",this.scrollCallback),this.initialized=!1)}isEligible(){const t=!window.theme.isMobile();return!t&&this.anchor.hasAttribute(o)||t&&this.anchor.hasAttribute(r)||!this.anchor.hasAttribute(r)&&!this.anchor.hasAttribute(o)}onScroll(){this.top=this.elementToSpy.getBoundingClientRect().top;const t=Math.round(window.innerHeight),i=Math.round(window.scrollY),s=i+t,o=Math.round(this.top+i),r=this.elementToSpy.offsetHeight;o<s&&!(o+r<i)&&this.triggerPointReached()&&(this.anchorSiblings.forEach((t=>{t.matches(this.anchorSelector)||t.classList.remove(e)})),this.anchor.classList.add(e))}triggerPointReached(){let t=!1;switch(this.triggerPoint){case"top":default:t=this.top<=0;break;case"middle":t=this.top<=window.innerHeight/2;break;case"bottom":t=this.top<=window.innerHeight}return t}disconnectedCallback(){document.removeEventListener("theme:resize:width",this.toggleScrollObserver),document.removeEventListener("theme:scroll",this.scrollCallback)}})}();
