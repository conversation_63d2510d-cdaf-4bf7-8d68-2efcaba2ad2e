/** @license MIT smoothscroll-polyfill 0.4.4 (c) 2020 <PERSON><PERSON> */
!function(o){var t={};function e(l){if(t[l])return t[l].exports;var r=t[l]={i:l,l:!1,exports:{}};return o[l].call(r.exports,r,r.exports,e),r.l=!0,r.exports}e.m=o,e.c=t,e.d=function(o,t,l){e.o(o,t)||Object.defineProperty(o,t,{enumerable:!0,get:l})},e.r=function(o){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(o,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(o,"__esModule",{value:!0})},e.t=function(o,t){if(1&t&&(o=e(o)),8&t)return o;if(4&t&&"object"==typeof o&&o&&o.__esModule)return o;var l=Object.create(null);if(e.r(l),Object.defineProperty(l,"default",{enumerable:!0,value:o}),2&t&&"string"!=typeof o)for(var r in o)e.d(l,r,function(t){return o[t]}.bind(null,r));return l},e.n=function(o){var t=o&&o.__esModule?function(){return o.default}:function(){return o};return e.d(t,"a",t),t},e.o=function(o,t){return Object.prototype.hasOwnProperty.call(o,t)},e.p="",e(e.s=69)}({65:function(o,t,e){!function(){"use strict";o.exports={polyfill:function(){var o=window,t=document;if(!("scrollBehavior"in t.documentElement.style)||!0===o.__forceSmoothScrollPolyfill__){var e,l=o.HTMLElement||o.Element,r={scroll:o.scroll||o.scrollTo,scrollBy:o.scrollBy,elementScroll:l.prototype.scroll||c,scrollIntoView:l.prototype.scrollIntoView},i=o.performance&&o.performance.now?o.performance.now.bind(o.performance):Date.now,n=(e=o.navigator.userAgent,new RegExp(["MSIE ","Trident/","Edge/"].join("|")).test(e)?1:0);o.scroll=o.scrollTo=function(){void 0!==arguments[0]&&(!0!==s(arguments[0])?v.call(o,t.body,void 0!==arguments[0].left?~~arguments[0].left:o.scrollX||o.pageXOffset,void 0!==arguments[0].top?~~arguments[0].top:o.scrollY||o.pageYOffset):r.scroll.call(o,void 0!==arguments[0].left?arguments[0].left:"object"!=typeof arguments[0]?arguments[0]:o.scrollX||o.pageXOffset,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:o.scrollY||o.pageYOffset))},o.scrollBy=function(){void 0!==arguments[0]&&(s(arguments[0])?r.scrollBy.call(o,void 0!==arguments[0].left?arguments[0].left:"object"!=typeof arguments[0]?arguments[0]:0,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:0):v.call(o,t.body,~~arguments[0].left+(o.scrollX||o.pageXOffset),~~arguments[0].top+(o.scrollY||o.pageYOffset)))},l.prototype.scroll=l.prototype.scrollTo=function(){if(void 0!==arguments[0])if(!0!==s(arguments[0])){var o=arguments[0].left,t=arguments[0].top;v.call(this,this,void 0===o?this.scrollLeft:~~o,void 0===t?this.scrollTop:~~t)}else{if("number"==typeof arguments[0]&&void 0===arguments[1])throw new SyntaxError("Value could not be converted");r.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left:"object"!=typeof arguments[0]?~~arguments[0]:this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top:void 0!==arguments[1]?~~arguments[1]:this.scrollTop)}},l.prototype.scrollBy=function(){void 0!==arguments[0]&&(!0!==s(arguments[0])?this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior}):r.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop))},l.prototype.scrollIntoView=function(){if(!0!==s(arguments[0])){var e=u(this),l=e.getBoundingClientRect(),i=this.getBoundingClientRect();e!==t.body?(v.call(this,e,e.scrollLeft+i.left-l.left,e.scrollTop+i.top-l.top),"fixed"!==o.getComputedStyle(e).position&&o.scrollBy({left:l.left,top:l.top,behavior:"smooth"})):o.scrollBy({left:i.left,top:i.top,behavior:"smooth"})}else r.scrollIntoView.call(this,void 0===arguments[0]||arguments[0])}}function c(o,t){this.scrollLeft=o,this.scrollTop=t}function s(o){if(null===o||"object"!=typeof o||void 0===o.behavior||"auto"===o.behavior||"instant"===o.behavior)return!0;if("object"==typeof o&&"smooth"===o.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+o.behavior+" is not a valid value for enumeration ScrollBehavior.")}function f(o,t){return"Y"===t?o.clientHeight+n<o.scrollHeight:"X"===t?o.clientWidth+n<o.scrollWidth:void 0}function a(t,e){var l=o.getComputedStyle(t,null)["overflow"+e];return"auto"===l||"scroll"===l}function p(o){var t=f(o,"Y")&&a(o,"Y"),e=f(o,"X")&&a(o,"X");return t||e}function u(o){for(;o!==t.body&&!1===p(o);)o=o.parentNode||o.host;return o}function d(t){var e,l,r,n,c=(i()-t.startTime)/468;n=c=c>1?1:c,e=.5*(1-Math.cos(Math.PI*n)),l=t.startX+(t.x-t.startX)*e,r=t.startY+(t.y-t.startY)*e,t.method.call(t.scrollable,l,r),l===t.x&&r===t.y||o.requestAnimationFrame(d.bind(o,t))}function v(e,l,n){var s,f,a,p,u=i();e===t.body?(s=o,f=o.scrollX||o.pageXOffset,a=o.scrollY||o.pageYOffset,p=r.scroll):(s=e,f=e.scrollLeft,a=e.scrollTop,p=c),d({scrollable:s,method:p,startTime:u,startX:f,startY:a,x:l,y:n})}}}}()},69:function(o,t,e){o.exports=e(70)},70:function(o,t,e){"use strict";e.r(t);var l=e(65);e.n(l).a.polyfill()}});

/** @license MIT smoothscroll-anchor-polyfill 1.3.2 (c) 2020 Jonas Kuske */
var e,t;e=function(){return(new function(){var e=this,t="undefined"!=typeof window;if(t)var n=window,o=document,r=o.documentElement,i=o.createElement("a");if(this.polyfill=function(r){if(r=r||{},t){var l=n.__forceSmoothscrollAnchorPolyfill__,a="boolean"==typeof r.force?r.force:l;if("scrollBehavior"in i.style&&!a)return e;e.destroy(),o.addEventListener("click",p,!1),o.addEventListener("scroll",S),n.addEventListener("hashchange",b)}return e},this.destroy=function(r){return r=r||{},t&&(o.removeEventListener("click",p,!1),o.removeEventListener("scroll",S),n.removeEventListener("hashchange",b)),e},t){var l=!1;try{var a=Object.defineProperty({},"preventScroll",{get:function(){l=!0}});i.focus(a)}catch(e){}var c,s=/scroll-behavior:[\s]*([^;"`'\s]+)/,u=getComputedStyle(r),f=[]}function h(){for(var e=[r.style.scrollBehavior,(s.exec(r.getAttribute("style"))||[])[1],u.getPropertyValue("--scroll-behavior"),(s.exec(u.fontFamily)||[])[1]],t=0;t<e.length;t++){var n=(i=void 0,i=null,o=(o=e[t])&&o.trim(),/^smooth$/.test(o)&&(i=!0),/^(initial|inherit|auto|unset)$/.test(o)&&(i=!1),i);if(null!==n)return n}var o,i;return!1}function v(e){if(!/^a$/i.test(e.tagName)||!/#/.test(e.href))return!1;var t=e.pathname;return"/"!==t[0]&&(t="/"+t),e.hostname===location.hostname&&t===location.pathname&&(!e.search||e.search===location.search)}function d(e){if(e.focus({preventScroll:!0}),o.activeElement!==e){var t=e.getAttribute("tabindex");if(e.setAttribute("tabindex","-1"),"none"===getComputedStyle(e).outlineStyle){var n=e.style.outlineStyle;e.style.outlineStyle="none",e.addEventListener("blur",function o(){e.style.outlineStyle=n,e.setAttribute("tabindex",t),e.removeEventListener("blur",o)})}e.focus({preventScroll:!0})}}function y(e){if("string"!=typeof e)return null;var t=(e=function(e){try{e=decodeURIComponent(e)}catch(e){}return e}(e))?o.getElementById(e.slice(1)):o.body;return"#top"!==e||t||(t=o.body),t}function m(e){l||clearTimeout(c),e===o.body?n.scroll({top:0,left:0,behavior:"smooth"}):e.scrollIntoView({behavior:"smooth",block:"start"}),l?d(e):c=setTimeout(d.bind(null,e),450)}function p(e){var t=e.metaKey||e.ctrlKey||e.shiftKey||0!==e.button;if(!e.defaultPrevented&&!t&&h()){var r=function e(t,n){return n(t)?t:t.parentElement?e(t.parentElement,n):null}(function(e){return(e=e||n.event).target||e.srcElement}(e),v);if(r){var i=r.hash,l=y(i);l&&(e.preventDefault(),m(l),history.pushState&&history.pushState(null,o.title,i||"#"))}}}function b(){if(o.body&&h()){var e=y(location.hash);if(e){var t=E(),r=f[f[1]===t?0:1];n.scroll({top:r,behavior:"instant"}),m(e)}}}function E(){return r.scrollTop||o.body.scrollTop}function S(){o.body&&(f[0]=f[1],f[1]=E())}}).polyfill()},!(t=this&&this.__sap_ES_MODULE__)&&"function"==typeof define&&define.amd?define([],e):!t&&"object"==typeof module&&module.exports?module.exports=e():this.SmoothscrollAnchorPolyfill=e();