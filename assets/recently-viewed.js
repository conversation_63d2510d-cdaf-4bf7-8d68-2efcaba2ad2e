!function(){"use strict";const t="[data-section-type]";customElements.get("recently-viewed")||customElements.define("recently-viewed",class extends HTMLElement{constructor(){super(),this.section=this.closest(t),this.howManyToShow=parseInt(this.dataset.limit)||3,this.target=this.dataset.target,this.wrapperId=this.dataset.wrapperId||this.id}connectedCallback(){Shopify.Products.showRecentlyViewed({howManyToShow:this.howManyToShow,wrapperId:this.wrapperId,section:this.section,target:this.target,onComplete:()=>{this.dispatchEvent(new CustomEvent("theme:recently-viewed:loaded",{bubbles:!1}))}})}})}();
