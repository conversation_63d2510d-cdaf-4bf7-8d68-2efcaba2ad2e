!function(){"use strict";const t="[data-section-type]",e="[data-collection-sidebar]",i="[data-collection-sidebar-slide-out]",o="[data-collection-sidebar-close]",s="[data-collection-filters-form]",r="input",n="select",l="label",c="textarea",a="[data-field-price-min]",h="[data-field-price-max]",d="data-field-price-min",u="data-field-price-max",m="[data-se-min-value]",f="[data-se-max-value]",p="data-se-min-value",v="data-se-max-value",S="data-se-min",b="data-se-max",E="[data-show-more]",g="[data-link-hidden]",w="[data-collection-nav]",y="[data-products-grid]",L="[data-active-filters]",q="data-active-filters-count",C="[data-filter-update-url]",k="[data-sort-enabled]",A="[data-sort-link]",B="data-value",F="[data-popout-toggle]",M="[data-sort-button-text]",U="[data-results-count]",x="hidden",T="is-focused",O="is-loading",P="is-active";class R extends HTMLElement{constructor(){super(),this.container=this.closest(t),this.collectionSidebar=this.container.querySelector(e),this.collectionSidebarSlideOut=this.container.querySelector(i),this.form=this.querySelector(s),this.productsContainer=this.container.querySelector(y),this.collectionNav=this.container.querySelector(w),this.sort=this.container.querySelector(k),this.sortButton=this.container.querySelector(F),this.sortLinks=this.container.querySelectorAll(A),this.filterUrlButtons=this.container.querySelectorAll(C),this.collectionSidebarCloseButtons=this.container.querySelectorAll(o),this.showMoreOptions=this.querySelectorAll(E),this.a11y=window.theme.a11y,this.updatePriceEvent=window.theme.debounce((t=>this.updatePrice(t)),500),this.updateRangeEvent=t=>this.updateRange(t),this.showMoreEvent=t=>this.showMore(t),this.onSortButtonClickEvent=t=>this.onSortButtonClick(t),this.submitFormEvent=t=>this.submitForm(t),this.collectionSidebarCloseEvent=t=>this.collectionSidebarClose(t),this.filterUpdateFromUrlEvent=t=>this.filterUpdateFromUrl(t)}connectedCallback(){this.sort&&this.sortLinks.length&&this.sortLinks.forEach((t=>{t.addEventListener("click",this.onSortButtonClickEvent)})),this.collectionSidebar&&this.form&&(this.collectionSidebar.addEventListener("input",this.updatePriceEvent),this.collectionSidebar.addEventListener("theme:range:update",this.updateRangeEvent)),this.showMoreOptions.length&&this.showMoreOptions.forEach((t=>{t.addEventListener("click",this.showMoreEvent)})),(this.collectionSidebar||this.sort)&&window.addEventListener("popstate",this.submitFormEvent),this.filterUrlButtons.length&&this.filterUrlButtons.forEach((t=>{t.addEventListener("click",this.filterUpdateFromUrlEvent)})),this.collectionSidebarCloseButtons.length&&this.collectionSidebarCloseButtons.forEach((t=>{t.addEventListener("click",this.collectionSidebarCloseEvent)}))}collectionSidebarClose(t){t.preventDefault(),this.container.dispatchEvent(new CustomEvent("theme:filter:close",{bubbles:!1}))}onSortButtonClick(t){t.preventDefault(),this.sortButton&&this.sortButton.dispatchEvent(new Event("click")),this.sortActions(t,t.currentTarget)}sortActions(t,e,i=!0){const o=this.sort.querySelector(M),s=this.sort.querySelector(`.${P}`);if(o){const t=e?e.textContent.trim():"";o.textContent=t}s&&s.classList.remove(P),this.sort.classList.toggle(P,e),e&&(e.parentElement.classList.add(P),i&&this.submitForm(t))}onSortCheck(t){let e=null;if(window.location.search.includes("sort_by")){const t=new window.URL(window.location.href).searchParams;for(const[i,o]of t.entries()){const t=this.sort.querySelector(`[${B}="${o}"]`);if(i.includes("sort_by")&&t){e=t;break}}}this.sortActions(t,e,!1)}showMore(t){t.preventDefault();const e=t.target.matches(E)?t.target:t.target.closest(E);e.parentElement.classList.add(x),e.parentElement.previousElementSibling.querySelectorAll(g).forEach(((t,e)=>{t.classList.remove(x);const i=t.querySelector(r);0===e&&document.body.classList.contains(T)&&i&&(this.collectionSidebarSlideOut||window.theme.isMobile()?(this.a11y.removeTrapFocus(),this.a11y.trapFocus(this.collectionSidebar,{elementToFocus:i})):i.focus())}))}updatePrice(t){const e=t.type,i=t.target;if((e===r||e===n||e===l||e===c)&&this.form&&"function"==typeof this.form.submit){const e=this.form.querySelector(a),o=this.form.querySelector(h);e&&o&&(i.hasAttribute(d)&&!o.value?o.value=o.placeholder:i.hasAttribute(u)&&!e.value&&(e.value=e.placeholder)),this.submitForm(t)}}updateRange(t){if(this.form&&"function"==typeof this.form.submit){const e=this.form.querySelector(m),i=this.form.querySelector(f),o=this.form.querySelector(a),s=this.form.querySelector(h);if(e&&i&&o&&s&&e.hasAttribute(p)&&i.hasAttribute(v)){const r=parseInt(o.placeholder),n=parseInt(s.placeholder),l=parseInt(e.getAttribute(p)),c=parseInt(i.getAttribute(v));r===l&&n===c||(o.value=l,s.value=c,this.submitForm(t))}}}filterUpdateFromUrl(t){t.preventDefault(),this.submitForm(t,t.currentTarget.getAttribute("href"))}submitForm(t,i=""){if(!t||t&&"popstate"!==t.type)if(""===i){let t=new window.URL(window.location.href).searchParams;const e=t,i=Object.fromEntries(e),o=t.toString();if(o.includes("filter.")||o.includes("page=")||o.includes("sort_by="))for(const e in i)(e.includes("filter.")||"page"===e||"sort_by"===e)&&t.delete(e);if(this.form){const e=new FormData(this.form),i=new URLSearchParams(e),o=this.form.querySelector(m),s=this.form.querySelector(f),r=o&&o.hasAttribute(S)?o.getAttribute(S):"",n=s&&s.hasAttribute(b)?s.getAttribute(b):"";let l=0;for(let[e,o]of i.entries())(e.includes("filter.")&&o||e.includes("sort_by")&&o)&&(t.append(e,o),(o===r&&"filter.v.price.gte"===e||o===n&&"filter.v.price.lte"===e)&&(l+=1));2===l&&(t.delete("filter.v.price.gte"),t.delete("filter.v.price.lte"))}const s=t.toString(),r=s?`?${s}`:location.pathname;window.history.pushState(null,"",r)}else window.history.pushState(null,"",i);else this.sort&&this.onSortCheck(t);this.productsContainer&&(this.productsContainer.classList.add(O),fetch(`${window.location.pathname}${window.location.search}`).then((t=>t.text())).then((t=>{const i=(new DOMParser).parseFromString(t,"text/html"),o=this.container.querySelector(U);if(o){const t=i.querySelector(U);o.innerHTML=t.innerHTML}if(this.productsContainer.innerHTML=i.querySelector(y).innerHTML,this.collectionSidebar){this.collectionSidebar.innerHTML=i.querySelector(e).innerHTML;const t=this.collectionSidebar.querySelector(`[${q}]`),o=this.container.querySelectorAll(L);if(t&&o.length){const e=parseInt(t.getAttribute(q));o.forEach((t=>{t.textContent=e,t.classList.toggle(x,e<1)}))}}this.collectionNav&&window.theme.scrollTo(this.productsContainer.getBoundingClientRect().top-this.collectionNav.offsetHeight),setTimeout((()=>{this.productsContainer.classList.remove(O)}),500)})).catch((t=>{console.log(t)})))}disconnectedCallback(){this.collectionSidebar&&this.form&&(this.collectionSidebar.removeEventListener("input",this.updatePriceEvent),this.collectionSidebar.removeEventListener("theme:range:update",this.updateRangeEvent)),this.showMoreOptions.length&&this.showMoreOptions.forEach((t=>{t.removeEventListener("click",this.showMoreEvent)})),this.sort&&this.sortLinks.length&&this.sortLinks.forEach((t=>{t.removeEventListener("click",this.onSortButtonClickEvent)})),(this.collectionSidebar||this.sort)&&window.removeEventListener("popstate",this.submitFormEvent),this.filterUrlButtons.length&&this.filterUrlButtons.forEach((t=>{t.removeEventListener("click",this.filterUpdateFromUrlEvent)})),this.collectionSidebarCloseButtons.length&&this.collectionSidebarCloseButtons.forEach((t=>{t.removeEventListener("click",this.collectionSidebarCloseEvent)}))}}customElements.get("collection-filters-form")||customElements.define("collection-filters-form",R)}();
