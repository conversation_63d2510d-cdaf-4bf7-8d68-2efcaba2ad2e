!function(){"use strict";const t="[data-timeline-rows]",e="[data-timeline-row]",s="[data-timeline-row-editor]",i="[data-timeline-button]",o="[data-block-scroll]",l="is-selected";customElements.get("timeline-component")||customElements.define("timeline-component",class extends HTMLElement{constructor(){super(),this.rows=this.querySelectorAll(e),this.rows.length<2||(this.rowsWidth=0,this.rowsHeight=[],this.holderTop=this.getBoundingClientRect().top+window.scrollY,this.holderHeight=this.offsetHeight,this.buttons=this.querySelectorAll(i),this.rowsHolder=this.querySelector(t),this.requestAnimation=null,this.isDesktopView=!window.theme.isMobile(),this.isScrollEnabled=!1,this.scrollEvent=t=>this.scrollEvents(t),this.resizeEvent=()=>this.resizeEvents())}connectedCallback(){this.calculateRowsDimensions(),this.requestAnimation=requestAnimationFrame((()=>this.calculatePosition())),this.isDesktopView&&!this.isScrollEnabled&&(this.isScrollEnabled=!0,document.addEventListener("theme:scroll",this.scrollEvent)),this.rowsHolder.addEventListener("scroll",this.scrollEvent),document.addEventListener("theme:resize:width",this.resizeEvent),this.buttons.length&&this.buttons.forEach(((t,s)=>{t.addEventListener("click",(t=>{t.preventDefault();const i=t.currentTarget.closest(e);if(i)if(window.theme.isMobile()){const t=16,e=i.offsetLeft-t;this.rowsHolder.scrollTo({left:e,behavior:"smooth"})}else{const t=Math.max(...this.rowsHeight),e=this.holderHeight/this.rows.length,i=this.getBoundingClientRect().top,o=(s>0&&t<e?e*s+i:i)+1;window.theme.scrollTo(o)}}))}))}resizeEvents(){this.holderTop=this.getBoundingClientRect().top+window.scrollY,this.holderHeight=this.offsetHeight,this.calculateRowsDimensions(),this.requestAnimation=requestAnimationFrame((()=>this.calculatePosition()));const t=!window.theme.isMobile();t&&!this.isScrollEnabled?(this.isScrollEnabled=!0,document.addEventListener("theme:scroll",this.scrollEvent)):!t&&this.isScrollEnabled&&(this.isScrollEnabled=!1,document.removeEventListener("theme:scroll",this.scrollEvent))}scrollEvents(t){if(!this.requestAnimation){const e=t.currentTarget;this.requestAnimation=requestAnimationFrame((()=>this.calculatePosition(e)))}}removeAnimationFrame(){this.requestAnimation&&(cancelAnimationFrame(this.requestAnimation),this.requestAnimation=null)}calculateRowsDimensions(){if(this.rows.length){this.rowsHeight=[],this.rowsWidth=0;let t=0;this.rows.forEach(((e,i)=>{if(this.rowsHeight.push(e.offsetHeight),this.rowsWidth+=e.offsetWidth+parseInt(getComputedStyle(e).marginRight),window.Shopify.designMode){const o=this.querySelectorAll(s);o.length&&(t+=e.offsetHeight,o[i]?.style.setProperty("--row-height-min",`${e.offsetHeight}px`),o[i+1]?.style.setProperty("--row-top-mobile",`${t}px`))}}))}}calculatePosition(t=null){this.removeAnimationFrame(),this.holderTop=this.getBoundingClientRect().top+window.scrollY;const e=!window.theme.isMobile(),s=this.holderHeight/this.rows.length;let i=this.holderTop;const o=window.innerHeight,n=window.scrollY*****o;let r=0;if(!e&&t){const e=t.scrollLeft-16,s=t.offsetWidth;r=e/(this.rowsWidth-s)*100,this.style.setProperty("--percent-mobile",`${r}%`)}e&&(n<this.holderTop||n>this.holderTop+this.holderHeight+o)||this.rows.forEach(((t,o)=>{if(o>0){const o=t.offsetLeft/this.rowsWidth*100;let h=r>o;e&&(h=i+s<n,i-s<n&&t.previousElementSibling?.style.setProperty("--percent-desktop",(n-i)/s*100+"%")),t.classList.toggle(l,h)}i+=s}))}disconnectedCallback(){this.isScrollEnabled&&(this.isScrollEnabled=!1,document.removeEventListener("theme:scroll",this.scrollEvent)),this.rowsHolder.removeEventListener("scroll",this.scrollEvent),document.removeEventListener("theme:resize:width",this.resizeEvent)}onBlockSelect(t){const e=this.querySelector(o);if(e){const s=t.srcElement,i=[...s.parentElement.children].indexOf(s),o=this.rows[i];o&&e.scrollTo({top:0,left:o.offsetLeft,behavior:"smooth"})}}})}();
