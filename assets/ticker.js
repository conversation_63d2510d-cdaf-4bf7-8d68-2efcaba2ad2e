!function(){"use strict";const t="[data-ticker-scale]",e="[data-ticker-text]",i="data-clone",s="autoplay",c="speed",h="ticker--animated",r="ticker--unloaded",a="ticker__comparitor",o=1.63,n=100;customElements.get("ticker-bar")||customElements.define("ticker-bar",class extends HTMLElement{constructor(){super(),this.autoplay=this.hasAttribute(s),this.scale=this.querySelector(t),this.text=this.querySelector(e),this.speed=this.hasAttribute(c)?this.getAttribute(c):o,this.comparitor=this.text.cloneNode(!0),this.comparitor.classList.add(a),this.appendChild(this.comparitor),this.scale.classList.remove(r),this.checkWidthEvent=this.checkWidth.bind(this)}connectedCallback(){this.checkWidth(),this.addEventListener("theme:ticker:refresh",window.theme.debounce((()=>this.checkWidthEvent()),50)),screen.orientation.addEventListener("change",this.checkWidthEvent),document.addEventListener("theme:resize:width",this.checkWidthEvent)}disconnectedCallback(){document.removeEventListener("theme:resize:width",this.checkWidthEvent)}checkWidth(){this.text=this.querySelector(e);const t=2*window.getComputedStyle(this).paddingLeft.replace("px",""),s=this.clientWidth-t<this.comparitor.clientWidth;if(s||this.autoplay){this.text.classList.remove(h);const c=this.scale.querySelectorAll(`[${i}]`),r=this.autoplay?parseInt((window.innerWidth-t)/this.text.clientWidth):2;if(c?.forEach((t=>{t.remove()})),this.autoplay||s)for(let t=0;t<=r;t++){const t=this.text.cloneNode(!0);t.setAttribute(i,""),this.scale.appendChild(t)}const a=(this.text.clientWidth/n*Number(this.speed)).toFixed(2);this.scale.style.removeProperty("--animation-time"),this.scale.style.setProperty("--animation-time",`${a}s`),this.scale.querySelectorAll(e)?.forEach((t=>{t.classList.add(h)}))}else{this.text.classList.add(h);this.scale.querySelectorAll(`[${i}]`).forEach((t=>{t.parentNode.removeChild(t)})),this.text.classList.remove(h)}}})}();
