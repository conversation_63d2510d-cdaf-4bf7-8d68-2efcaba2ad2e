!function(){"use strict";const t="[data-featured-image]",e="[data-featured-aside]",i="[data-featured-content]",s="[data-featured-wrapper]",r="data-horizontal-scroll",o="data-horizontal-scroll-reversed";customElements.get("featured-product")||customElements.define("featured-product",class extends HTMLElement{constructor(){super(),this.horizontalScroll=this.hasAttribute(r),this.horizontalScrollReversed=this.hasAttribute(o),this.images=this.querySelectorAll(t),this.imagesHolder=this.querySelector(e),this.contentHolder=this.querySelector(i),this.wrapper=this.querySelector(s),this.requestAnimationSticky=null,this.lastPercent=0,this.scrollEvent=()=>this.scrollEvents(),this.calculateHorizontalPositionEvent=()=>this.calculateHorizontalPosition(),this.calculateHeightEvent=()=>this.calculateHeight()}connectedCallback(){this.horizontalScroll&&this.imagesHolder&&(this.requestAnimationSticky=requestAnimationFrame(this.calculateHorizontalPositionEvent),document.addEventListener("theme:scroll",this.scrollEvent)),this.wrapper&&this.contentHolder&&this.images.length&&(this.calculateHeight(),document.addEventListener("theme:resize:width",this.calculateHeightEvent))}disconnectedCallback(){this.horizontalScroll&&this.imagesHolder&&document.removeEventListener("theme:scroll",this.scrollEvent),this.wrapper&&this.contentHolder&&this.images.length&&document.removeEventListener("theme:resize:width",this.calculateHeightEvent)}scrollEvents(){this.requestAnimationSticky||(this.requestAnimationSticky=requestAnimationFrame(this.calculateHorizontalPositionEvent))}removeAnimationFrame(){this.requestAnimationSticky&&(cancelAnimationFrame(this.requestAnimationSticky),this.requestAnimationSticky=null)}calculateHorizontalPosition(){let t=window.scrollY+this.headerHeight;const e=t+window.innerHeight,i=this.imagesHolder.offsetTop,s=this.imagesHolder.offsetHeight,r=i+s+this.headerHeight,o=s-(window.innerHeight-this.headerHeight),n=this.horizontalScrollReversed?1:-1;let a=0;a=t>=i&&e<=r?(t-i)/o*100:t<i?0:100,a*=this.images.length-1,this.style.setProperty("--translateX",a*n+"%"),this.lastPercent!==a?this.requestAnimationSticky=requestAnimationFrame(this.calculateHorizontalPositionEvent):this.requestAnimationSticky&&this.removeAnimationFrame(),this.lastPercent=a}calculateHeight(){let{stickyHeaderHeight:t}=window.theme.readHeights();this.style.removeProperty("--min-height"),this.style.setProperty("--min-height",`${this.wrapper.offsetHeight+this.contentHolder.offsetHeight}px`),this.headerHeight=t}})}();
