var themeRellax=function(e){"use strict";var t=function(e,o){var n=Object.create(t.prototype),r=0,i=0,s=0,a=0,l=[],p=!0,d=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||function(e){return setTimeout(e,1e3/60)},c=null,m=!1;try{var u=Object.defineProperty({},"passive",{get:function(){m=!0}});window.addEventListener("testPassive",null,u),window.removeEventListener("testPassive",null,u)}catch(e){}var f=window.cancelAnimationFrame||window.mozCancelAnimationFrame||clearTimeout,w=window.transformProp||function(){var e=document.createElement("div");if(null===e.style.transform){var t=["Webkit","Moz","ms"];for(var o in t)if(void 0!==e.style[t[o]+"Transform"])return t[o]+"Transform"}return"transform"}();n.options={speed:-2,center:!1,wrapper:null,relativeToWrapper:!1,round:!0,vertical:!0,frame:null,horizontal:!1,callback:function(){}},o&&Object.keys(o).forEach((function(e){n.options[e]=o[e]})),e||(e=".rellax");var v="string"==typeof e?document.querySelectorAll(e):[e];if(v.length>0){if(n.elems=v,n.options.wrapper&&!n.options.wrapper.nodeType){var h=document.querySelector(n.options.wrapper);if(!h)return void console.warn("Rellax: The wrapper you're trying to use doesn't exist.");n.options.wrapper=h}if(n.options.frame&&!n.options.frame.nodeType){var g=document.querySelector(n.options.frame);if(!g)return void console.warn("Rellax: The frame you're trying to use doesn't exist.");n.options.frame=g}var y=function(){for(var e=0;e<l.length;e++)n.elems[e].style.cssText=l[e].style;l=[],i=window.innerHeight,a=window.innerWidth,b(),function(){for(var e=0;e<n.elems.length;e++){var t=x(n.elems[e]);l.push(t)}}(),L(),p&&(window.addEventListener("resize",y),p=!1,z())},x=function(e){var t=e.getAttribute("data-rellax-percentage"),o=e.getAttribute("data-rellax-speed"),r=e.getAttribute("data-rellax-zindex")||0,s=e.getAttribute("data-rellax-min"),l=e.getAttribute("data-rellax-max"),p=n.options.wrapper?n.options.wrapper.scrollTop:window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop;n.options.relativeToWrapper&&(p=(window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop)-n.options.wrapper.offsetTop);var d=n.options.vertical&&(t||n.options.center)?p:0,c=n.options.horizontal&&(t||n.options.center)?n.options.wrapper?n.options.wrapper.scrollLeft:window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft:0,m=d+e.getBoundingClientRect().top,u=e.clientHeight||e.offsetHeight||e.scrollHeight,f=c+e.getBoundingClientRect().left,w=e.clientWidth||e.offsetWidth||e.scrollWidth,v=t||(d-m+i)/(u+i),h=t||(c-f+a)/(w+a);n.options.center&&(h=.5,v=.5);var g=o||n.options.speed;if(n.options.frame){var y=n.options.frame,x=u-(y.clientHeight||y.offsetHeight||y.scrollHeight);g=x/100*-1,s=x/2*-1,l=x/2}var b=T(h,v,g),E=e.style.cssText,z="",L=/transform\s*:/i.exec(E);if(L){var A=L.index,O=E.slice(A),R=O.indexOf(";");z=R?" "+O.slice(11,R).replace(/\s/g,""):" "+O.slice(11).replace(/\s/g,"")}return{baseX:b.x,baseY:b.y,top:m,left:f,height:u,width:w,speed:g,style:E,transform:z,zindex:r,min:s,max:l}},b=function(){var e=r,t=s;if(r=n.options.wrapper?n.options.wrapper.scrollTop:(document.documentElement||document.body.parentNode||document.body).scrollTop||window.pageYOffset,s=n.options.wrapper?n.options.wrapper.scrollLeft:(document.documentElement||document.body.parentNode||document.body).scrollLeft||window.pageXOffset,n.options.relativeToWrapper){var o=(document.documentElement||document.body.parentNode||document.body).scrollTop||window.pageYOffset;r=o-n.options.wrapper.offsetTop}return!(e==r||!n.options.vertical)||!(t==s||!n.options.horizontal)},T=function(e,t,o){var r={},i=o*(100*(1-e)),s=o*(100*(1-t));return r.x=n.options.round?Math.round(i):Math.round(100*i)/100,r.y=n.options.round?Math.round(s):Math.round(100*s)/100,r},E=function(){window.removeEventListener("resize",E),window.removeEventListener("orientationchange",E),(n.options.wrapper?n.options.wrapper:window).removeEventListener("scroll",E),(n.options.wrapper?n.options.wrapper:document).removeEventListener("touchmove",E),c=d(z)},z=function(){b()&&!1===p?(L(),c=d(z)):(c=null,window.addEventListener("resize",E),window.addEventListener("orientationchange",E),(n.options.wrapper?n.options.wrapper:window).addEventListener("scroll",E,!!m&&{passive:!0}),(n.options.wrapper?n.options.wrapper:document).addEventListener("touchmove",E,!!m&&{passive:!0}))},L=function(){for(var e,t=0;t<n.elems.length;t++){var o=(r-l[t].top+i)/(l[t].height+i),p=(s-l[t].left+a)/(l[t].width+a),d=(e=T(p,o,l[t].speed)).y-l[t].baseY,c=e.x-l[t].baseX;null!==l[t].min&&(n.options.vertical&&!n.options.horizontal&&(d=d<=l[t].min?l[t].min:d),n.options.horizontal&&!n.options.vertical&&(c=c<=l[t].min?l[t].min:c)),null!==l[t].max&&(n.options.vertical&&!n.options.horizontal&&(d=d>=l[t].max?l[t].max:d),n.options.horizontal&&!n.options.vertical&&(c=c>=l[t].max?l[t].max:c));var m=l[t].zindex,u="translate3d("+(n.options.horizontal?c:"0")+"px,"+(n.options.vertical?d:"0")+"px,"+m+"px) "+l[t].transform;n.elems[t].style[w]=u}n.options.callback(e)};return n.destroy=function(){for(var e=0;e<n.elems.length;e++)n.elems[e].style.cssText=l[e].style;p||(window.removeEventListener("resize",y),p=!0),f(c),c=null},y(),n.refresh=y,n}console.warn("Rellax: The elements you're trying to select don't exist.")};return e.Rellax=t,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
