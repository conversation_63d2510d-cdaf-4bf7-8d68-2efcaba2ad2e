/*
* @license
* Broadcast Theme (c) Invisible Themes
*
* The contents of this file should not be modified.
* add any minor changes to assets/custom.js
*
*/
!function(t){"use strict";!function(){const t={NODE_ENV:"production"};try{if(process)return process.env=Object.assign({},process.env),void Object.assign(process.env,t)}catch(t){}globalThis.process={env:t}}(),window.theme=window.theme||{},window.theme.sizes={mobile:480,small:750,large:990,widescreen:1400},window.theme.focusable='button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',window.theme.getWindowWidth=function(){return document.documentElement.clientWidth||document.body.clientWidth||window.innerWidth},window.theme.getWindowHeight=function(){return document.documentElement.clientHeight||document.body.clientHeight||window.innerHeight},window.theme.isMobile=function(){return window.theme.getWindowWidth()<window.theme.sizes.small};window.theme.formatMoney=function(t,e){"string"==typeof t&&(t=t.replace(".",""));let s="";const i=/\{\{\s*(\w+)\s*\}\}/,o=e||"${{amount}}";function r(t,e=2,s=",",i="."){if(isNaN(t)||null==t)return 0;const o=(t=(t/100).toFixed(e)).split(".");return o[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g,`$1${s}`)+(o[1]?i+o[1]:"")}switch(o.match(i)[1]){case"amount":s=r(t,2);break;case"amount_no_decimals":s=r(t,0);break;case"amount_with_comma_separator":s=r(t,2,".",",");break;case"amount_no_decimals_with_comma_separator":s=r(t,0,".",",");break;case"amount_with_apostrophe_separator":s=r(t,2,"'",".");break;case"amount_no_decimals_with_space_separator":s=r(t,0," ","");break;case"amount_with_space_separator":s=r(t,2," ",",");break;case"amount_with_period_and_space_separator":s=r(t,2," ",".")}return o.replace(i,s)},window.theme.debounce=function(t,e){let s;return function(){if(t){const i=()=>t.apply(this,arguments);clearTimeout(s),s=setTimeout(i,e)}}};let e=o(),s=!0;function i(){const{windowHeight:t,headerHeight:i,logoHeight:r,footerHeight:n,collectionNavHeight:a}=window.theme.readHeights(),l=o();(!s||l!==e||window.innerWidth>window.theme.sizes.mobile)&&(document.documentElement.style.setProperty("--full-height",`${t}px`),document.documentElement.style.setProperty("--three-quarters",t*(3/4)+"px"),document.documentElement.style.setProperty("--two-thirds",t*(2/3)+"px"),document.documentElement.style.setProperty("--one-half",t/2+"px"),document.documentElement.style.setProperty("--one-third",t/3+"px"),e=l,s=!1),document.documentElement.style.setProperty("--collection-nav-height",`${a}px`),document.documentElement.style.setProperty("--header-height",`${i}px`),document.documentElement.style.setProperty("--footer-height",`${n}px`),document.documentElement.style.setProperty("--content-full",t-i-r/2+"px"),document.documentElement.style.setProperty("--content-min",t-i-n+"px")}function o(){return window.matchMedia("(orientation: portrait)").matches?"portrait":window.matchMedia("(orientation: landscape)").matches?"landscape":void 0}function r(t){const e=document.querySelector(t);return e?e.offsetHeight:0}window.theme.readHeights=function(){const t={};return t.windowHeight=Math.min(window.screen.height,window.innerHeight),t.footerHeight=r('[data-section-type*="footer"]'),t.headerHeight=r("[data-header-height]"),t.stickyHeaderHeight=document.querySelector("[data-header-sticky]")?t.headerHeight:0,t.collectionNavHeight=r("[data-collection-nav]"),t.logoHeight=function(){const t=r("[data-footer-logo]");return t>0?t+20:0}(),t},i(),window.addEventListener("DOMContentLoaded",i),document.addEventListener("theme:resize",i),document.addEventListener("shopify:section:load",i),window.theme.scrollTo=t=>{const e=document.querySelector("[data-header-sticky]")?document.querySelector("[data-header-height]").offsetHeight:0;window.scrollTo({top:t+window.scrollY-e,left:0,behavior:"smooth"})};const n={},a={forceFocus(t,e){e=e||{};var s=t.tabIndex;t.tabIndex=-1,t.dataset.tabIndex=s,t.focus(),void 0!==e.className&&t.classList.add(e.className),t.addEventListener("blur",(function i(o){o.target.removeEventListener(o.type,i),t.tabIndex=s,delete t.dataset.tabIndex,void 0!==e.className&&t.classList.remove(e.className)}))},focusHash(t){t=t||{};var e=window.location.hash,s=document.getElementById(e.slice(1));if(s&&t.ignore&&s.matches(t.ignore))return!1;e&&s&&this.forceFocus(s,t)},bindInPageLinks(t){return t=t||{},Array.prototype.slice.call(document.querySelectorAll('a[href^="#"]')).filter((e=>{if("#"===e.hash||""===e.hash)return!1;if(t.ignore&&e.matches(t.ignore))return!1;if(s=e.hash.substr(1),null===document.getElementById(s))return!1;var s,i=document.querySelector(e.hash);return!!i&&(e.addEventListener("click",(()=>{this.forceFocus(i,t)})),!0)}))},focusable(t){return Array.prototype.slice.call(t.querySelectorAll("[tabindex],[draggable],a[href],area,button:enabled,input:not([type=hidden]):enabled,object,select:enabled,textarea:enabled")).filter((t=>!(!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)||!this.isVisible(t))))},trapFocus(t,e){e=e||{};var s=this.focusable(t),i=e.elementToFocus||t,o=s[0],r=s[s.length-1];this.removeTrapFocus(),n.focusin=function(e){t!==e.target&&!t.contains(e.target)&&o&&o===e.target&&o.focus(),e.target!==t&&e.target!==r&&e.target!==o||document.addEventListener("keydown",n.keydown)},n.focusout=function(){document.removeEventListener("keydown",n.keydown)},n.keydown=function(e){"Tab"===e.code&&(e.target!==r||e.shiftKey||(e.preventDefault(),o.focus()),e.target!==t&&e.target!==o||!e.shiftKey||(e.preventDefault(),r.focus()))},document.addEventListener("focusout",n.focusout),document.addEventListener("focusin",n.focusin),this.forceFocus(i,e)},removeTrapFocus(){document.removeEventListener("focusin",n.focusin),document.removeEventListener("focusout",n.focusout),document.removeEventListener("keydown",n.keydown)},autoFocusLastElement(){window.a11y.lastElement&&document.body.classList.contains("is-focused")&&setTimeout((()=>{var t;null===(t=window.a11y.lastElement)||void 0===t||t.focus()}))},accessibleLinks(t,e){if("string"!=typeof t)throw new TypeError(t+" is not a String.");if(0!==(t=document.querySelectorAll(t)).length){(e=e||{}).messages=e.messages||{};var s,i,o,r={newWindow:e.messages.newWindow||"Opens in a new window.",external:e.messages.external||"Opens external website.",newWindowExternal:e.messages.newWindowExternal||"Opens external website in a new window."},n=e.prefix||"a11y",a={newWindow:n+"-new-window-message",external:n+"-external-message",newWindowExternal:n+"-new-window-external-message"};t.forEach((t=>{var e=t.getAttribute("target"),s=t.getAttribute("rel"),i=function(t){return t.hostname!==window.location.hostname}(t),o="_blank"===e,r=null===s||-1===s.indexOf("noopener");if(o&&r){var n=null===s?"noopener":s+" noopener";t.setAttribute("rel",n)}i&&o?t.setAttribute("aria-describedby",a.newWindowExternal):i?t.setAttribute("aria-describedby",a.external):o&&t.setAttribute("aria-describedby",a.newWindow)})),s=r,i=document.createElement("ul"),o=Object.keys(s).reduce(((t,e)=>t+"<li id="+a[e]+">"+s[e]+"</li>"),""),i.setAttribute("hidden",!0),i.innerHTML=o,document.body.appendChild(i)}},isVisible(t){var e=window.getComputedStyle(t);return"none"!==e.display&&"hidden"!==e.visibility}};function l(){if(document.querySelector("cart-items"))return;const t=document.createElement("cart-items");document.body.appendChild(t)}function c(t){t.querySelectorAll(".form-field").forEach((t=>{const e=t.querySelector("label"),s=t.querySelector("input, textarea");e&&s&&(s.addEventListener("keyup",(t=>{""!==t.target.value?e.classList.add("label--float"):e.classList.remove("label--float")})),s.value&&s.value.length&&e.classList.add("label--float"))}))}window.theme=window.theme||{},window.theme.a11y=a,window.theme.throttle=(t,e)=>{let s,i;return function o(...r){const n=Date.now();i=clearTimeout(i),!s||n-s>=e?(t.apply(null,r),s=n):i=setTimeout(o.bind(null,...r),e-(n-s))}};let d=window.theme.getWindowWidth(),h=window.theme.getWindowHeight();let u=window.scrollY,p=null,m=null,v=null,g=null,b=0;function w(e){setTimeout((()=>{b&&clearTimeout(b),t.disablePageScroll(e.detail,{allowTouchMove:t=>"TEXTAREA"===t.tagName}),document.documentElement.setAttribute("data-scroll-locked","")}))}function f(t){const e=t.detail;e?b=setTimeout(y,e):y()}function y(){t.clearQueueScrollLocks(),t.enablePageScroll(),document.documentElement.removeAttribute("data-scroll-locked")}const L=(t,e="",s)=>{const i=s||document.createElement("div");return i.classList.add(e),t.parentNode.insertBefore(i,t),i.appendChild(t)};function E(t){t.querySelectorAll(".rte table").forEach((t=>{L(t,"rte__table-wrapper"),t.setAttribute("data-scroll-lock-scrollable","")}));t.querySelectorAll('.rte iframe[src*="youtube.com/embed"], .rte iframe[src*="player.vimeo"], .rte iframe#admin_bar_iframe').forEach((t=>{L(t,"rte__video-wrapper")}))}function S(t){const e=t.querySelectorAll("[data-aria-toggle]");e.length&&e.forEach((t=>{t.addEventListener("click",(function(t){t.preventDefault();const e=t.currentTarget;e.setAttribute("aria-expanded","false"==e.getAttribute("aria-expanded")?"true":"false");const s=e.getAttribute("aria-controls"),i=document.querySelector(`#${s}`),o=()=>{i.classList.remove("expanding"),i.removeEventListener("transitionend",o)},r=()=>{i.classList.add("expanding"),i.removeEventListener("transitionstart",r)};i.addEventListener("transitionstart",r),i.addEventListener("transitionend",o),i.classList.toggle("expanded")}))}))}const A="is-loading",k="img.is-loading";const C="[data-aos]:not(.aos-animate)",q="[data-aos-anchor]",T="[data-aos]:not([data-aos-anchor]):not(.aos-animate)",M="aos-animate",x={attributes:!1,childList:!0,subtree:!0};let I=[];const H=t=>{for(const e of t)if("childList"===e.type){const t=e.target,s=t.querySelectorAll(C),i=t.querySelectorAll(q);s.length&&s.forEach((t=>{O.observe(t)})),i.length&&D(i)}},O=new IntersectionObserver(((t,e)=>{t.forEach((t=>{t.isIntersecting&&(t.target.classList.add(M),e.unobserve(t.target))}))}),{root:null,rootMargin:"0px",threshold:[0,.1,.25,.5,.75,1]}),P=new IntersectionObserver(((t,e)=>{t.forEach((t=>{if(t.intersectionRatio){const s=t.target.querySelectorAll(C);s.length&&s.forEach((t=>{t.classList.add(M)})),e.unobserve(t.target);const i=I.indexOf("#"+t.target.id);-1!==i&&I.splice(i,1)}}))}),{root:null,rootMargin:"0px",threshold:[.1,.25,.5,.75,1]});function D(t){t.length&&t.forEach((t=>{const e=t.dataset.aosAnchor;if(e&&-1===I.indexOf(e)){const t=document.querySelector(e);t&&(P.observe(t),I.push(e))}}))}window.requestIdleCallback=window.requestIdleCallback||function(t){var e=Date.now();return setTimeout((function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})}),1)},window.cancelIdleCallback=window.cancelIdleCallback||function(t){clearTimeout(t)},window.theme.settings.enableAnimations&&(!function(){const t=document.querySelectorAll(T);t.length&&t.forEach((t=>{O.observe(t)}))}(),function(){const t=document.querySelectorAll(q);t.length&&D(t)}(),new MutationObserver(H).observe(document.body,x),document.addEventListener("shopify:section:unload",(t=>{var e;const s="#"+(null===(e=t.target.querySelector("[data-section-id]"))||void 0===e?void 0:e.id),i=I.indexOf(s);-1!==i&&I.splice(i,1)}))),window.addEventListener("resize",window.theme.debounce((function(){document.dispatchEvent(new CustomEvent("theme:resize",{bubbles:!0})),d!==window.theme.getWindowWidth()&&(document.dispatchEvent(new CustomEvent("theme:resize:width",{bubbles:!0})),d=window.theme.getWindowWidth()),h!==window.theme.getWindowHeight()&&(document.dispatchEvent(new CustomEvent("theme:resize:height",{bubbles:!0})),h=window.theme.getWindowHeight())}),50)),function(){let t;window.addEventListener("scroll",(function(){t&&window.cancelAnimationFrame(t),t=window.requestAnimationFrame((function(){!function(){const t=window.scrollY;t>u?(m=!0,p=!1):t<u?(m=!1,p=!0):(p=null,m=null),u=t,document.dispatchEvent(new CustomEvent("theme:scroll",{detail:{up:p,down:m,position:t},bubbles:!1})),p&&!v&&document.dispatchEvent(new CustomEvent("theme:scroll:up",{detail:{position:t},bubbles:!1})),m&&!g&&document.dispatchEvent(new CustomEvent("theme:scroll:down",{detail:{position:t},bubbles:!1})),g=m,v=p}()}))}),{passive:!0}),window.addEventListener("theme:scroll:lock",w),window.addEventListener("theme:scroll:unlock",f)}(),"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?(document.documentElement.className=document.documentElement.className.replace("no-touch","supports-touch"),window.theme.touch=!0):window.theme.touch=!1,document.addEventListener("load",(t=>{"img"==t.target.tagName.toLowerCase()&&t.target.classList.contains(A)&&(t.target.classList.remove(A),t.target.parentNode.classList.remove(A),t.target.parentNode.parentNode.classList.contains(A)&&t.target.parentNode.parentNode.classList.remove(A))}),!0),window.addEventListener("DOMContentLoaded",(()=>{S(document),c(document),E(document),document.querySelectorAll(k).forEach((t=>{t.complete&&(t.classList.remove(A),t.parentNode.classList.remove(A),t.parentNode.parentNode.classList.contains(A)&&t.parentNode.parentNode.classList.remove(A))})),document.body.classList.add("is-loaded"),l(),requestIdleCallback((()=>{Shopify.visualPreviewMode&&document.documentElement.classList.add("preview-mode")}))})),document.addEventListener("shopify:section:load",(t=>{const e=t.target;c(e),E(e),S(document)}));const F="is-focused",_="[data-skip-content]",$='a[href="#"]';function B(t){this.status=t.status||null,this.headers=t.headers||null,this.json=t.json||null,this.body=t.body||null}window.a11y=new class{init(){this.a11y=window.theme.a11y,this.html=document.documentElement,this.body=document.body,this.inPageLink=document.querySelector(_),this.linkesWithOnlyHash=document.querySelectorAll($),this.a11y.focusHash(),this.a11y.bindInPageLinks(),this.clickEvents(),this.focusEvents()}clickEvents(){this.inPageLink&&this.inPageLink.addEventListener("click",(t=>{t.preventDefault()})),this.linkesWithOnlyHash&&this.linkesWithOnlyHash.forEach((t=>{t.addEventListener("click",(t=>{t.preventDefault()}))}))}focusEvents(){document.addEventListener("mousedown",(()=>{this.body.classList.remove(F)})),document.addEventListener("keyup",(t=>{"Tab"===t.code&&this.body.classList.add(F)}))}constructor(){this.init()}},window.theme.waitForAnimationEnd=function(t){return new Promise((e=>{null==t||t.addEventListener("animationend",(function s(i){i.target==t&&(t.removeEventListener("animationend",s),e())}))}))},window.theme.waitForAllAnimationsEnd=function(t){return new Promise(((e,s)=>{const i=t.querySelectorAll("[data-aos]");let o=0;function r(t){o++,o===i.length&&e(),t.target.removeEventListener("animationend",r)}i.forEach((t=>{t.addEventListener("animationend",r)})),o||s()}))},B.prototype=Error.prototype;const W="is-animated",N="is-added",R="is-disabled",U="is-empty",V="is-hidden",j="is-hiding",z="is-loading",J="is-open",X="is-removed",Q="is-success",Y="is-visible",K="is-expanded",G="is-updated",Z="variant--soldout",tt="variant--unavailable",et={apiContent:"[data-api-content]",apiLineItems:"[data-api-line-items]",apiUpsellItems:"[data-api-upsell-items]",apiCartPrice:"[data-api-cart-price]",animation:"[data-animation]",additionalCheckoutButtons:".additional-checkout-buttons",buttonSkipUpsellProduct:"[data-skip-upsell-product]",cartBarAdd:"[data-add-to-cart-bar]",cartCloseError:"[data-cart-error-close]",cartDrawer:"cart-drawer",cartDrawerClose:"[data-cart-drawer-close]",cartEmpty:"[data-cart-empty]",cartErrors:"[data-cart-errors]",cartItemRemove:"[data-item-remove]",cartPage:"[data-cart-page]",cartForm:"[data-cart-form]",cartTermsCheckbox:"[data-cart-acceptance-checkbox]",cartCheckoutButtonWrapper:"[data-cart-checkout-buttons]",cartCheckoutButton:"[data-cart-checkout-button]",cartTotal:"[data-cart-total]",checkoutButtons:"[data-checkout-buttons]",errorMessage:"[data-error-message]",formCloseError:"[data-close-error]",formErrorsContainer:"[data-cart-errors-container]",formWrapper:"[data-form-wrapper]",freeShipping:"[data-free-shipping]",freeShippingGraph:"[data-progress-graph]",freeShippingProgress:"[data-progress-bar]",headerWrapper:"[data-header-wrapper]",item:"[data-item]",itemsHolder:"[data-items-holder]",leftToSpend:"[data-left-to-spend]",navDrawer:"[data-drawer]",outerSection:"[data-section-id]",priceHolder:"[data-cart-price-holder]",quickAddHolder:"[data-quick-add-holder]",quickAddModal:"[data-quick-add-modal]",qtyInput:'input[name="updates[]"]',upsellProductsHolder:"[data-upsell-products]",upsellWidget:"[data-upsell-widget]",termsErrorMessage:"[data-terms-error-message]",collapsibleBody:"[data-collapsible-body]",noscript:"noscript"},st="data-cart-total",it="disabled",ot="data-free-shipping",rt="data-free-shipping-limit",nt="data-item",at="data-item-index",lt="data-item-title",ct="open",dt="data-quick-add-holder",ht="data-scroll-locked",ut="data-upsell-auto-open",pt="name",mt="data-max-inventory-reached",vt="data-error-message-position";let gt=class extends HTMLElement{connectedCallback(){this.cartPage=document.querySelector(et.cartPage),this.cartForm=document.querySelector(et.cartForm),this.cartDrawer=document.querySelector(et.cartDrawer),this.cartEmpty=document.querySelector(et.cartEmpty),this.cartTermsCheckbox=document.querySelector(et.cartTermsCheckbox),this.cartCheckoutButtonWrapper=document.querySelector(et.cartCheckoutButtonWrapper),this.cartCheckoutButton=document.querySelector(et.cartCheckoutButton),this.checkoutButtons=document.querySelector(et.checkoutButtons),this.itemsHolder=document.querySelector(et.itemsHolder),this.priceHolder=document.querySelector(et.priceHolder),this.items=document.querySelectorAll(et.item),this.cartTotal=document.querySelector(et.cartTotal),this.freeShipping=document.querySelectorAll(et.freeShipping),this.cartErrorHolder=document.querySelector(et.cartErrors),this.cartCloseErrorMessage=document.querySelector(et.cartCloseError),this.headerWrapper=document.querySelector(et.headerWrapper),this.navDrawer=document.querySelector(et.navDrawer),this.upsellProductsHolder=document.querySelector(et.upsellProductsHolder),this.subtotal=window.theme.subtotal,this.cart=this.cartDrawer||this.cartPage,this.animateItems=this.animateItems.bind(this),this.addToCart=this.addToCart.bind(this),this.cartAddEvent=this.cartAddEvent.bind(this),this.updateProgress=this.updateProgress.bind(this),this.onCartDrawerClose=this.onCartDrawerClose.bind(this),document.addEventListener("theme:cart:add",this.cartAddEvent),document.addEventListener("theme:announcement:init",this.updateProgress),"drawer"==theme.settings.cartType&&(document.addEventListener("theme:cart-drawer:open",this.animateItems),document.addEventListener("theme:cart-drawer:close",this.onCartDrawerClose)),this.skipUpsellProductsArray=[],this.skipUpsellProductEvent(),this.checkSkippedUpsellProductsFromStorage(),this.toggleCartUpsellWidgetVisibility(),this.circumference=28*Math.PI,this.freeShippingLimit=this.freeShipping.length?100*Number(this.freeShipping[0].getAttribute(rt))*window.Shopify.currency.rate:0,this.freeShippingMessageHandle(this.subtotal),this.updateProgress(),this.build=this.build.bind(this),this.updateCart=this.updateCart.bind(this),this.productAddCallback=this.productAddCallback.bind(this),this.formSubmitHandler=window.theme.throttle(this.formSubmitHandler.bind(this),50),this.cartPage&&this.animateItems(),this.cart&&(this.hasItemsInCart=this.hasItemsInCart.bind(this),this.cartCount=this.getCartItemCount()),this.toggleClassesOnContainers=this.toggleClassesOnContainers.bind(this),this.totalItems=this.items.length,this.showCannotAddMoreInCart=!1,this.cartUpdateFailed=!1,this.cartEvents(),this.cartRemoveEvents(),this.cartUpdateEvents(),document.addEventListener("theme:product:add",this.productAddCallback),document.addEventListener("theme:product:add-error",this.productAddCallback),document.addEventListener("theme:cart:refresh",this.getCart.bind(this))}disconnectedCallback(){document.removeEventListener("theme:cart:add",this.cartAddEvent),document.removeEventListener("theme:cart:refresh",this.cartAddEvent),document.removeEventListener("theme:announcement:init",this.updateProgress),document.removeEventListener("theme:product:add",this.productAddCallback),document.removeEventListener("theme:product:add-error",this.productAddCallback),document.documentElement.hasAttribute(ht)&&document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0}))}onCartDrawerClose(){var t;this.resetAnimatedItems(),(null===(t=this.cartDrawer)||void 0===t?void 0:t.classList.contains(J))&&this.cart.classList.remove(G),this.cartEmpty.classList.remove(G),this.cartErrorHolder.classList.remove(K),this.cart.querySelectorAll(et.animation).forEach((t=>{const e=()=>{t.classList.remove(j),t.removeEventListener("animationend",e)};t.classList.add(j),t.addEventListener("animationend",e)}))}cartUpdateEvents(){this.items=document.querySelectorAll(et.item),this.items.forEach((t=>{t.addEventListener("theme:cart:update",(e=>{this.updateCart({id:e.detail.id,quantity:e.detail.quantity},t)}))}))}cartRemoveEvents(){document.querySelectorAll(et.cartItemRemove).forEach((t=>{const e=t.closest(et.item);t.addEventListener("click",(s=>{s.preventDefault(),t.classList.contains(R)||this.updateCart({id:t.dataset.id,quantity:0},e)}))})),this.cartCloseErrorMessage&&this.cartCloseErrorMessage.addEventListener("click",(t=>{t.preventDefault(),this.cartErrorHolder.classList.remove(K)}))}cartAddEvent(t){let e="",s=t.detail.button;if(s.hasAttribute("disabled"))return;const i=s.closest("form");if(!i.checkValidity())return void i.reportValidity();e=new FormData(i);if([...i.elements].some((t=>t.closest(et.noscript)))&&(e=this.handleFormDataDuplicates([...i.elements],e)),null!==i&&i.querySelector('[type="file"]'))return;"drawer"===theme.settings.cartType&&this.cartDrawer&&t.preventDefault();const o=i.getAttribute(mt),r=i.getAttribute(vt);this.showCannotAddMoreInCart=!1,"true"===o&&"cart"===r&&(this.showCannotAddMoreInCart=!0),this.addToCart(e,s)}handleFormDataDuplicates(t,e){return t.length&&"object"==typeof e?(t.forEach((t=>{if(t.closest(et.noscript)){const s=t.getAttribute(pt),i=t.value;if(s){const t=e.getAll(s);t.length>1&&t.splice(t.indexOf(i),1),e.delete(s),e.set(s,t[0])}}})),e):e}cartEvents(){this.cartTermsCheckbox&&(this.cartTermsCheckbox.removeEventListener("change",this.formSubmitHandler),this.cartCheckoutButtonWrapper.removeEventListener("click",this.formSubmitHandler),this.cartForm.removeEventListener("submit",this.formSubmitHandler),this.cartTermsCheckbox.addEventListener("change",this.formSubmitHandler),this.cartCheckoutButtonWrapper.addEventListener("click",this.formSubmitHandler),this.cartForm.addEventListener("submit",this.formSubmitHandler))}formSubmitHandler(){const t=document.querySelector(et.cartTermsCheckbox).checked,e=document.querySelector(et.termsErrorMessage);if(t)e.classList.remove(K),this.cartCheckoutButton.removeAttribute(it);else{if(document.querySelector(et.termsErrorMessage).length>0)return;e.innerText=theme.strings.cartAcceptanceError,this.cartCheckoutButton.setAttribute(it,!0),e.classList.add(K)}}formErrorsEvents(t){const e=t.querySelector(et.formCloseError);null==e||e.addEventListener("click",(e=>{e.preventDefault(),t&&t.classList.remove(Y)}))}getCart(){fetch(theme.routes.cart_url+"?section_id=api-cart-items").then(this.cartErrorsHandler).then((t=>t.text())).then((t=>{const e=document.createElement("div");e.innerHTML=t;const s=e.querySelector(et.apiContent);this.build(s)})).catch((t=>console.log(t)))}addToCart(t,e){this.cart&&this.cart.classList.add(z);const s=null==e?void 0:e.closest(et.quickAddHolder);e&&(e.classList.add(z),e.disabled=!0),s&&s.classList.add(Y),fetch(theme.routes.cart_add_url,{method:"POST",headers:{"X-Requested-With":"XMLHttpRequest",Accept:"application/javascript"},body:t}).then((t=>t.json())).then((t=>{t.status&&(this.addToCartError(t,e),e&&(e.classList.remove(z),e.disabled=!1),!this.showCannotAddMoreInCart)||(this.cart?(e&&(e.classList.remove(z),e.classList.add(N),e.dispatchEvent(new CustomEvent("theme:product:add",{detail:{response:t,button:e},bubbles:!0}))),"page"===theme.settings.cartType&&(window.location=theme.routes.cart_url),this.getCart()):window.location=theme.routes.cart_url)})).catch((t=>{this.addToCartError(t,e),this.enableCartButtons()}))}updateCart(t={},e=null){this.cart.classList.add(z);let s=t.quantity;null!==e&&(s?e.classList.add(z):e.classList.add(X)),this.disableCartButtons();const i=this.cart.querySelector(`[${nt}="${t.id}"]`)||e,o=(null==i?void 0:i.hasAttribute(at))?parseInt(i.getAttribute(at)):0,r=(null==i?void 0:i.hasAttribute(lt))?i.getAttribute(lt):null;if(0===o)return;const n={line:o,quantity:s};fetch(theme.routes.cart_change_url,{method:"post",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(n)}).then((t=>t.text())).then((t=>{if(JSON.parse(t).errors)return this.cartUpdateFailed=!0,this.updateErrorText(r),this.toggleErrorMessage(),this.resetLineItem(e),void this.enableCartButtons();this.getCart()})).catch((t=>{console.log(t),this.enableCartButtons()}))}resetLineItem(t){const e=t.querySelector(et.qtyInput),s=e.getAttribute("value");e.value=s,t.classList.remove(z)}disableCartButtons(){const t=this.cart.querySelectorAll("input"),e=this.cart.querySelectorAll(`button, ${et.cartItemRemove}`);t.length&&t.forEach((t=>{t.classList.add(R),t.blur(),t.disabled=!0})),e.length&&e.forEach((t=>{t.setAttribute(it,!0)}))}enableCartButtons(){const t=this.cart.querySelectorAll("input"),e=this.cart.querySelectorAll(`button, ${et.cartItemRemove}`);t.length&&t.forEach((t=>{t.classList.remove(R),t.disabled=!1})),e.length&&e.forEach((t=>{t.removeAttribute(it)})),this.cart.classList.remove(z)}updateErrorText(t){this.cartErrorHolder.querySelector(et.errorMessage).innerText=t}toggleErrorMessage(){this.cartErrorHolder&&(this.cartErrorHolder.classList.toggle(K,this.cartUpdateFailed||this.showCannotAddMoreInCart),this.showCannotAddMoreInCart=!1,this.cartUpdateFailed=!1)}cartErrorsHandler(t){return t.ok?t:t.json().then((function(e){throw new B({status:t.statusText,headers:t.headers,json:e})}))}hideAddToCartErrorMessage(){const t=this.button.closest(et.upsellHolder)?this.button.closest(et.upsellHolder):this.button.closest(et.productForm),e=null==t?void 0:t.querySelector(et.formErrorsContainer);null==e||e.classList.remove(Y)}addToCartError(t,e){var s;if(this.showCannotAddMoreInCart)return;if(null!==e){const s=e.closest(et.outerSection)||e.closest(et.quickAddHolder)||e.closest(et.quickAddModal);let i=null==s?void 0:s.querySelector(et.formErrorsContainer);const o=e.closest(et.quickAddHolder);if(o&&o.querySelector(et.formErrorsContainer)&&(i=o.querySelector(et.formErrorsContainer)),i){let e=`${t.message}: ${t.description}`;t.message==t.description&&(e=t.message),i.innerHTML=`<div class="errors">${e}<button type="button" class="errors__close" data-close-error><svg aria-hidden="true" focusable="false" role="presentation" width="24px" height="24px" stroke-width="1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="currentColor" class="icon icon-cancel"><path d="M6.758 17.243L12.001 12m5.243-5.243L12 12m0 0L6.758 6.757M12.001 12l5.243 5.243" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"></path></svg></button></div>`,i.classList.add(Y),this.formErrorsEvents(i)}e.dispatchEvent(new CustomEvent("theme:product:add-error",{detail:{response:t,button:e},bubbles:!0}))}const i=null==e?void 0:e.closest(et.quickAddHolder);i&&i.dispatchEvent(new CustomEvent("theme:cart:error",{bubbles:!0,detail:{message:t.message,description:t.description,holder:i}})),null===(s=this.cart)||void 0===s||s.classList.remove(z)}productAddCallback(t){let e=[],s=null;const i="theme:product:add-error"==t.type,o=t.detail.button,r=document.querySelector(et.cartBarAdd);e.push(o),s=o.closest(et.quickAddHolder),r&&e.push(r),e.forEach((t=>{t.classList.remove(z),i||t.classList.add(N)})),setTimeout((()=>{e.forEach((t=>{var e,s;t.classList.remove(N);(null===(e=t.closest(et.formWrapper))||void 0===e?void 0:e.classList.contains(Z))||(null===(s=t.closest(et.formWrapper))||void 0===s?void 0:s.classList.contains(tt))||(t.disabled=!1)})),null==s||s.classList.remove(Y)}),1e3)}toggleClassesOnContainers(){const t=this.hasItemsInCart();this.cart.classList.toggle(U,!t),!t&&this.cartDrawer&&setTimeout((()=>{this.a11y.trapFocus(this.cartDrawer,{elementToFocus:this.cartDrawer.querySelector(et.cartDrawerClose)})}),100)}build(t){var e;const s=t.querySelector(et.apiLineItems),i=t.querySelector(et.apiUpsellItems),o=Boolean(null===s&&null===i),r=t.querySelector(et.apiCartPrice),n=t.querySelector(et.cartTotal);this.priceHolder&&r&&(this.priceHolder.innerHTML=r.innerHTML),o?(this.itemsHolder.innerHTML=t.innerHTML,this.upsellProductsHolder&&(this.upsellProductsHolder.innerHTML="")):(this.itemsHolder.innerHTML=s.innerHTML,this.upsellProductsHolder&&(this.upsellProductsHolder.innerHTML=i.innerHTML),this.skipUpsellProductEvent(),this.checkSkippedUpsellProductsFromStorage(),this.toggleCartUpsellWidgetVisibility()),this.newTotalItems=s&&s.querySelectorAll(et.item).length?s.querySelectorAll(et.item).length:0,this.subtotal=n&&n.hasAttribute(st)?parseInt(n.getAttribute(st)):0,this.cartCount=this.getCartItemCount(),document.dispatchEvent(new CustomEvent("theme:cart:change",{bubbles:!0,detail:{cartCount:this.cartCount}})),this.cartTotal.innerHTML=0===this.subtotal?window.theme.strings.free:window.theme.formatMoney(this.subtotal,theme.moneyWithCurrencyFormat),this.totalItems!==this.newTotalItems&&(this.totalItems=this.newTotalItems,this.toggleClassesOnContainers()),(null===(e=this.cartDrawer)||void 0===e?void 0:e.classList.contains(J))&&this.cart.classList.add(G),this.cart.classList.remove(z),this.hasItemsInCart()||this.cartEmpty.querySelectorAll(et.animation).forEach((t=>{t.classList.remove(W)})),this.freeShippingMessageHandle(this.subtotal),this.cartRemoveEvents(),this.cartUpdateEvents(),this.toggleErrorMessage(),this.enableCartButtons(),this.updateProgress(),this.animateItems(),document.dispatchEvent(new CustomEvent("theme:product:added",{bubbles:!0}))}getCartItemCount(){return Array.from(this.cart.querySelectorAll(et.qtyInput)).reduce(((t,e)=>t+parseInt(e.value)),0)}hasItemsInCart(){return this.totalItems>0}freeShippingMessageHandle(t){this.freeShipping.length&&this.freeShipping.forEach((e=>{const s=e.hasAttribute(ot)&&"true"===e.getAttribute(ot)&&t>=0;e.classList.toggle(Q,s&&t>=this.freeShippingLimit)}))}updateProgress(){if(this.freeShipping=document.querySelectorAll(et.freeShipping),!this.freeShipping.length)return;const t=isNaN(this.subtotal/this.freeShippingLimit)?100:this.subtotal/this.freeShippingLimit,e=Math.min(100*t,100),s=this.circumference-e/100*this.circumference/2,i=window.theme.formatMoney(this.freeShippingLimit-this.subtotal,theme.moneyFormat);this.freeShipping.forEach((t=>{const o=t.querySelector(et.freeShippingProgress),r=t.querySelector(et.freeShippingGraph),n=t.querySelector(et.leftToSpend);n&&(n.innerHTML=i.replace(".00","")),o&&(o.value=e),r&&r.style.setProperty("--stroke-dashoffset",`${s}`)}))}skipUpsellProductEvent(){if(null===this.upsellProductsHolder)return;const t=this.upsellProductsHolder.querySelectorAll(et.buttonSkipUpsellProduct);t.length&&t.forEach((t=>{t.addEventListener("click",(e=>{e.preventDefault();const s=t.closest(et.quickAddHolder).getAttribute(dt);this.skipUpsellProductsArray.includes(s)||this.skipUpsellProductsArray.push(s),window.sessionStorage.setItem("skip_upsell_products",this.skipUpsellProductsArray),this.removeUpsellProduct(s),this.toggleCartUpsellWidgetVisibility()}))}))}checkSkippedUpsellProductsFromStorage(){const t=window.sessionStorage.getItem("skip_upsell_products");if(!t)return;t.split(",").forEach((t=>{this.skipUpsellProductsArray.includes(t)||this.skipUpsellProductsArray.push(t),this.removeUpsellProduct(t)}))}removeUpsellProduct(t){if(!this.upsellProductsHolder)return;const e=this.upsellProductsHolder.querySelector(`[${dt}="${t}"]`);e&&e.parentNode.remove()}toggleCartUpsellWidgetVisibility(){if(!this.upsellProductsHolder)return;const t=this.upsellProductsHolder.querySelectorAll(et.quickAddHolder),e=this.upsellProductsHolder.closest(et.upsellWidget);if(e&&(e.classList.toggle(V,!t.length),t.length&&!e.hasAttribute(ct)&&e.hasAttribute(ut))){e.setAttribute(ct,!0);const t=e.querySelector(et.collapsibleBody);t&&(t.style.height="auto")}}resetAnimatedItems(){this.cart.querySelectorAll(et.animation).forEach((t=>{t.classList.remove(W),t.classList.remove(j)}))}animateItems(t){requestAnimationFrame((()=>{let e=this.cart;t&&t.detail&&t.detail.target&&(e=t.detail.target),null==e||e.querySelectorAll(et.animation).forEach((t=>{t.classList.add(W)}))}))}constructor(){super(),this.a11y=window.theme.a11y}};customElements.get("cart-items")||customElements.define("cart-items",gt);const bt="data-cart-count",wt="data-limit";let ft=class extends HTMLElement{connectedCallback(){document.addEventListener("theme:cart:change",this.onCartChangeCallback)}disconnectedCallback(){document.addEventListener("theme:cart:change",this.onCartChangeCallback)}onCartChange(t){this.cartCount=t.detail.cartCount,this.update()}update(){if(null!==this.cartCount){this.setAttribute(bt,this.cartCount);let t=this.cartCount;this.limit&&this.cartCount>=this.limit&&(t="9+"),this.innerText=t}}constructor(){super(),this.cartCount=null,this.limit=this.getAttribute(wt),this.onCartChangeCallback=this.onCartChange.bind(this)}};customElements.get("cart-count")||customElements.define("cart-count",ft);const yt="is-open",Lt="is-closing",Et="drawer--duplicate",St="drawer-editor-error",At={cartDrawer:"cart-drawer",cartDrawerClose:"[data-cart-drawer-close]",cartDrawerSection:'[data-section-type="cart-drawer"]',cartDrawerInner:"[data-cart-drawer-inner]",shopifySection:".shopify-section"},kt="data-drawer-underlay";let Ct=class extends HTMLElement{connectedCallback(){const t=this.closest(At.shopifySection);if(window.theme.hasCartDrawer){if(!window.Shopify.designMode)return void t.remove();{const t=document.createElement("div");t.classList.add(St),t.innerText="Cart drawer section already exists.",this.querySelector(`.${St}`)||this.querySelector(At.cartDrawerInner).append(t),this.classList.add(Et)}}window.theme.hasCartDrawer=!0,this.addEventListener("theme:cart-drawer:show",this.openCartDrawer),document.addEventListener("theme:cart:toggle",this.toggleCartDrawer),document.addEventListener("theme:quick-add:open",this.closeCartDrawer),document.addEventListener("theme:product:added",this.openCartDrawerOnProductAdded),document.addEventListener("shopify:block:select",this.openCartDrawerOnSelect),document.addEventListener("shopify:section:select",this.openCartDrawerOnSelect),document.addEventListener("shopify:section:deselect",this.closeCartDrawerOnDeselect)}disconnectedCallback(){document.removeEventListener("theme:product:added",this.openCartDrawerOnProductAdded),document.removeEventListener("theme:cart:toggle",this.toggleCartDrawer),document.removeEventListener("theme:quick-add:open",this.closeCartDrawer),document.removeEventListener("shopify:block:select",this.openCartDrawerOnSelect),document.removeEventListener("shopify:section:select",this.openCartDrawerOnSelect),document.removeEventListener("shopify:section:deselect",this.closeCartDrawerOnDeselect),document.querySelectorAll(At.cartDrawer).length<=1&&(window.theme.hasCartDrawer=!1),l()}openCartDrawerOnProductAdded(){this.cartDrawerIsOpen||this.openCartDrawer()}openCartDrawerOnSelect(t){(t.target.querySelector(At.shopifySection)||t.target.closest(At.shopifySection)||t.target)===this.cartDrawerSection&&this.openCartDrawer(!0)}closeCartDrawerOnDeselect(){this.cartDrawerIsOpen&&this.closeCartDrawer()}openCartDrawer(t=!1){!t&&this.classList.contains(Et)||(this.cartDrawerIsOpen=!0,this.onBodyClickEvent=this.onBodyClickEvent||this.onBodyClick.bind(this),document.body.addEventListener("click",this.onBodyClickEvent),document.dispatchEvent(new CustomEvent("theme:cart-drawer:open",{detail:{target:this},bubbles:!0})),document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),this.classList.add(yt),this.observeAdditionalCheckoutButtons(),window.theme.waitForAnimationEnd(this.cartDrawerInner).then((()=>{this.a11y.trapFocus(this,{elementToFocus:this.querySelector(At.cartDrawerClose)})})))}closeCartDrawer(){this.classList.contains(yt)&&(this.classList.add(Lt),this.classList.remove(yt),this.cartDrawerIsOpen=!1,document.dispatchEvent(new CustomEvent("theme:cart-drawer:close",{bubbles:!0})),this.a11y.removeTrapFocus(),this.a11y.autoFocusLastElement(),document.body.removeEventListener("click",this.onBodyClickEvent),document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0})),window.theme.waitForAnimationEnd(this.cartDrawerInner).then((()=>{this.classList.remove(Lt)})))}toggleCartDrawer(){this.cartDrawerIsOpen?this.closeCartDrawer():this.openCartDrawer()}closeCartEvents(){this.cartDrawerClose.addEventListener("click",(t=>{t.preventDefault(),this.closeCartDrawer()})),this.addEventListener("keyup",(t=>{"Escape"===t.code&&this.closeCartDrawer()}))}onBodyClick(t){t.target.hasAttribute(kt)&&this.closeCartDrawer()}observeAdditionalCheckoutButtons(){const t=this.querySelector(At.additionalCheckoutButtons);if(t){const e=new MutationObserver((()=>{this.a11y.trapFocus(this,{elementToFocus:this.querySelector(At.cartDrawerClose)}),e.disconnect()}));e.observe(t,{subtree:!0,childList:!0})}}constructor(){super(),this.cartDrawerIsOpen=!1,this.cartDrawerClose=this.querySelector(At.cartDrawerClose),this.cartDrawerInner=this.querySelector(At.cartDrawerInner),this.openCartDrawer=this.openCartDrawer.bind(this),this.closeCartDrawer=this.closeCartDrawer.bind(this),this.toggleCartDrawer=this.toggleCartDrawer.bind(this),this.openCartDrawerOnProductAdded=this.openCartDrawerOnProductAdded.bind(this),this.openCartDrawerOnSelect=this.openCartDrawerOnSelect.bind(this),this.closeCartDrawerOnDeselect=this.closeCartDrawerOnDeselect.bind(this),this.cartDrawerSection=this.closest(At.shopifySection),this.a11y=window.theme.a11y,this.closeCartEvents()}};customElements.get("cart-drawer")||customElements.define("cart-drawer",Ct);const qt="[data-collapsible]",Tt="[data-collapsible-trigger]",Mt="[data-collapsible-body]",xt="[data-collapsible-content]",It="desktop",Ht="disabled",Ot="mobile",Pt="open",Dt="single";let Ft=class extends HTMLElement{connectedCallback(){this.toggle(),document.addEventListener("theme:resize:width",this.toggle),this.collapsibles.forEach((t=>{const e=t.querySelector(Tt),s=t.querySelector(Mt);null==e||e.addEventListener("click",(t=>this.onCollapsibleClick(t))),null==s||s.addEventListener("transitionend",(e=>{e.target===s&&("true"==t.getAttribute(Pt)&&this.setBodyHeight(s,"auto"),"false"==t.getAttribute(Pt)&&(t.removeAttribute(Pt),this.setBodyHeight(s,"")))}))}))}disconnectedCallback(){document.removeEventListener("theme:resize:width",this.toggle)}toggle(){const t=!window.theme.isMobile();this.collapsibles.forEach((e=>{if(!e.hasAttribute(It)&&!e.hasAttribute(Ot))return;const s=e.hasAttribute(It)?e.getAttribute(It):"true",i=e.hasAttribute(Ot)?e.getAttribute(Ot):"true",o=t&&"true"==s||!t&&"true"==i,r=e.querySelector(Mt);o?(e.removeAttribute(Ht),e.querySelector(Tt).removeAttribute("tabindex"),e.removeAttribute(Pt),this.setBodyHeight(r,"")):(e.setAttribute(Ht,""),e.setAttribute("open",!0),e.querySelector(Tt).setAttribute("tabindex",-1))}))}open(t){if("true"==t.getAttribute("open"))return;const e=t.querySelector(Mt),s=t.querySelector(xt);t.setAttribute("open",!0),this.setBodyHeight(e,s.offsetHeight)}close(t){if(!t.hasAttribute("open"))return;const e=t.querySelector(Mt),s=t.querySelector(xt);this.setBodyHeight(e,s.offsetHeight),t.setAttribute("open",!1),setTimeout((()=>{requestAnimationFrame((()=>{this.setBodyHeight(e,0)}))}))}setBodyHeight(t,e){t.style.height="auto"!==e&&""!==e?`${e}px`:e}onCollapsibleClick(t){t.preventDefault();const e=t.target.closest(qt);this.single&&this.collapsibles.forEach((t=>{t.hasAttribute(Pt)&&t!=e&&requestAnimationFrame((()=>{this.close(t)}))})),e.hasAttribute(Pt)?this.close(e):this.open(e),e.dispatchEvent(new CustomEvent("theme:form:sticky",{bubbles:!0,detail:{element:"accordion"}})),e.dispatchEvent(new CustomEvent("theme:collapsible:toggle",{bubbles:!0}))}constructor(){super(),this.collapsibles=this.querySelectorAll(qt),this.single=this.hasAttribute(Dt),this.toggle=this.toggle.bind(this)}};customElements.get("collapsible-elements")||customElements.define("collapsible-elements",Ft);const _t="[data-deferred-media-button]",$t="video, model-viewer, iframe",Bt='[data-host="youtube"]',Wt='[data-host="vimeo"]',Nt="template",Rt="video",Ut="product-model",Vt="loaded",jt="autoplay";let zt=class extends HTMLElement{loadContent(t=!0){if(this.pauseAllMedia(),!this.getAttribute(Vt)){const e=document.createElement("div"),s=this.querySelector(Nt).content.firstElementChild.cloneNode(!0);e.appendChild(s),this.setAttribute(Vt,!0);const i=this.appendChild(e.querySelector($t));t&&i.focus(),"VIDEO"==i.nodeName&&i.getAttribute(jt)&&i.play()}}pauseAllMedia(){document.querySelectorAll(Bt).forEach((t=>{t.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}',"*")})),document.querySelectorAll(Wt).forEach((t=>{t.contentWindow.postMessage('{"method":"pause"}',"*")})),document.querySelectorAll(Rt).forEach((t=>t.pause())),document.querySelectorAll(Ut).forEach((t=>{t.modelViewerUI&&t.modelViewerUI.pause()}))}constructor(){super();const t=this.querySelector(_t);null==t||t.addEventListener("click",this.loadContent.bind(this))}};customElements.get("deferred-media")||customElements.define("deferred-media",zt),window.theme.DeferredMedia=window.theme.DeferredMedia||zt;const Jt="is-visible";class Xt{init(){var t;const e={root:this.container,threshold:[.01,.5,.75,.99]};this.observer=new IntersectionObserver((t=>{t.forEach((t=>{t.intersectionRatio>=.99?t.target.classList.add(Jt):t.target.classList.remove(Jt)}))}),e),null===(t=this.container.querySelectorAll(this.itemSelector))||void 0===t||t.forEach((t=>{this.observer.observe(t)}))}destroy(){this.observer.disconnect()}constructor(t,e){t&&e&&(this.observer=null,this.container=t,this.itemSelector=e,this.init())}}const Qt="is-dragging",Yt="is-enabled",Kt="is-scrolling",Gt="is-visible",Zt="[data-grid-item]";class te{handleMouseDown(t){t.preventDefault(),this.isDown=!0,this.startX=t.pageX-this.slider.offsetLeft,this.scrollLeft=this.slider.scrollLeft,this.cancelMomentumTracking()}handleMouseLeave(){this.isDown&&(this.isDown=!1,this.beginMomentumTracking())}handleMouseUp(){this.isDown=!1,this.beginMomentumTracking()}handleMouseMove(t){if(!this.isDown)return;t.preventDefault();const e=1*(t.pageX-this.slider.offsetLeft-this.startX),s=this.slider.scrollLeft,i=e>0?1:-1;this.slider.classList.add(Qt,Kt),this.slider.scrollLeft=this.scrollLeft-e,this.slider.scrollLeft!==s&&(this.velX=this.slider.scrollLeft-s||i)}handleMouseWheel(){this.cancelMomentumTracking(),this.slider.classList.remove(Kt)}beginMomentumTracking(){this.isScrolling=!1,this.slider.classList.remove(Qt),this.cancelMomentumTracking(),this.scrollToSlide()}cancelMomentumTracking(){cancelAnimationFrame(this.scrollAnimation)}scrollToSlide(){if(!this.velX&&!this.isScrolling)return;const t=this.slider.querySelector(`${Zt}.${Gt}`);if(!t)return;const e=parseInt(window.getComputedStyle(t).marginRight)||0,s=t.offsetWidth+e,i=t.offsetLeft,o=this.velX>0?1:-1,r=Math.floor(Math.abs(this.velX)/100)||1;this.startPosition=this.slider.scrollLeft,this.distance=i-this.startPosition,this.startTime=performance.now(),this.isScrolling=!0,o<0&&this.velX<s&&(this.distance-=s*r),o>0&&this.velX<s&&(this.distance+=s*r),this.scrollAnimation=requestAnimationFrame(this.scrollStep)}scrollStep(){const t=performance.now()-this.startTime,e=parseFloat(this.easeOutCubic(Math.min(t,this.duration))).toFixed(1);this.slider.scrollLeft=e,t<this.duration?this.scrollAnimation=requestAnimationFrame(this.scrollStep):(this.slider.classList.remove(Kt),this.velX=0,this.isScrolling=!1)}easeOutCubic(t){return t/=this.duration,t--,this.distance*(t*t*t+1)+this.startPosition}destroy(){this.slider.classList.remove(Yt),this.slider.removeEventListener("mousedown",this.handleMouseDown),this.slider.removeEventListener("mouseleave",this.handleMouseLeave),this.slider.removeEventListener("mouseup",this.handleMouseUp),this.slider.removeEventListener("mousemove",this.handleMouseMove),this.slider.removeEventListener("wheel",this.handleMouseWheel)}constructor(t){this.slider=t,this.isDown=!1,this.startX=0,this.scrollLeft=0,this.velX=0,this.scrollAnimation=null,this.isScrolling=!1,this.duration=800,this.scrollStep=this.scrollStep.bind(this),this.scrollToSlide=this.scrollToSlide.bind(this),this.handleMouseDown=this.handleMouseDown.bind(this),this.handleMouseLeave=this.handleMouseLeave.bind(this),this.handleMouseUp=this.handleMouseUp.bind(this),this.handleMouseMove=this.handleMouseMove.bind(this),this.handleMouseWheel=this.handleMouseWheel.bind(this),this.slider.addEventListener("mousedown",this.handleMouseDown),this.slider.addEventListener("mouseleave",this.handleMouseLeave),this.slider.addEventListener("mouseup",this.handleMouseUp),this.slider.addEventListener("mousemove",this.handleMouseMove),this.slider.addEventListener("wheel",this.handleMouseWheel,{passive:!0}),this.slider.classList.add(Yt)}}const ee="[data-button-arrow]",se="[data-collection-image]",ie="[data-column-image]",oe="[data-product-image]",re="[data-grid-item]",ne="[data-grid-slider]",ae="data-button-prev",le="data-button-next",ce="align-arrows",de="images-widths-different",he="slider__arrows",ue="is-visible",pe="scroll-snap-disabled";customElements.get("grid-slider")||customElements.define("grid-slider",class extends HTMLElement{connectedCallback(){this.init(),this.addEventListener("theme:grid-slider:init",this.init)}init(){this.slider=this.querySelector(ne),this.slides=this.querySelectorAll(re),this.buttons=this.querySelectorAll(ee),this.slider.classList.add(pe),this.toggleSlider(),document.addEventListener("theme:resize:width",this.toggleSlider),window.theme.waitForAllAnimationsEnd(this).then((()=>{this.slider.classList.remove(pe)})).catch((()=>{this.slider.classList.remove(pe)}))}toggleSlider(){if(!(this.slider.clientWidth<this.getSlidesWidth())||window.theme.isMobile()&&window.theme.touch)this.destroy();else{if(this.isInitialized)return;this.slidesObserver=new Xt(this.slider,re),this.initArrows(),this.isInitialized=!0,this.draggableSlider=new te(this.slider)}}initArrows(){if(!this.buttons.length){const t=document.createElement("div");t.classList.add(he),t.innerHTML=theme.sliderArrows.prev+theme.sliderArrows.next,this.append(t),this.buttons=this.querySelectorAll(ee),this.buttonPrev=this.querySelector(`[${ae}]`),this.buttonNext=this.querySelector(`[${le}]`)}this.toggleArrowsObserver(),this.hasAttribute(ce)&&(this.positionArrows(),this.arrowsResizeObserver()),this.buttons.forEach((t=>{t.addEventListener("click",this.onButtonArrowClick)}))}buttonArrowClickEvent(t){t.preventDefault();const e=this.slider.querySelector(`${re}.${ue}`);let s=null;t.target.hasAttribute(ae)&&(s=null==e?void 0:e.previousElementSibling),t.target.hasAttribute(le)&&(s=null==e?void 0:e.nextElementSibling),this.goToSlide(s)}removeArrows(){var t;null===(t=this.querySelector(`.${he}`))||void 0===t||t.remove()}goToSlide(t){t&&this.slider.scrollTo({top:0,left:t.offsetLeft,behavior:"smooth"})}getSlidesWidth(){var t;return(null===(t=this.slider.querySelector(re))||void 0===t?void 0:t.clientWidth)*this.slider.querySelectorAll(re).length}toggleArrowsObserver(){if(this.buttonPrev&&this.buttonNext){const t=this.slides.length,e=this.slides[0],s=this.slides[t-1],i={attributes:!0,childList:!1,subtree:!1},o=t=>{for(const i of t)if("attributes"===i.type){const t=i.target,o=Boolean(t.classList.contains(ue));t==e&&(this.buttonPrev.disabled=o),t==s&&(this.buttonNext.disabled=o)}};e&&s&&(this.firstLastSlidesObserver=new MutationObserver(o),this.firstLastSlidesObserver.observe(e,i),this.firstLastSlidesObserver.observe(s,i))}}positionArrows(){if(this.hasAttribute(de)){const t=this.slider.querySelectorAll("figure"),e=Math.max(0,...Array.from(t).map((t=>t.clientHeight)));return void this.style.setProperty("--button-position",e/2+"px")}const t=this.slider.querySelector(oe)||this.slider.querySelector(se)||this.slider.querySelector(ie)||this.slider;t&&this.style.setProperty("--button-position",t.clientHeight/2+"px")}arrowsResizeObserver(){document.addEventListener("theme:resize:width",this.positionArrows)}disconnectedCallback(){this.destroy(),document.removeEventListener("theme:resize:width",this.toggleSlider)}destroy(){var t,e;this.isInitialized=!1,null===(t=this.draggableSlider)||void 0===t||t.destroy(),this.draggableSlider=null,null===(e=this.slidesObserver)||void 0===e||e.destroy(),this.slidesObserver=null,this.removeArrows(),document.removeEventListener("theme:resize:width",this.positionArrows)}constructor(){super(),this.isInitialized=!1,this.draggableSlider=null,this.positionArrows=this.positionArrows.bind(this),this.onButtonArrowClick=t=>this.buttonArrowClickEvent(t),this.slidesObserver=null,this.firstLastSlidesObserver=null,this.isDragging=!1,this.toggleSlider=this.toggleSlider.bind(this)}}),window.theme.hasOpenModals=function(){const t=Boolean(document.querySelectorAll("dialog[open][data-scroll-lock-required]").length),e=Boolean(document.querySelectorAll(".drawer.is-open").length);return t||e};const me="cart-drawer",ve="[data-cart-toggle]",ge='.navlink[href="#"]',be="[data-header-desktop]",we=".main-content > .shopify-section.section-overlay-header:first-of-type",fe=".page-header",ye="[data-prevent-transparent-header]",Le="[data-child-takes-space]",Ee="[data-takes-space-wrapper]",Se="js__header__clone",Ae="has-first-section-overlay-header",ke="shopify-section-group-header-group",Ce="js__show__mobile",qe="has-header-sticky",Te="js__header__stuck",Me="has-header-transparent",xe="header-wrapper",Ie="data-drawer",He="data-drawer-toggle",Oe="data-scroll-locked",Pe="data-header-sticky",De="data-header-transparent";customElements.get("header-component")||customElements.define("header-component",class extends HTMLElement{connectedCallback(){this.killDeadLinks(),this.drawerToggleEvent(),this.cartToggleEvent(),this.initSticky(),"drawer"!==this.style&&this.desktop&&(this.minWidth=this.getMinWidth(),this.listenWidth())}listenWidth(){"ResizeObserver"in window?(this.resizeObserver=new ResizeObserver(this.checkWidth),this.resizeObserver.observe(this)):document.addEventListener("theme:resize",this.checkWidth)}drawerToggleEvent(){var t;null===(t=this.querySelectorAll(`[${He}]`))||void 0===t||t.forEach((t=>{t.addEventListener("click",(()=>{let e;const s=t.hasAttribute(He)?t.getAttribute(He):"",i=document.querySelector(`[${Ie}="${s}"]`),o=document.querySelector(`mobile-menu > [${Ie}]`);e=!window.theme.isMobile()?i:"new"===theme.settings.mobileMenuType&&o||i,e.dispatchEvent(new CustomEvent("theme:drawer:toggle",{bubbles:!1,detail:{button:t}}))}))}))}killDeadLinks(){this.deadLinks.forEach((t=>{t.onclick=t=>{t.preventDefault()}}))}checkWidth(){if(document.body.clientWidth<this.minWidth){this.classList.add(Ce);const{headerHeight:t}=window.theme.readHeights();document.documentElement.style.setProperty("--header-height",`${t}px`)}else this.classList.remove(Ce)}getMinWidth(){const t=document.createElement("div");t.classList.add(Se,xe),t.appendChild(this.querySelector("header").cloneNode(!0)),document.body.appendChild(t);const e=t.querySelectorAll(Ee);let s=0,i=0;return e.forEach((t=>{const e=t.querySelectorAll(Le);let o=0;o=3===e.length?this._sumSplitWidths(e):this._sumWidths(e),o>s&&(s=o,i=20*e.length)})),document.body.removeChild(t),s+i}cartToggleEvent(){var t;"drawer"===theme.settings.cartType&&(null===(t=this.querySelectorAll(ve))||void 0===t||t.forEach((t=>{t.addEventListener("click",(e=>{const s=document.querySelector(me);s&&(e.preventDefault(),s.dispatchEvent(new CustomEvent("theme:cart-drawer:show")),window.a11y.lastElement=t)}))})))}toggleButtonClick(t){t.preventDefault(),document.dispatchEvent(new CustomEvent("theme:cart:toggle",{bubbles:!0}))}initSticky(){var t;this.isSticky&&(this.isStuck=!1,this.cls=this.classList,this.headerOffset=null===(t=document.querySelector(fe))||void 0===t?void 0:t.offsetTop,this.updateHeaderOffset=this.updateHeaderOffset.bind(this),this.scrollEvent=t=>this.onScroll(t),this.listen(),this.stickOnLoad())}listen(){document.addEventListener("theme:scroll",this.scrollEvent),document.addEventListener("shopify:section:load",this.updateHeaderOffset),document.addEventListener("shopify:section:unload",this.updateHeaderOffset)}onScroll(t){t.detail.down?!this.isStuck&&t.detail.position>this.headerOffset&&this.stickSimple():t.detail.position<=this.headerOffset&&this.unstickSimple()}updateHeaderOffset(t){t.target.classList.contains(ke)&&setTimeout((()=>{var t;this.headerOffset=null===(t=document.querySelector(fe))||void 0===t?void 0:t.offsetTop}))}stickOnLoad(){window.scrollY>this.headerOffset&&this.stickSimple()}stickSimple(){this.cls.add(Te),this.isStuck=!0}unstickSimple(){document.documentElement.hasAttribute(Oe)||(this.cls.remove(Te),this.isStuck=!1)}_sumSplitWidths(t){let e=[];t.forEach((t=>{t.firstElementChild&&e.push(t.firstElementChild.clientWidth)})),e[0]>e[2]?e[2]=e[0]:e[0]=e[2];return e.reduce(((t,e)=>t+e))}_sumWidths(t){let e=0;return t.forEach((t=>{e+=t.clientWidth})),e}disconnectedCallback(){var t;"ResizeObserver"in window?null===(t=this.resizeObserver)||void 0===t||t.unobserve(this):document.removeEventListener("theme:resize",this.checkWidth);this.isSticky&&(document.removeEventListener("theme:scroll",this.scrollEvent),document.removeEventListener("shopify:section:load",this.updateHeaderOffset),document.removeEventListener("shopify:section:unload",this.updateHeaderOffset))}constructor(){super(),this.style=this.dataset.style,this.desktop=this.querySelector(be),this.deadLinks=document.querySelectorAll(ge),this.resizeObserver=null,this.checkWidth=this.checkWidth.bind(this),this.isSticky=this.hasAttribute(Pe),document.body.classList.toggle(qe,this.isSticky);let t=!1;const e=document.querySelector(we);e&&!e.querySelector(ye)&&(t=!0),document.body.classList.toggle(Me,this.hasAttribute(De)),document.body.classList.toggle(Ae,t)}});const Fe="[data-top-link]",_e="[data-header-wrapper]",$e="[data-stagger]",Be="[data-stagger-first]",We="[data-stagger-second]",Ne="is-visible",Re="meganav--visible",Ue="meganav--is-transitioning";customElements.get("hover-disclosure")||customElements.define("hover-disclosure",class extends HTMLElement{connectedCallback(){this.setAttribute("aria-haspopup",!0),this.setAttribute("aria-expanded",!1),this.setAttribute("aria-controls",this.key),this.connectHoverToggle(),this.handleTablets(),this.staggerChildAnimations(),this.addEventListener("theme:disclosure:show",(t=>{this.showDisclosure(t)})),this.addEventListener("theme:disclosure:hide",(t=>{this.hideDisclosure(t)}))}showDisclosure(t){t&&t.type&&"mouseenter"===t.type&&this.wrapper.classList.add(Ue),this.grandparent?this.wrapper.classList.add(Re):this.wrapper.classList.remove(Re),this.setAttribute("aria-expanded",!0),this.classList.add(Ne),this.disclosure.classList.add(Ne),this.transitionTimeout&&clearTimeout(this.transitionTimeout),this.transitionTimeout=setTimeout((()=>{this.wrapper.classList.remove(Ue)}),200)}hideDisclosure(){this.classList.remove(Ne),this.disclosure.classList.remove(Ne),this.setAttribute("aria-expanded",!1),this.wrapper.classList.remove(Re,Ue)}staggerChildAnimations(){const t=this.querySelectorAll($e);let e=50;t.forEach(((t,s)=>{t.style.transitionDelay=s*e+10+"ms",e*=.95}));this.querySelectorAll(Be).forEach(((t,e)=>{const s=100*e;t.style.transitionDelay=`${s}ms`,t.parentElement.querySelectorAll(We).forEach(((t,e)=>{const i=20*(e+1);t.style.transitionDelay=`${s+i}ms`}))}))}handleTablets(){this.addEventListener("touchstart",function(t){this.classList.contains(Ne)||(t.preventDefault(),this.showDisclosure(t))}.bind(this),{passive:!0})}connectHoverToggle(){this.addEventListener("mouseenter",(t=>this.showDisclosure(t))),this.link.addEventListener("focus",(t=>this.showDisclosure(t))),this.addEventListener("mouseleave",(()=>this.hideDisclosure())),this.addEventListener("focusout",(t=>{this.contains(t.relatedTarget)||this.hideDisclosure()})),this.addEventListener("keyup",(t=>{"Escape"===t.code&&this.hideDisclosure()}))}constructor(){super(),this.wrapper=this.closest(_e),this.key=this.getAttribute("aria-controls"),this.link=this.querySelector(Fe),this.grandparent=this.classList.contains("grandparent"),this.disclosure=document.getElementById(this.key),this.transitionTimeout=0}});const Ve="[data-drawer-inner]",je="[data-drawer-close]",ze="[data-drawer-underlay]",Je="[data-stagger-animation]",Xe='button, [href], select, textarea, [tabindex]:not([tabindex="-1"])',Qe="drawer--animated",Ye="is-open",Ke="is-closing",Ge="is-focused";customElements.get("header-drawer")||customElements.define("header-drawer",class extends HTMLElement{connectDrawer(){this.addEventListener("theme:drawer:toggle",(t=>{var e;this.triggerButton=null===(e=t.detail)||void 0===e?void 0:e.button,this.classList.contains(Ye)?this.dispatchEvent(new CustomEvent("theme:drawer:close",{bubbles:!0})):this.dispatchEvent(new CustomEvent("theme:drawer:open",{bubbles:!0}))})),this.addEventListener("theme:drawer:close",this.hideDrawer),this.addEventListener("theme:drawer:open",this.showDrawer),document.addEventListener("theme:cart-drawer:open",this.hideDrawer)}closers(){var t;null===(t=this.querySelectorAll(je))||void 0===t||t.forEach((t=>{t.addEventListener("click",(()=>{this.hideDrawer()}))})),document.addEventListener("keyup",(t=>{"Escape"===t.code&&this.hideDrawer()})),this.underlay.addEventListener("click",(()=>{this.hideDrawer()}))}showDrawer(){var t;this.isAnimating||(this.isAnimating=!0,null===(t=this.triggerButton)||void 0===t||t.setAttribute("aria-expanded",!0),this.classList.add(Ye,Qe),document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),this.drawerInner&&(this.a11y.removeTrapFocus(),window.theme.waitForAnimationEnd(this.drawerInner).then((()=>{this.isAnimating=!1,this.a11y.trapFocus(this.drawerInner,{elementToFocus:this.querySelector(Xe)})}))))}hideDrawer(){!this.isAnimating&&this.classList.contains(Ye)&&(this.isAnimating=!0,this.classList.add(Ke),this.classList.remove(Ye),this.a11y.removeTrapFocus(),this.triggerButton&&(this.triggerButton.setAttribute("aria-expanded",!1),document.body.classList.contains(Ge)&&this.triggerButton.focus()),document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0})),window.theme.waitForAnimationEnd(this.drawerInner).then((()=>{this.classList.remove(Ke,Qe),this.isAnimating=!1,document.dispatchEvent(new CustomEvent("theme:sliderule:close",{bubbles:!1}))})))}disconnectedCallback(){document.removeEventListener("theme:cart-drawer:open",this.hideDrawer)}constructor(){super(),this.a11y=window.theme.a11y,this.isAnimating=!1,this.drawer=this,this.drawerInner=this.querySelector(Ve),this.underlay=this.querySelector(ze),this.triggerButton=null,this.staggers=this.querySelectorAll(Je),this.showDrawer=this.showDrawer.bind(this),this.hideDrawer=this.hideDrawer.bind(this),this.connectDrawer(),this.closers()}});const Ze={animates:"data-animates",sliderule:"[data-sliderule]",slideruleOpen:"data-sliderule-open",slideruleClose:"data-sliderule-close",sliderulePane:"data-sliderule-pane",drawerContent:"[data-drawer-content]",focusable:'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',children:":scope > [data-animates],\n             :scope > * > [data-animates],\n             :scope > * > * > [data-animates],\n             :scope > * > .sliderule-grid  > *"},ts="is-visible",es="is-hiding",ss="is-hidden",is="is-focused",os="is-scrolling";customElements.get("mobile-sliderule")||customElements.define("mobile-sliderule",class extends HTMLElement{clickEvents(){this.trigger.addEventListener("click",(()=>{this.cachedButton=this.trigger,this.showSliderule()})),this.exit.forEach((t=>{t.addEventListener("click",(()=>{this.hideSliderule()}))}))}keyboardEvents(){this.addEventListener("keyup",(t=>{t.stopPropagation(),"Escape"===t.code&&this.hideSliderule()}))}trapFocusSliderule(t=!0){const e=t?this.querySelector(this.exitSelector):this.cachedButton;this.a11y.removeTrapFocus(),e&&this.drawerContent&&this.a11y.trapFocus(this.drawerContent,{elementToFocus:document.body.classList.contains(is)?e:null})}hideSliderule(t=!1){const e=parseInt(this.pane.dataset.sliderulePane,10)-1;this.pane.setAttribute(Ze.sliderulePane,e),this.pane.classList.add(es),this.sliderule.classList.add(es);const s=t?`[${Ze.animates}].${ss}`:`[${Ze.animates}="${e}"]`,i=this.pane.querySelectorAll(s);i.length&&i.forEach((t=>{t.classList.remove(ss)}));const o=t?this.pane.querySelectorAll(`.${ts}, .${es}`):this.childrenElements;o.forEach(((s,i)=>{const r=o.length-1==i;s.classList.remove(ts),t&&(s.classList.remove(es),this.pane.classList.remove(es));const n=()=>{parseInt(this.pane.getAttribute(Ze.sliderulePane))===e&&this.sliderule.classList.remove(ts),this.sliderule.classList.remove(es),this.pane.classList.remove(es),r&&(this.a11y.removeTrapFocus(),t||this.trapFocusSliderule(!1)),s.removeEventListener("animationend",n)};window.theme.settings.enableAnimations?s.addEventListener("animationend",n):n()}))}showSliderule(){let t=null;const e=this.closest(`.${ts}`);let s=this.pane;e&&(s=e),s.scrollTo({top:0,left:0,behavior:"smooth"}),s.classList.add(os);const i=()=>{s.scrollTop<=0?(s.classList.remove(os),t&&cancelAnimationFrame(t)):t=requestAnimationFrame(i)};t=requestAnimationFrame(i);const o=parseInt(this.pane.dataset.sliderulePane,10),r=o+1;this.sliderule.classList.add(ts),this.pane.setAttribute(Ze.sliderulePane,r);const n=this.pane.querySelectorAll(`[${Ze.animates}="${o}"]`);n.length&&n.forEach(((t,e)=>{const s=n.length-1==e;t.classList.add(es);const i=()=>{t.classList.remove(es),parseInt(this.pane.getAttribute(Ze.sliderulePane))!==o&&t.classList.add(ss),s&&this.trapFocusSliderule(),t.removeEventListener("animationend",i)};window.theme.settings.enableAnimations?t.addEventListener("animationend",i):i()}))}closeSliderule(){this.pane&&this.pane.hasAttribute(Ze.sliderulePane)&&parseInt(this.pane.getAttribute(Ze.sliderulePane))>0&&(this.hideSliderule(!0),parseInt(this.pane.getAttribute(Ze.sliderulePane))>0&&this.pane.setAttribute(Ze.sliderulePane,0))}disconnectedCallback(){document.removeEventListener("theme:sliderule:close",this.closeSliderule)}constructor(){super(),this.key=this.id,this.sliderule=this.querySelector(Ze.sliderule);const t=`[${Ze.slideruleOpen}='${this.key}']`;this.exitSelector=`[${Ze.slideruleClose}='${this.key}']`,this.trigger=this.querySelector(t),this.exit=document.querySelectorAll(this.exitSelector),this.pane=this.trigger.closest(`[${Ze.sliderulePane}]`),this.childrenElements=this.querySelectorAll(Ze.children),this.drawerContent=this.closest(Ze.drawerContent),this.cachedButton=null,this.a11y=window.theme.a11y,this.trigger.setAttribute("aria-haspopup",!0),this.trigger.setAttribute("aria-expanded",!1),this.trigger.setAttribute("aria-controls",this.key),this.closeSliderule=this.closeSliderule.bind(this),this.clickEvents(),this.keyboardEvents(),document.addEventListener("theme:sliderule:close",this.closeSliderule)}});const rs="details",ns="[data-popdown]",as="[data-popdown-close]",ls='input:not([type="hidden"])',cs="mobile-menu",ds="data-popdown-underlay",hs="data-scroll-locked",us="is-open";let ps=class extends HTMLElement{connectedCallback(){this.popdown.addEventListener("transitionend",this.popdownTransitionCallback),this.popdownContainer.addEventListener("keyup",(t=>"ESCAPE"===t.code.toUpperCase()&&this.close())),this.popdownContainer.addEventListener("toggle",this.detailsToggleCallback),this.popdownClose.addEventListener("click",this.close.bind(this))}detailsToggleCallback(t){t.target.hasAttribute("open")&&this.open()}popdownTransitionCallback(t){t.target===this.popdown&&(this.classList.contains(us)?"transform"!==t.propertyName&&"opacity"!==t.propertyName||this.a11y.trapFocus(this.popdown,{elementToFocus:this.popdown.querySelector(ls)}):(this.popdownContainer.removeAttribute("open"),this.a11y.removeTrapFocus()))}onBodyClick(t){this.contains(t.target)&&!t.target.hasAttribute(ds)||this.close()}open(){this.onBodyClickEvent=this.onBodyClickEvent||this.onBodyClick.bind(this),document.body.addEventListener("click",this.onBodyClickEvent),document.documentElement.hasAttribute(hs)||document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),requestAnimationFrame((()=>{this.classList.add(us)}))}close(){this.classList.remove(us),document.body.removeEventListener("click",this.onBodyClickEvent),this.mobileMenu||document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0}))}constructor(){super(),this.popdown=this.querySelector(ns),this.popdownContainer=this.querySelector(rs),this.popdownClose=this.querySelector(as),this.popdownTransitionCallback=this.popdownTransitionCallback.bind(this),this.detailsToggleCallback=this.detailsToggleCallback.bind(this),this.mobileMenu=this.closest(cs),this.a11y=window.theme.a11y}};customElements.get("header-search-popdown")||customElements.define("header-search-popdown",ps);const ms='input[type="search"]',vs='[aria-selected="true"] a',gs='button[type="reset"]',bs="hidden";let ws=class extends HTMLElement{toggleResetButton(){const t=this.resetButton.classList.contains(bs);this.input.value.length>0&&t?this.resetButton.classList.remove(bs):0!==this.input.value.length||t||this.resetButton.classList.add(bs)}onChange(){this.toggleResetButton()}shouldResetForm(){return!document.querySelector(vs)}onFormReset(t){t.preventDefault(),this.shouldResetForm()&&(this.input.value="",this.toggleResetButton(),t.target.querySelector(ms).focus())}constructor(){super(),this.input=this.querySelector(ms),this.resetButton=this.querySelector(gs),this.input&&(this.input.form.addEventListener("reset",this.onFormReset.bind(this)),this.input.addEventListener("input",window.theme.debounce((t=>{this.onChange(t)}),300).bind(this)))}};customElements.define("header-search-form",ws);const fs='input[type="search"]';let ys=class extends ws{setupEventListeners(){let t=[];this.allSearchInputs.forEach((e=>t.push(e.form))),this.input.addEventListener("focus",this.onInputFocus.bind(this)),t.length<2||(t.forEach((t=>t.addEventListener("reset",this.onFormReset.bind(this)))),this.allSearchInputs.forEach((t=>t.addEventListener("input",this.onInput.bind(this)))))}onFormReset(t){super.onFormReset(t),super.shouldResetForm()&&this.keepInSync("",this.input)}onInput(t){const e=t.target;this.keepInSync(e.value,e)}onInputFocus(){window.theme.isMobile()&&this.scrollIntoView({behavior:"smooth"})}keepInSync(t,e){this.allSearchInputs.forEach((s=>{s!==e&&(s.value=t)}))}constructor(){super(),this.allSearchInputs=document.querySelectorAll(fs),this.setupEventListeners()}};customElements.get("main-search")||customElements.define("main-search",ys);const Ls="[data-scrollbar]",Es="[data-scrollbar-arrow-prev]",Ss="[data-scrollbar-arrow-next]",As="is-hidden",ks="data-scrollbar-slider",Cs="data-scrollbar-slide-fullwidth";customElements.get("native-scrollbar")||customElements.define("native-scrollbar",class extends HTMLElement{connectedCallback(){document.addEventListener("theme:resize",this.toggleNextArrow),this.scrollbar.hasAttribute(ks)&&this.scrollToVisibleElement(),this.arrowNext&&this.arrowPrev&&this.events()}disconnectedCallback(){document.removeEventListener("theme:resize",this.toggleNextArrow)}events(){this.arrowNext.addEventListener("click",(t=>{t.preventDefault(),this.goToNext()})),this.arrowPrev.addEventListener("click",(t=>{t.preventDefault(),this.goToPrev()})),this.scrollbar.addEventListener("scroll",(()=>{this.togglePrevArrow(),this.toggleNextArrow()}))}goToNext(){const t=(this.scrollbar.hasAttribute(Cs)?this.scrollbar.getBoundingClientRect().width:this.scrollbar.getBoundingClientRect().width/2)+this.scrollbar.scrollLeft;this.move(t),this.arrowPrev.classList.remove(As),this.toggleNextArrow()}goToPrev(){const t=this.scrollbar.hasAttribute(Cs)?this.scrollbar.getBoundingClientRect().width:this.scrollbar.getBoundingClientRect().width/2,e=this.scrollbar.scrollLeft-t;this.move(e),this.arrowNext.classList.remove(As),this.togglePrevArrow()}toggleNextArrow(){requestAnimationFrame((()=>{var t;null===(t=this.arrowNext)||void 0===t||t.classList.toggle(As,Math.round(this.scrollbar.scrollLeft+this.scrollbar.getBoundingClientRect().width+1)>=this.scrollbar.scrollWidth)}))}togglePrevArrow(){requestAnimationFrame((()=>{this.arrowPrev.classList.toggle(As,this.scrollbar.scrollLeft<=0)}))}scrollToVisibleElement(){[].forEach.call(this.scrollbar.children,(t=>{t.addEventListener("click",(e=>{e.preventDefault(),this.move(t.offsetLeft-t.clientWidth)}))}))}move(t){this.scrollbar.scrollTo({top:0,left:t,behavior:"smooth"})}constructor(){super(),this.scrollbar=this.querySelector(Ls),this.arrowNext=this.querySelector(Ss),this.arrowPrev=this.querySelector(Es),this.toggleNextArrow=this.toggleNextArrow.bind(this),this.addEventListener("theme:swatches:loaded",this.toggleNextArrow)}});const qs="[data-popout-list]",Ts="[data-popout-toggle]",Ms="[data-popout-toggle-text]",xs="[data-popout-input]",Is="[data-popout-option]",Hs="[data-product-image]",Os="[data-grid-item]",Ps="popout-list--visible",Ds="is-visible",Fs="is-active",_s="popout-list--top",$s="aria-expanded",Bs="aria-current",Ws="data-value",Ns="data-popout-toggle-text",Rs="submit";customElements.get("popout-select")||customElements.define("popout-select",class extends HTMLElement{connectedCallback(){this.popoutList=this.querySelector(qs),this.popoutToggle=this.querySelector(Ts),this.popoutToggleText=this.querySelector(Ms),this.popoutInput=this.querySelector(xs)||this.parentNode.querySelector(xs),this.popoutOptions=this.querySelectorAll(Is),this.productGridItem=this.popoutList.closest(Os),this.fireSubmitEvent=this.hasAttribute(Rs),this.popupToggleFocusoutEvent=t=>this.onPopupToggleFocusout(t),this.popupListFocusoutEvent=t=>this.onPopupListFocusout(t),this.popupToggleClickEvent=t=>this.onPopupToggleClick(t),this.keyUpEvent=t=>this.onKeyUp(t),this.bodyClickEvent=t=>this.onBodyClick(t),this._connectOptions(),this._connectToggle(),this._onFocusOut(),this.popupListSetDimensions()}onPopupToggleClick(t){const e="true"===t.currentTarget.getAttribute($s);if(this.productGridItem){const t=this.productGridItem.querySelector(Hs);t&&t.classList.toggle(Ds,!e),this.popoutList.style.maxHeight=`${Math.abs(this.popoutToggle.getBoundingClientRect().bottom-this.productGridItem.getBoundingClientRect().bottom)}px`}t.currentTarget.setAttribute($s,!e),this.popoutList.classList.toggle(Ps),this.popupListSetDimensions(),this.toggleListPosition(),document.body.addEventListener("click",this.bodyClickEvent)}onPopupToggleFocusout(t){this.contains(t.relatedTarget)||this._hideList()}onPopupListFocusout(t){const e=t.currentTarget.contains(t.relatedTarget);this.popoutList.classList.contains(Ps)&&!e&&this._hideList()}toggleListPosition(){const t=this.querySelector(Ts),e=this.getBoundingClientRect().top+this.clientHeight,s=()=>{"true"!==t.getAttribute($s)&&this.popoutList.classList.remove(_s),this.popoutList.removeEventListener("transitionend",s)};"true"===t.getAttribute($s)?window.innerHeight/2<e&&this.popoutList.classList.add(_s):this.popoutList.addEventListener("transitionend",s)}popupListSetDimensions(){this.popoutList.style.setProperty("--max-width","100vw"),this.popoutList.style.setProperty("--max-height","100vh"),requestAnimationFrame((()=>{this.popoutList.style.setProperty("--max-width",`${parseInt(document.body.clientWidth-this.popoutList.getBoundingClientRect().left)}px`),this.popoutList.style.setProperty("--max-height",`${parseInt(document.body.clientHeight-this.popoutList.getBoundingClientRect().top)}px`)}))}popupOptionsClick(t){if("#"===t.target.closest(Is).attributes.href.value){t.preventDefault();const e=t.currentTarget.hasAttribute(Ws)?t.currentTarget.getAttribute(Ws):"";if(this.popoutInput.value=e,this.popoutInput.disabled&&this.popoutInput.removeAttribute("disabled"),this.fireSubmitEvent)this._submitForm(e);else{const s=t.currentTarget.parentElement,i=this.popoutList.querySelector(`.${Fs}`),o=this.popoutList.querySelector(`[${Bs}]`);this.popoutInput.dispatchEvent(new Event("change")),i&&(i.classList.remove(Fs),s.classList.add(Fs)),"quantity"!=this.popoutInput.name||s.nextSibling||this.classList.add(Fs),o&&o.hasAttribute(`${Bs}`)&&(o.removeAttribute(`${Bs}`),t.currentTarget.setAttribute(`${Bs}`,"true")),""!==e&&(this.popoutToggleText.innerHTML=e,this.popoutToggleText.hasAttribute(Ns)&&""!==this.popoutToggleText.getAttribute(Ns)&&this.popoutToggleText.setAttribute(Ns,e)),this.onPopupToggleFocusout(t),this.onPopupListFocusout(t)}}}onKeyUp(t){"Escape"===t.code&&(this._hideList(),this.popoutToggle.focus())}onBodyClick(t){const e=this.contains(t.target);this.popoutList.classList.contains(Ps)&&!e&&this._hideList()}_connectToggle(){this.popoutToggle.addEventListener("click",this.popupToggleClickEvent)}_connectOptions(){this.popoutOptions.length&&this.popoutOptions.forEach((t=>{t.addEventListener("click",(t=>this.popupOptionsClick(t)))}))}_onFocusOut(){this.addEventListener("keyup",this.keyUpEvent),this.popoutToggle.addEventListener("focusout",this.popupToggleFocusoutEvent),this.popoutList.addEventListener("focusout",this.popupListFocusoutEvent)}_submitForm(){const t=this.closest("form");t&&t.submit()}_hideList(){this.popoutList.classList.remove(Ps),this.popoutToggle.setAttribute($s,!1),this.toggleListPosition(),document.body.removeEventListener("click",this.bodyClickEvent)}constructor(){super()}});const Us={open:"[data-popup-open]",close:"[data-popup-close]",dialog:"dialog",focusable:'button, [href], select, textarea, [tabindex]:not([tabindex="-1"])',newsletterForm:"[data-newsletter-form]",newsletterHeading:"[data-newsletter-heading]",newsletterField:"[data-newsletter-field]"},Vs={closing:"closing",delay:"data-popup-delay",scrollLock:"data-scroll-lock-required",cookieName:"data-cookie-name",cookieValue:"data-cookie-value",preventTopLayer:"data-prevent-top-layer"},js="hidden",zs="has-value",Js="cart-bar-visible",Xs="is-visible",Qs="has-success",Ys="mobile",Ks="desktop",Gs="bottom";let Zs=class extends HTMLElement{checkTargetReferrer(){this.popup.hasAttribute(Vs.referrer)&&(-1!==location.href.indexOf(this.popup.getAttribute(Vs.referrer))||window.Shopify.designMode||this.popup.parentNode.removeChild(this.popup))}checkCookie(){this.cookie&&!1!==this.cookie.read()||(this.showPopupEvents(),this.popup.addEventListener("theme:popup:onclose",(()=>this.cookie.write())))}bindListeners(){var t,e;null===(t=this.buttonPopupOpen)||void 0===t||t.addEventListener("click",(t=>{t.preventDefault(),this.popupOpen(),window.theme.a11y.lastElement=this.buttonPopupOpen})),null===(e=this.popup.querySelectorAll(Us.close))||void 0===e||e.forEach((t=>{t.addEventListener("click",(t=>{t.preventDefault(),this.popupClose()}))})),this.popup.addEventListener("click",(t=>{"DIALOG"===t.target.nodeName&&"click"===t.type&&this.popupClose()})),this.popup.addEventListener("keydown",(t=>{"Escape"===t.code&&(t.preventDefault(),this.popupClose())})),this.popup.addEventListener("close",(()=>this.popupCloseActions()))}popupOpen(){this.isAnimating=!0,"function"!=typeof this.popup.showModal||this.preventTopLayer?"function"==typeof this.popup.show?this.popup.show():this.popup.setAttribute("open",""):this.popup.showModal(),this.popup.removeAttribute("inert"),this.popup.setAttribute("aria-hidden",!1),this.popup.focus(),this.enableScrollLock&&document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),window.theme.waitForAnimationEnd(this.popup).then((()=>{this.isAnimating=!1,this.enableScrollLock&&this.a11y.trapFocus(this.popup);const t=this.popup.querySelector("[autofocus]")||this.popup.querySelector(Us.focusable);null==t||t.focus()}))}popupClose(){if(!this.isAnimating&&!this.popup.hasAttribute("inert")){if(!this.popup.hasAttribute(Vs.closing))return this.popup.setAttribute(Vs.closing,""),this.isAnimating=!0,void window.theme.waitForAnimationEnd(this.popup).then((()=>{this.isAnimating=!1,this.popupClose()}));"function"==typeof this.popup.close?this.popup.close():(this.popup.removeAttribute("open"),this.popup.setAttribute("aria-hidden",!0)),this.popupCloseActions()}}popupCloseActions(){this.popup.hasAttribute("inert")||(this.popup.setAttribute("inert",""),this.popup.setAttribute("aria-hidden",!0),this.popup.removeAttribute(Vs.closing),!window.theme.hasOpenModals()&&this.enableScrollLock&&document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0})),this.popup.dispatchEvent(new CustomEvent("theme:popup:onclose",{bubbles:!1})),this.enableScrollLock&&(this.a11y.removeTrapFocus(),this.a11y.autoFocusLastElement()))}showPopupEvents(){this.popup.hasAttribute("open")&&1==this.popup.getAttribute("open")&&this.popupOpen(),this.delay=this.popup.hasAttribute(Vs.delay)?this.popup.getAttribute(Vs.delay):null,this.isSubmitted=-1!==window.location.href.indexOf("accepts_marketing")||-1!==window.location.href.indexOf("customer_posted=true"),this.showOnScrollEvent=window.theme.throttle(this.showOnScroll.bind(this),200),("always"===this.delay||this.isSubmitted)&&this.popupOpen(),this.delay&&this.delay.includes("delayed")&&!this.isSubmitted&&this.showDelayed(),"bottom"!==this.delay||this.isSubmitted||this.showOnBottomReached(),"idle"!==this.delay||this.isSubmitted||this.showOnIdle()}showDelayed(){const t=this.delay.includes("_")?parseInt(this.delay.split("_")[1]):10;setTimeout((()=>{this.popupOpen()}),1e3*t)}showOnIdle(){let t=0;const e=["mousemove","mousedown","click","touchmove","touchstart","touchend","keydown","keypress"],s=["load","resize","scroll"],i=()=>{t=setTimeout((()=>{t=0,this.popupOpen()}),6e4),e.forEach((t=>{document.addEventListener(t,o)})),s.forEach((t=>{window.addEventListener(t,o)}))},o=()=>{t&&clearTimeout(t),e.forEach((t=>{document.removeEventListener(t,o)})),s.forEach((t=>{window.removeEventListener(t,o)})),i()};i()}showOnBottomReached(){document.addEventListener("theme:scroll",this.showOnScrollEvent)}showOnScroll(){window.scrollY+window.innerHeight>=document.body.clientHeight&&(this.popupOpen(),document.removeEventListener("theme:scroll",this.showOnScrollEvent))}disconnectedCallback(){document.removeEventListener("theme:scroll",this.showOnScrollEvent)}constructor(){super(),this.popup=this.querySelector(Us.dialog),this.preventTopLayer=this.popup.hasAttribute(Vs.preventTopLayer),this.enableScrollLock=this.popup.hasAttribute(Vs.scrollLock),this.buttonPopupOpen=this.querySelector(Us.open),this.a11y=window.theme.a11y,this.isAnimating=!1,this.cookie=new class{write(){(-1!==document.cookie.indexOf("; ")&&!document.cookie.split("; ").find((t=>t.startsWith(this.name)))||-1===document.cookie.indexOf("; "))&&(document.cookie=`${this.name}=${this.value}; expires=${this.config.expires}; path=${this.config.path}; domain=${this.config.domain}; sameSite=${this.config.sameSite}; secure=${this.config.secure}`)}read(){return!(-1===document.cookie.indexOf("; ")||!document.cookie.split("; ").find((t=>t.startsWith(this.name))))&&document.cookie.split("; ").find((t=>t.startsWith(this.name))).split("=")[1]}destroy(){document.cookie.split("; ").find((t=>t.startsWith(this.name)))&&(document.cookie=`${this.name}=null; expires=${this.config.expires}; path=${this.config.path}; domain=${this.config.domain}`)}constructor(t,e,s=7){const i=new Date,o=new Date;o.setTime(i.getTime()+864e5*s),this.config={expires:o.toGMTString(),path:"/",domain:window.location.hostname,sameSite:"none",secure:!0},this.name=t,this.value=e}}(this.popup.getAttribute(Vs.cookieName),this.popup.getAttribute(Vs.cookieValue)),this.checkTargetReferrer(),this.checkCookie(),this.bindListeners()}},ti=class extends Zs{connectedCallback(){var t;const e=!1!==(null===(t=this.cookie)||void 0===t?void 0:t.read()),s=-1!==window.location.search.indexOf("?customer_posted=true"),i=[...this.classList].toString().includes(Gs),o=this.popup.classList.contains(Ys),r=this.popup.classList.contains(Ks),n=window.theme.isMobile();let a=!0;if((o&&!n||r&&n)&&(a=!1),!a)return super.a11y.removeTrapFocus(),void document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0}));e&&!window.Shopify.designMode||(window.Shopify.designMode||window.location.pathname.endsWith("/challenge")||super.showPopupEvents(),this.form&&this.form.classList.contains(Qs)&&(super.popupOpen(),this.cookie.write()),this.popup.addEventListener("theme:popup:onclose",(()=>this.cookie.write()))),s&&(this.delay=0),e&&!window.Shopify.designMode||(this.show(),this.form.classList.contains(Qs)&&(this.popupOpen(),this.cookie.write())),i&&this.observeCartBar()}show(){window.location.pathname.endsWith("/challenge")||(window.Shopify.designMode?super.popupOpen():super.showPopupEvents()),this.showForm(),this.inputField(),this.popup.addEventListener("theme:popup:onclose",(()=>this.cookie.write()))}observeCartBar(){if(this.cartBar=document.getElementById(Us.cartBar),!this.cartBar)return;let t=this.cartBar.classList.contains(Xs);document.body.classList.toggle(Js,t);this.observer=new MutationObserver((e=>{for(const s of e)"attributes"===s.type&&(t=s.target.classList.contains(Xs),document.body.classList.toggle(Js,t))})),this.observer.observe(this.cartBar,{attributes:!0,childList:!1,subtree:!1})}showForm(){var t,e;null===(t=this.heading)||void 0===t||t.addEventListener("click",(t=>{t.preventDefault(),this.heading.classList.add(js),this.form.classList.remove(js),this.newsletterField.focus()})),null===(e=this.heading)||void 0===e||e.addEventListener("keyup",(t=>{"Enter"===t.code&&this.heading.dispatchEvent(new Event("click"))}))}inputField(){const t=()=>{this.resetClassTimer&&clearTimeout(this.resetClassTimer),""!==this.newsletterField.value&&this.popup.classList.add(zs)};this.newsletterField.addEventListener("input",t),this.newsletterField.addEventListener("focus",t),this.newsletterField.addEventListener("focusout",(()=>{this.resetClassTimer&&clearTimeout(this.resetClassTimer),this.resetClassTimer=setTimeout((()=>{this.popup.classList.remove(zs)}),2e3)}))}disconnectedCallback(){this.observer&&this.observer.disconnect()}constructor(){super(),this.form=this.popup.querySelector(Us.newsletterForm),this.heading=this.popup.querySelector(Us.newsletterHeading),this.newsletterField=this.popup.querySelector(Us.newsletterField)}};customElements.get("popup-component")||customElements.define("popup-component",Zs),customElements.get("popup-newsletter")||customElements.define("popup-newsletter",ti);const ei='[role="option"]',si='[aria-selected="true"]',ii="[data-popular-searches]",oi="predictive-search",ri="[data-predictive-search-results]",ni="[data-predictive-search-status]",ai='input[type="search"]',li="[data-popdown]",ci="[data-predictive-search-live-region-count-value]",di="[data-search-results-groups-wrapper]",hi="[data-predictive-search-search-for-text]",ui="#shopify-section-predictive-search",pi='[aria-selected="true"] a',mi='[aria-selected="true"] a, button[aria-selected="true"]';function vi(){this.entries=[]}function gi(t,e){bi(t);var s=function(t,e){bi(t),function(t){if(!Array.isArray(t))throw new TypeError(t+" is not an array.");if(0===t.length)throw new Error(t+" is empty.");if(!t[0].hasOwnProperty("name"))throw new Error(t[0]+"does not contain name key.");if("string"!=typeof t[0].name)throw new TypeError("Invalid value type passed for name of option "+t[0].name+". Value should be string.")}(e);var s=[];return e.forEach((function(e){for(var i=0;i<t.options.length;i++){if((t.options[i].name||t.options[i]).toLowerCase()===e.name.toLowerCase()){s[i]=e.value;break}}})),s}(t,e);return function(t,e){return bi(t),function(t){if(Array.isArray(t)&&"object"==typeof t[0])throw new Error(t+"is not a valid array of options.")}(e),t.variants.filter((function(t){return e.every((function(e,s){return t.options[s]===e}))}))[0]||null}(t,s)}function bi(t){if("object"!=typeof t)throw new TypeError(t+" is not an object.");if(0===Object.keys(t).length&&t.constructor===Object)throw new Error(t+" is empty.")}customElements.get("predictive-search")||customElements.define("predictive-search",class extends ws{connectedCallback(){this.input.addEventListener("focus",this.onFocus.bind(this)),this.input.form.addEventListener("submit",this.onFormSubmit.bind(this)),this.addEventListener("focusout",this.onFocusOut.bind(this)),this.addEventListener("keyup",this.onKeyup.bind(this)),this.addEventListener("keydown",this.onKeydown.bind(this))}getQuery(){return this.input.value.trim()}onChange(){super.onChange();const t=this.getQuery();var e;this.searchTerm&&t.startsWith(this.searchTerm)||(null===(e=this.querySelector(di))||void 0===e||e.remove());this.updateSearchForTerm(this.searchTerm,t),this.searchTerm=t,this.searchTerm.length?this.getSearchResults(this.searchTerm):this.reset()}onFormSubmit(t){this.getQuery().length&&!this.querySelector(pi)||t.preventDefault()}onFormReset(t){super.onFormReset(t),super.shouldResetForm()&&(this.searchTerm="",this.abortController.abort(),this.abortController=new AbortController,this.closeResults(!0))}shouldResetForm(){return!document.querySelector(pi)}onFocus(){const t=this.getQuery();t.length&&(this.searchTerm!==t?this.onChange():"true"===this.getAttribute("results")?this.open():this.getSearchResults(this.searchTerm))}onFocusOut(){setTimeout((()=>{this.contains(document.activeElement)||this.close()}))}onKeyup(t){switch(this.getQuery().length||this.close(!0),t.preventDefault(),t.code){case"ArrowUp":this.switchOption("up");break;case"ArrowDown":this.switchOption("down");break;case"Enter":this.selectOption()}}onKeydown(t){"ArrowUp"!==t.code&&"ArrowDown"!==t.code||t.preventDefault()}updateSearchForTerm(t,e){const s=this.querySelector(hi),i=null==s?void 0:s.innerText;if(i){var o;if((null===(o=i.match(new RegExp(t,"g")))||void 0===o?void 0:o.length)>1)return;const r=i.replace(t,e);s.innerText=r}}switchOption(t){if(!this.getAttribute("open"))return;const e="up"===t,s=this.querySelector(si),i=Array.from(this.querySelectorAll(ei)).filter((t=>null!==t.offsetParent));let o=0;if(e&&!s)return;let r=-1,n=0;for(;-1===r&&n<=i.length;)i[n]===s&&(r=n),n++;if(this.statusElement.textContent="",!e&&s?o=r===i.length-1?0:r+1:e&&(o=0===r?i.length-1:r-1),o===r)return;const a=i[o];a.setAttribute("aria-selected",!0),s&&s.setAttribute("aria-selected",!1),this.input.setAttribute("aria-activedescendant",a.id)}selectOption(){const t=this.querySelector(mi);t&&t.click()}getSearchResults(t){const e=t.replace(" ","-").toLowerCase();this.setLiveRegionLoadingState(),this.cachedResults[e]?this.renderSearchResults(this.cachedResults[e]):fetch(`${theme.routes.predictive_search_url}?q=${encodeURIComponent(t)}&section_id=predictive-search`,{signal:this.abortController.signal}).then((t=>{if(!t.ok){var e=new Error(t.status);throw this.close(),e}return t.text()})).then((t=>{const s=(new DOMParser).parseFromString(t,"text/html").querySelector(ui).innerHTML;this.allPredictiveSearchInstances.forEach((t=>{t.cachedResults[e]=s})),this.renderSearchResults(s)})).catch((t=>{if(20!==(null==t?void 0:t.code))throw this.close(),t}))}setLiveRegionLoadingState(){this.statusElement=this.statusElement||this.querySelector(ni),this.loadingText=this.loadingText||this.getAttribute("data-loading-text"),this.setLiveRegionText(this.loadingText),this.setAttribute("loading",!0)}setLiveRegionText(t){this.statusElement.setAttribute("aria-hidden","false"),this.statusElement.textContent=t,setTimeout((()=>{this.statusElement.setAttribute("aria-hidden","true")}),1e3)}renderSearchResults(t){this.predictiveSearchResults.innerHTML=t,this.setAttribute("results",!0),this.setLiveRegionResults(),this.open()}setLiveRegionResults(){this.removeAttribute("loading"),this.setLiveRegionText(this.querySelector(ci).textContent)}open(){this.setAttribute("open",!0),this.input.setAttribute("aria-expanded",!0),this.isOpen=!0,this.predictiveSearchResults.style.setProperty("--results-height",window.visualViewport.height-this.predictiveSearchResults.getBoundingClientRect().top+"px")}close(t=!1){this.closeResults(t),this.isOpen=!1,this.predictiveSearchResults.style.removeProperty("--results-height")}closeResults(t=!1){var e;t&&(this.input.value="",this.removeAttribute("results"));const s=this.querySelector(si);s&&s.setAttribute("aria-selected",!1),this.input.setAttribute("aria-activedescendant",""),this.removeAttribute("loading"),this.removeAttribute("open"),this.input.setAttribute("aria-expanded",!1),null===(e=this.predictiveSearchResults)||void 0===e||e.removeAttribute("style")}reset(){this.predictiveSearchResults.innerHTML="",this.input.val="",this.a11y.removeTrapFocus(),this.popularSearches&&(this.input.dispatchEvent(new Event("blur",{bubbles:!1})),this.a11y.trapFocus(this.searchPopdown,{elementToFocus:this.input}))}constructor(){var t;super(),this.a11y=window.theme.a11y,this.abortController=new AbortController,this.allPredictiveSearchInstances=document.querySelectorAll(oi),this.cachedResults={},this.input=this.querySelector(ai),this.isOpen=!1,this.predictiveSearchResults=this.querySelector(ri),this.searchPopdown=this.closest(li),this.popularSearches=null===(t=this.searchPopdown)||void 0===t?void 0:t.querySelector(ii),this.searchTerm=""}}),vi.prototype.add=function(t,e,s){this.entries.push({element:t,event:e,fn:s}),t.addEventListener(e,s)},vi.prototype.removeAll=function(){this.entries=this.entries.filter((function(t){return t.element.removeEventListener(t.event,t.fn),!1}))};var wi='[name="id"]',fi='[name="selling_plan"]',yi='[name^="options"]',Li='[name="quantity"]',Ei='[name^="properties"]';const Si="data-option-position",Ai='[name^="options"], [data-popout-option]',ki='[name^="options"]:checked, [name^="options"][type="hidden"]',Ci="data-value",qi="[data-popout]",Ti="sold-out",Mi="unavailable",xi="sale";const Ii="[data-product]",Hi="[data-product-form]",Oi="product-notification",Pi="[data-variant-title]",Di="[data-notification-product]",Fi="[data-item-count-for-variant]",_i="[data-max-inventory]",$i="[data-add-to-cart]",Bi="[data-add-to-cart-text]",Wi="[data-compare-price]",Ni="[data-compare-text]",Ri="[data-final-sale-badge]",Ui="[data-color-description-metafield]",Vi="[data-color-description]",ji="[data-form-wrapper]",zi="[data-product-select]",Ji="[data-price-wrapper]",Xi="product-images",Qi="[data-product-media-list]",Yi="[data-product-json]",Ki="[data-product-price]",Gi="[data-product-unit-price]",Zi="[data-product-base]",to="[data-product-unit]",eo="[data-product-preorder]",so="[data-subscription-watch-price]",io="[data-subscription-selectors]",oo="[data-toggles-group]",ro="data-group-toggle",no="[data-plan-description]",ao="[data-section-type]",lo="[data-variant-sku]",co="[data-variant-final-sale-metafield]",ho="[data-variant-buttons]",uo="[data-variant-option-image]",po="[data-quick-add-modal]",mo="[data-price-off-amount]",vo="[data-price-off-badge]",go="[data-price-off-type]",bo="[data-price-off]",wo="[data-remaining-count]",fo="[data-remaining-max]",yo="[data-remaining-wrapper]",Lo="[data-product-remaining-json]",Eo="[data-option-value]",So="[data-option-position]",Ao="[data-product-form-installment]",ko='input[name="id"]',Co="hidden",qo="variant--soldout",To="variant--unavailable",Mo="product__price--sale",xo="count-is-low",Io="count-is-in",Ho="count-is-out",Oo="count-is-unavailable",Po="data-remaining-max",Do="data-enable-history-state",Fo="data-fader-desktop",_o="data-fader-mobile",$o="data-option-position",Bo="data-image-id",Wo="data-media-id",No="data-quick-add-btn",Ro="data-final-sale",Uo="data-variant-image-scroll",Vo="data-max-inventory-reached",jo="data-error-message-position";customElements.get("product-form")||customElements.define("product-form",class extends HTMLElement{connectedCallback(){if(this.cartAddEvents(),this.container=this.closest(ao)||this.closest(po),!this.container)return;if(this.sectionId=this.container.dataset.sectionId,this.product=this.container.querySelector(Ii),this.productForm=this.container.querySelector(Hi),this.productNotification=this.container.querySelector(Oi),this.productImages=this.container.querySelector(Xi),this.productMediaList=this.container.querySelector(Qi),this.installmentForm=this.container.querySelector(Ao),this.skuWrapper=this.container.querySelector(lo),this.sellout=null,this.variantImageScroll="true"===this.container.getAttribute(Uo),this.priceOffWrap=this.container.querySelector(bo),this.priceOffAmount=this.container.querySelector(mo),this.priceOffType=this.container.querySelector(go),this.planDescription=this.container.querySelector(no),this.remainingWrapper=this.container.querySelector(yo),this.colorDescriptionMetafield=this.container.querySelector(Ui),this.colorDescription=this.container.querySelector(Vi),this.colorDescriptionData=null,this.colorDescriptionMetafield&&this.colorDescriptionMetafield.textContent)try{this.colorDescriptionData=JSON.parse(this.colorDescriptionMetafield.textContent)}catch(t){console.warn("Error parsing color description metafield data:",t)}if(this.remainingWrapper){const t=this.container.querySelector(fo);t&&(this.remainingMaxInt=parseInt(t.getAttribute(Po),10),this.remainingCount=this.container.querySelector(wo),this.remainingJSONWrapper=this.container.querySelector(Lo),this.remainingJSON=null,this.remainingJSONWrapper&&""!==this.remainingJSONWrapper.innerHTML?this.remainingJSON=JSON.parse(this.remainingJSONWrapper.innerHTML):console.warn("Missing product quantity JSON"))}this.enableHistoryState="true"===this.container.getAttribute(Do),this.hasUnitPricing=this.container.querySelector(to),this.subSelectors=this.container.querySelector(io),this.subPrices=this.container.querySelector(so),this.isPreOrder=this.container.querySelector(eo);let t=null;const e=this.container.querySelector(Yi);e&&(t=e.innerHTML),t?(this.productJSON=JSON.parse(t),this.linkForm(),this.sellout=new class{init(){this.update()}update(){this.getCurrentState(),this.optionElements.forEach((t=>{const e=t.closest(`[${Si}]`);if(!e)return;const s=t.value||t.getAttribute(Ci),i=e.getAttribute(Si),o=parseInt(i,10)-1,r=t.closest(qi);let n=[...this.selections];n[o]=s;const a=this.productJSON.variants.find((t=>{let e=!0;for(let s=0;s<n.length;s++)t.options[s]!==n[s]&&(e=!1);return e}));t.classList.remove(Ti,Mi),t.parentNode.classList.remove(xi),r&&r.classList.remove(Ti,Mi,xi),void 0===a?(t.classList.add(Mi),r&&r.classList.add(Mi)):a&&!1===a.available&&(t.classList.add(Ti),r&&r.classList.add(Ti)),a&&a.compare_at_price>a.price&&theme.settings.variantOnSale&&t.parentNode.classList.add(xi)}))}getCurrentState(){this.selections=[];const t=this.container.querySelectorAll(ki);t.length&&t.forEach((t=>{const e=t.value;e&&""!==e&&this.selections.push(e)}))}constructor(t,e){this.container=t,this.productJSON=e,this.optionElements=this.container.querySelectorAll(Ai),this.productJSON&&this.optionElements.length&&this.init()}}(this.container,this.productJSON)):console.error("Missing product JSON"),this.variantOptionImages=this.container.querySelectorAll(uo),this.variantButtons=this.container.querySelectorAll(ho),this.variantOptionImages.length>1&&this.optionImagesWidth()}cartAddEvents(){this.buttonATC=this.querySelector($i),this.buttonATC.addEventListener("click",(t=>{t.preventDefault(),document.dispatchEvent(new CustomEvent("theme:cart:add",{detail:{button:this.buttonATC},bubbles:!1})),this.closest(po)||(window.a11y.lastElement=this.buttonATC)}))}destroy(){this.productForm.destroy()}linkForm(){this.productForm=new class{destroy(){this._listeners.removeAll()}options(){return this._serializeInputValues(this.optionInputs,(function(t){return t.name=/(?:^(options\[))(.*?)(?:\])/.exec(t.name)[2],t}))}variant(){const t=this.options();return t.length?gi(this.product,t):this.product.variants[0]}plan(t){let e={allocation:null,group:null,detail:null};const s=this.element.querySelector(`${fi}:checked`);if(!s)return null;const i=s.value,o=i&&""!==i?i:null;return o&&t&&(e.allocation=t.selling_plan_allocations.find((function(t){return t.selling_plan_id.toString()===o.toString()}))),e.allocation&&(e.group=this.product.selling_plan_groups.find((function(t){return t.id.toString()===e.allocation.selling_plan_group_id.toString()}))),e.group&&(e.detail=e.group.selling_plans.find((function(t){return t.id.toString()===o.toString()}))),e&&e.allocation&&e.detail&&e.allocation?e:null}properties(){return this._serializeInputValues(this.propertyInputs,(function(t){return t.name=/(?:^(properties\[))(.*?)(?:\])/.exec(t.name)[2],t}))}quantity(){return this.quantityInputs[0]?Number.parseInt(this.quantityInputs[0].value,10):1}getFormState(){const t=this.variant();return{options:this.options(),variant:t,properties:this.properties(),quantity:this.quantity(),plan:this.plan(t)}}_setIdInputValue(t){t&&t.id?this.variantElement.value=t.id.toString():this.variantElement.value="",this.variantElement.dispatchEvent(new Event("change"))}_onSubmit(t,e){e.dataset=this.getFormState(),t.onFormSubmit&&t.onFormSubmit(e)}_onOptionChange(t){this._setIdInputValue(t.dataset.variant)}_onFormEvent(t){return void 0===t?Function.prototype.bind():function(e){e.dataset=this.getFormState(),this._setIdInputValue(e.dataset.variant),t(e)}.bind(this)}_initInputs(t,e){return Array.prototype.slice.call(this.element.querySelectorAll(t)).map(function(t){return this._listeners.add(t,"change",this._onFormEvent(e)),t}.bind(this))}_serializeInputValues(t,e){return t.reduce((function(t,s){return(s.checked||"radio"!==s.type&&"checkbox"!==s.type)&&t.push(e({name:s.name,value:s.value})),t}),[])}_validateProductObject(t){if("object"!=typeof t)throw new TypeError(t+" is not an object.");if(void 0===t.variants[0].options)throw new TypeError("Product object is invalid. Make sure you use the product object that is output from {{ product | json }} or from the http://[your-product-url].js route");return t}constructor(t,e,s){this.element=t,this.product=this._validateProductObject(e),this.variantElement=this.element.querySelector(wi),s=s||{},this._listeners=new vi,this._listeners.add(this.element,"submit",this._onSubmit.bind(this,s)),this.optionInputs=this._initInputs(yi,s.onOptionChange),this.planInputs=this._initInputs(fi,s.onPlanChange),this.quantityInputs=this._initInputs(Li,s.onQuantityChange),this.propertyInputs=this._initInputs(Ei,s.onPropertyChange)}}(this.container,this.productJSON,{onOptionChange:this.onOptionChange.bind(this),onPlanChange:this.onPlanChange.bind(this),onQuantityChange:this.onQuantityChange.bind(this)}),this.pushState(this.productForm.getFormState(),!0),this.subsToggleListeners(),this.checkLiveCartInfoCallback=()=>this.checkLiveCartInfo(),document.addEventListener("theme:cart-drawer:close",this.checkLiveCartInfoCallback)}onOptionChange(t){this.pushState(t.dataset)}onPlanChange(t){this.subPrices&&this.pushState(t.dataset)}onQuantityChange(t){this.pushState(t.dataset)}pushState(t,e=!1){var s;this.productState=this.setProductState(t),this.updateAddToCartState(t),this.updateNotificationForm(t),this.updateProductPrices(t),this.updateProductImage(t),this.updateSaleText(t),this.updateSku(t),this.updateSubscriptionText(t),this.updateRemaining(t),this.checkLiveCartInfo(t),this.updateLegend(t),this.updateColorDescription(t),this.fireHookEvent(t),null===(s=this.sellout)||void 0===s||s.update(t),this.enableHistoryState&&!e&&this.updateHistoryState(t)}updateAddToCartState(t){const e=t.variant;let s=theme.strings.addToCart;const i=this.container.querySelectorAll(Ji),o=this.container.querySelectorAll($i),r=this.container.querySelectorAll(Bi),n=this.container.querySelectorAll(ji);if(this.installmentForm&&e){const t=this.installmentForm.querySelector(ko);t.value=e.id,t.dispatchEvent(new Event("change",{bubbles:!0}))}if(this.isPreOrder&&(s=theme.strings.preOrder),theme.settings.atcButtonShowPrice){var a;const t=(null===(a=this.container.querySelector("quantity-counter input"))||void 0===a?void 0:a.value)||1,i=e.price*t;let o=0===i?window.theme.strings.free:window.theme.formatMoney(i,theme.moneyFormat);if(e.compare_at_price&&e.compare_at_price>e.price){const s=e.compare_at_price*t;o=`${o} <s>${window.theme.formatMoney(s,theme.moneyFormat)}</s>`}s=`${s} <span class="btn__price">${o}</span>`}i.length&&e&&i.forEach((t=>{t.classList.remove(Co)})),null==o||o.forEach((t=>{t.hasAttribute(No)||(e&&e.available?t.disabled=!1:t.disabled=!0)})),null==r||r.forEach((t=>{let i=s;e?e.available||(i=theme.strings.soldOut):i=theme.strings.unavailable,t.innerHTML=i})),n.length&&n.forEach((t=>{if(e){e.available?t.classList.remove(qo,To):(t.classList.add(qo),t.classList.remove(To));const s=t.querySelector(zi);s&&(s.value=e.id);const i=t.querySelector(`${ko}[form]`);i&&(i.value=e.id,i.dispatchEvent(new Event("change")))}else t.classList.add(To),t.classList.remove(qo)}))}updateNotificationForm(t){if(!this.productNotification)return;const e=this.productNotification.querySelector(Pi),s=this.productNotification.querySelector(Di);null!=e&&null!=s&&(e.textContent=t.variant.title,s&&(s.value=t.variant.name))}updateHistoryState(t){const e=t.variant,s=t.plan,i=window.location.href;if(e&&i.includes("/product")){const t=new window.URL(i),o=t.searchParams;o.set("variant",e.id),s&&s.detail&&s.detail.id&&this.productState.hasPlan?o.set("selling_plan",s.detail.id):o.delete("selling_plan"),t.search=o.toString();const r=t.toString();window.history.replaceState({path:r},"",r)}}updateRemaining(t){var e;const s=t.variant;if(null===(e=this.remainingWrapper)||void 0===e||e.classList.remove(Io,Ho,Oo,xo),s&&this.remainingWrapper&&this.remainingJSON){const t=this.remainingJSON[s.id];("out"===t||t<1)&&this.remainingWrapper.classList.add(Ho),("in"===t||t>=this.remainingMaxInt)&&this.remainingWrapper.classList.add(Io),("low"===t||t>0&&t<this.remainingMaxInt)&&(this.remainingWrapper.classList.add(xo),this.remainingCount&&(this.remainingCount.innerHTML=t))}else!s&&this.remainingWrapper&&this.remainingWrapper.classList.add(Oo)}checkLiveCartInfo(t){const e=(t||this.productForm.getFormState()).variant;if(!e)return;const s=`${theme.routes.root}products/${this.productJSON.handle}?section_id=api-live-cart-info&variant=${e.id}`;fetch(s).then((t=>t.text())).then((t=>{const e=(new DOMParser).parseFromString(t,"text/html"),s=Number(e.querySelector(Fi).innerHTML),i=e.querySelector(_i).innerHTML,o=Number(i),r=Boolean(this.productForm.quantity()+s>o),n=""!==i&&r,a=""!==i&&s===o?"form":"cart";this.productForm.element.form.setAttribute(Vo,n),this.productForm.element.form.setAttribute(jo,a)})).catch((t=>console.log("error: ",t)))}optionImagesWidth(){if(!this.variantButtons)return;let t=0;requestAnimationFrame((()=>{this.variantOptionImages.forEach((e=>{const s=e.clientWidth;s>t&&(t=s)})),this.variantButtons.forEach((e=>{var s;null===(s=e.style)||void 0===s||s.setProperty("--option-image-width",t+"px")}))}))}getBaseUnit(t){return 1===t.unit_price_measurement.reference_value?t.unit_price_measurement.reference_unit:t.unit_price_measurement.reference_value+t.unit_price_measurement.reference_unit}subsToggleListeners(){this.container.querySelectorAll(oo).forEach((t=>{t.addEventListener("change",function(t){const e=t.target.value.toString(),s=this.container.querySelector(`[${ro}="${e}"]`),i=this.container.querySelectorAll(`[${ro}]`);if(s){s.classList.remove(Co);const t=s.querySelector('[name="selling_plan"]');t.checked=!0,t.dispatchEvent(new Event("change"))}i.forEach((t=>{if(t!==s){t.classList.add(Co);t.querySelectorAll('[name="selling_plan"]').forEach((t=>{t.checked=!1,t.dispatchEvent(new Event("change"))}))}}))}.bind(this))}))}updateSaleText(t){this.priceOffWrap&&(this.productState.planSale?this.updateSaleTextSubscription(t):this.productState.onSale?this.updateSaleTextStandard(t):this.priceOffWrap.classList.add(Co))}isVariantFinalSale(t){var e;const s=null===(e=document.querySelector(co))||void 0===e?void 0:e.textContent;if(!s)return;const i=JSON.parse(s);let o=!1;return i.forEach((e=>{Number(e.variant_id)===t.id&&(o="true"===e.metafield_value)})),o}updateSaleTextStandard(t){var e,s,i;const o=t.variant,r=null===(e=this.priceOffWrap)||void 0===e?void 0:e.querySelector(Ri),n=null===(s=this.priceOffWrap)||void 0===s?void 0:s.querySelector(vo),a=null==o?void 0:o.compare_at_price,l=null==o?void 0:o.price;if(this.priceOffType&&(this.priceOffType.innerHTML=window.theme.strings.sale||"sale"),!n||!this.priceOffAmount||!a||a<=l)null==n||n.classList.add(Co);else{const t=Math.round((a-l)/a*100);this.priceOffAmount.innerHTML=`${t}%`,n.classList.remove(Co)}const c=(null===(i=this.priceOffWrap)||void 0===i?void 0:i.hasAttribute(Ro))||this.isVariantFinalSale(o);r&&r.classList.toggle(Co,!c),this.priceOffWrap.classList.remove(Co)}updateSubscriptionText(t){t.plan&&this.planDescription?(this.planDescription.innerHTML=t.plan.detail.description,this.planDescription.classList.remove(Co)):this.planDescription&&this.planDescription.classList.add(Co)}updateSaleTextSubscription(t){if(this.priceOffType&&(this.priceOffType.innerHTML=window.theme.strings.subscription||"subscripton"),this.priceOffAmount&&this.priceOffWrap){const e=t.plan.detail.price_adjustments[0],s=e.value;e&&"percentage"===e.value_type?this.priceOffAmount.innerHTML=`${s}%`:this.priceOffAmount.innerHTML=window.theme.formatMoney(s,theme.moneyFormat),this.priceOffWrap.classList.remove(Co)}}updateProductPrices(t){const e=t.variant,s=t.plan;this.container.querySelectorAll(Ji).forEach((t=>{const i=t.querySelector(Wi),o=t.querySelector(Ki),r=t.querySelector(Ni);let n="",a="";this.productState.available&&(n=e.compare_at_price,a=e.price),this.productState.hasPlan&&(a=s.allocation.price),this.productState.planSale&&(n=s.allocation.compare_at_price,a=s.allocation.price),i&&(this.productState.onSale||this.productState.planSale?(i.classList.remove(Co),r.classList.remove(Co),o.classList.add(Mo)):(i.classList.add(Co),r.classList.add(Co),o.classList.remove(Mo)),i.innerHTML=window.theme.formatMoney(n,theme.moneyFormat)),o.innerHTML=0===a?window.theme.strings.free:window.theme.formatMoney(a,theme.moneyFormat)})),this.hasUnitPricing&&this.updateProductUnits(t)}updateProductUnits(t){const e=t.variant,s=t.plan;let i=null;if(e&&e.unit_price&&(i=e.unit_price),s&&s.allocation&&s.allocation.unit_price&&(i=s.allocation.unit_price),i){const t=this.getBaseUnit(e),s=window.theme.formatMoney(i,theme.moneyFormat);this.container.querySelector(Gi).innerHTML=s,this.container.querySelector(Zi).innerHTML=t,this.container.querySelector(to).classList.remove(Co)}else this.container.querySelector(to).classList.add(Co)}updateSku(t){this.skuWrapper&&(this.skuWrapper.innerHTML=`${theme.strings.sku}: ${t.variant.sku}`)}fireHookEvent(t){const e=t.variant;this.container.dispatchEvent(new CustomEvent("theme:variant:change",{detail:{variant:e},bubbles:!0}))}setProductState(t){const e=t.variant,s=t.plan,i={available:!0,soldOut:!1,onSale:!1,showUnitPrice:!1,requiresPlan:!1,hasPlan:!1,planPerDelivery:!1,planSale:!1};return!e||e.requires_selling_plan&&!s?i.available=!1:(e.available||(i.soldOut=!0),e.compare_at_price>e.price&&(i.onSale=!0),e.unit_price&&(i.showUnitPrice=!0),this.product&&this.product.requires_selling_plan&&(i.requiresPlan=!0),s&&this.subPrices&&(i.hasPlan=!0,s.allocation.per_delivery_price!==s.allocation.price&&(i.planPerDelivery=!0),e.price>s.allocation.price&&(i.planSale=!0))),i}updateProductImage(t){var e;const s=(null===(e=t.dataset)||void 0===e?void 0:e.variant)||t.variant;if(s&&s.featured_media){const t=this.container.querySelector(`[${Bo}="${s.featured_media.id}"]`);if(t){const e=t.getAttribute(Wo),s=!window.theme.isMobile();if(t.dispatchEvent(new CustomEvent("theme:media:select",{bubbles:!0,detail:{id:e}})),s&&!this.productImages.hasAttribute(Fo)&&this.variantImageScroll){const e=t.getBoundingClientRect().top;document.dispatchEvent(new CustomEvent("theme:tooltip:close",{bubbles:!1,detail:{hideTransition:!1}})),window.theme.scrollTo(e)}s||this.productImages.hasAttribute(_o)||this.productMediaList.scrollTo({left:t.offsetLeft})}}}updateLegend(t){const e=t.variant;if(e){const t=this.container.querySelectorAll(Eo);t.length&&t.forEach((t=>{const s=t.closest(So);if(s){const i=s.getAttribute($o),o=parseInt(i,10)-1,r=e.options[o];t.innerHTML=r}}))}}updateColorDescription(t){const e=t.variant;if(!e||!this.colorDescription||!this.colorDescriptionData.length)return;const s=e.id.toString();let i="";for(const t of this.colorDescriptionData){if(t.variant_id.toString()===s&&t.metafield_value){i=t.metafield_value;break}}this.colorDescription.textContent=i,i?this.colorDescription.classList.remove(Co):this.colorDescription.classList.add(Co)}constructor(){super()}});const zo="[data-grid-swatch-fieldset]",Jo="[data-grid-item]",Xo="[data-product-information]",Qo="[data-product-image]",Yo="[data-swatch-button]",Ko="[data-swatch-link]",Go="[data-swatch-text]",Zo="[data-swatch-template]",tr="native-scrollbar",er="is-visible",sr="no-events",ir="swatch",or="data-swatch-handle",rr="data-swatch-label",nr="data-swatch-count",ar="data-swatch-variant-name",lr="data-variant-title",cr="data-swatch-values";let dr=class extends HTMLElement{connectedCallback(){this.handle=this.getAttribute(or),this.nativeScrollbar=this.closest(tr),this.productItem=this.closest(Jo),this.productInfo=this.closest(Xo),this.productImage=this.productItem.querySelector(Qo),this.template=document.querySelector(Zo).innerHTML,this.swatchesJSON=this.getSwatchesJSON(),this.swatchesStyle=theme.settings.collectionSwatchStyle;const t=this.getAttribute(rr).trim().toLowerCase();(function(t){const e=`${window.theme.routes.root}products/${t}.js`;return window.fetch(e).then((t=>t.json())).catch((t=>{console.error(t)}))})(this.handle).then((e=>{this.product=e,this.colorOption=e.options.find((function(e){return e.name.toLowerCase()===t||null})),this.colorOption&&this.init()}))}init(){if(this.innerHTML="",this.count=0,this.limitedCount=0,this.swatches=this.colorOption.values,this.swatchesCount=0,this.swatches.forEach((t=>{let e=null,s=!1,i="";for(const s of this.product.variants){const o=s.options.includes(t);if(!e&&o&&(e=s),o&&s.featured_media){i=s.featured_media.preview_image.src,e=s;break}}for(const e of this.product.variants){if(e.options.includes(t)&&e.available){s=!0;break}}if(e){const n=document.createElement("div");n.innerHTML=this.template;const a=n.querySelector(Yo),l=n.querySelector(Ko),c=n.querySelector(Go),d=this.swatchesJSON[t],h="native"==theme.settings.swatchesType?d:`var(--${d})`,u=e.title.replaceAll('"',"'");a.style=`--animation-delay: ${100*this.count/1250}s`,a.classList.add(`${ir}-${d}`),a.dataset.tooltip=t,a.dataset.swatchVariant=e.id,a.dataset.swatchVariantName=u,a.dataset.swatchImage=i,a.dataset.variant=e.id,a.style.setProperty("--swatch",h),l.href=(o=this.product.url,r=e.id,/variant=/.test(o)?o.replace(/(variant=)[^&]+/,"$1"+r):/\?/.test(o)?o.concat("&variant=").concat(r):o.concat("?variant=").concat(r)),l.dataset.swatch=t,l.disabled=!s,c.innerText=t,"limited"!=this.swatchesStyle?this.innerHTML+=n.innerHTML:this.count<=4&&(this.innerHTML+=n.innerHTML,this.limitedCount++),this.count++}var o,r,n;(this.swatchesCount++,this.swatchesCount==this.swatches.length)&&(null===(n=this.nativeScrollbar)||void 0===n||n.dispatchEvent(new Event("theme:swatches:loaded")))})),this.swatchCount=this.productInfo.querySelector(`[${nr}]`),this.swatchElements=this.querySelectorAll(Ko),this.swatchFieldset=this.productInfo.querySelector(zo),this.hideSwatchesTimer=0,this.swatchCount.hasAttribute(nr)){if("text"==this.swatchesStyle||"text-slider"==this.swatchesStyle){if(this.swatchCount.innerText=`${this.count} ${this.count>1?theme.strings.otherColor:theme.strings.oneColor}`,"text"==this.swatchesStyle)return;this.swatchCount.addEventListener("mouseenter",(()=>{this.hideSwatchesTimer&&clearTimeout(this.hideSwatchesTimer),this.productInfo.classList.add(sr),this.swatchFieldset.classList.add(er)})),this.productInfo.addEventListener("mouseleave",(()=>{this.hideSwatchesTimer=setTimeout((()=>{this.productInfo.classList.remove(sr),this.swatchFieldset.classList.remove(er)}),100)}))}if("slider"!=this.swatchesStyle&&"grid"!=this.swatchesStyle||this.swatchFieldset.classList.add(er),"limited"==this.swatchesStyle){const t=this.count-this.limitedCount;this.swatchFieldset.classList.add(er),t>0&&(this.innerHTML+=`<div class="swatch-limited">+${t}</div>`)}}this.bindSwatchButtonEvents()}bindSwatchButtonEvents(){var t;null===(t=this.querySelectorAll(Yo))||void 0===t||t.forEach((t=>{t.addEventListener("mouseenter",this.showVariantImageEvent)})),this.productItem.addEventListener("mouseleave",this.productItemMouseLeaveEvent)}showVariantImage(t){var e;const s=null===(e=t.target.getAttribute(ar))||void 0===e?void 0:e.replaceAll('"',"'"),i=this.productImage.querySelectorAll(`[${lr}]`),o=this.productImage.querySelector(`[${lr}="${s}"]`);null==i||i.forEach((t=>{t.classList.remove(er)})),null==o||o.classList.add(er)}hideVariantImages(){var t;null===(t=this.productImage.querySelectorAll(`[${lr}].${er}`))||void 0===t||t.forEach((t=>{t.classList.remove(er)}))}getSwatchesJSON(){if(!this.hasAttribute(cr))return{};const t=this.getAttribute(cr).split(","),e={};return null==t||t.forEach((t=>{const[s,i]=t.split(":");e[s.trim()]=i.trim()})),e}constructor(){super(),this.productItemMouseLeaveEvent=()=>this.hideVariantImages(),this.showVariantImageEvent=t=>this.showVariantImage(t)}};const hr=".flickity-prev-next-button",ur="[data-product-link]",pr="[data-hover-slide]",mr="[data-hover-slide-touch]",vr="[data-hover-slider]",gr="recently-viewed",br="video",wr='[data-host="vimeo"]',fr='[data-host="youtube"]';let yr=class extends HTMLElement{connectedCallback(){this.addArrowClickHandler(),this.recentlyViewed?this.recentlyViewed.addEventListener("theme:recently-viewed:loaded",(()=>{this.initBasedOnDevice()})):this.initBasedOnDevice()}disconnectedCallback(){this.flkty&&(this.flkty.options.watchCSS=!1,this.flkty.destroy(),this.flkty=null),this.removeEventListener("mouseenter",this.mouseEnterEvent),this.removeEventListener("mouseleave",this.mouseLeaveEvent)}initBasedOnDevice(){window.theme.touch?this.initTouch():this.initFlickity()}addArrowClickHandler(){const t=this.closest(ur);t&&t.addEventListener("click",(t=>{t.target.matches(hr)&&t.preventDefault()}))}initTouch(){this.style.setProperty("--slides-count",this.querySelectorAll(mr).length),this.slider.addEventListener("scroll",this.handleScroll)}handleScroll(){const t=this.slider.scrollLeft/this.slider.clientWidth;this.style.setProperty("--slider-index",t)}initFlickity(){this.flkty||!this.slider||this.slider.classList.contains("flickity-enabled")||this.querySelectorAll(pr).length<2||(this.flkty=new window.theme.Flickity(this.slider,{cellSelector:pr,contain:!0,wrapAround:!0,watchCSS:!0,autoPlay:!1,draggable:!1,pageDots:!1,prevNextButtons:!0}),this.flkty.pausePlayer(),this.addEventListener("mouseenter",(()=>{this.flkty.unpausePlayer()})),this.addEventListener("mouseleave",(()=>{this.flkty.pausePlayer()})))}mouseEnterActions(){this.hovered=!0,this.videoActions()}mouseLeaveActions(){this.hovered=!1,this.videoActions()}videoActions(){const t=this.querySelector(fr),e=this.querySelector(wr),s=t||e,i=this.querySelector(br);if(s){let t=this.hovered?"playVideo":"pauseVideo",i=`{"event":"command","func":"${t}","args":""}`;e&&(t=this.hovered?"play":"pause",i=`{"method":"${t}"}`),s.contentWindow.postMessage(i,"*"),s.addEventListener("load",(t=>{this.videoActions()}))}else i&&(this.hovered?i.play():i.pause())}constructor(){super(),this.flkty=null,this.slider=this.querySelector(vr),this.handleScroll=this.handleScroll.bind(this),this.recentlyViewed=this.closest(gr),this.hovered=!1,this.mouseEnterEvent=()=>this.mouseEnterActions(),this.mouseLeaveEvent=()=>this.mouseLeaveActions(),this.addEventListener("mouseenter",this.mouseEnterEvent),this.addEventListener("mouseleave",this.mouseLeaveEvent)}};const Lr={added:"is-added",animated:"is-animated",disabled:"is-disabled",error:"has-error",loading:"is-loading",open:"is-open",overlayText:"product-item--overlay-text",visible:"is-visible",siblingLinkCurrent:"sibling__link--current"},Er="[data-animation]",Sr="[data-api-content]",Ar="[data-quick-add-btn]",kr="[data-add-to-cart]",Cr='button, [href], select, textarea, [tabindex]:not([tabindex="-1"])',qr="[data-message-error]",Tr="[data-quick-add-modal-handle]",Mr="[data-product-upsell-ajax]",xr="[data-quick-add-modal-close]",Ir="data-grid-item",Hr="[data-product-information]",Or="[data-quick-add-holder]",Pr="[data-quick-add-modal]",Dr="[data-quick-add-modal-template]",Fr="closing",_r="data-product-id",$r="data-quick-add-modal-handle",Br="data-sibling-swapper",Wr="data-quick-add-holder";let Nr=class extends HTMLElement{connectedCallback(){this.modalButton&&this.modalButton.addEventListener("click",this.modalButtonClickEvent),this.buttonATC&&this.buttonATC.addEventListener("click",(t=>{t.preventDefault(),window.a11y.lastElement=this.buttonATC,document.dispatchEvent(new CustomEvent("theme:cart:add",{detail:{button:this.buttonATC}}))})),this.quickAddHolder&&(this.quickAddHolder.addEventListener("animationend",this.quickAddLoadingToggle),this.errorHandler())}modalButtonClickEvent(t){t.preventDefault();const e=this.modalButton.hasAttribute(Br),s=this.modalButton.classList.contains(Lr.siblingLinkCurrent);s||(this.modalButton.classList.add(Lr.loading),this.modalButton.disabled=!0,e&&!s&&(this.currentModal=t.target.closest(Pr),this.currentModal.classList.add(Lr.loading)),this.renderModal())}modalCreate(t){const e=document.querySelector(`${Pr}[${_r}="${this.productId}"]`);if(e)this.modal=e,this.modalOpen();else{const e=this.quickAddHolder.querySelector(Dr);if(!e)return;const s=document.createElement("div");s.innerHTML=e.innerHTML,document.body.appendChild(s.querySelector(Pr)),e.remove(),this.modal=document.querySelector(`${Pr}[${_r}="${this.productId}"]`),this.modal.querySelector(Mr).innerHTML=(new DOMParser).parseFromString(t,"text/html").querySelector(Sr).innerHTML,this.modalCreatedCallback()}}modalOpen(){this.currentModal&&this.currentModal.dispatchEvent(new CustomEvent("theme:modal:close",{bubbles:!1})),"function"==typeof this.modal.show&&this.modal.show(),this.modal.setAttribute("open",!0),this.modal.removeAttribute("inert"),this.quickAddHolder.classList.add(Lr.disabled),this.modalButton&&(this.modalButton.classList.remove(Lr.loading),this.modalButton.disabled=!1,window.a11y.lastElement=this.modalButton),requestAnimationFrame((()=>{this.modal.querySelectorAll(Er).forEach((t=>{t.classList.add(Lr.animated)}))})),document.dispatchEvent(new CustomEvent("theme:quick-add:open",{bubbles:!0})),document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),document.addEventListener("theme:product:added",this.modalCloseOnProductAdded,{once:!0})}modalClose(){if(!this.isAnimating){if(!this.modal.hasAttribute(Fr))return this.modal.setAttribute(Fr,""),void(this.isAnimating=!0);"function"==typeof this.modal.close?this.modal.close():this.modal.removeAttribute("open"),this.modal.removeAttribute(Fr),this.modal.setAttribute("inert",""),this.modal.classList.remove(Lr.loading),this.modalButton&&(this.modalButton.disabled=!1),this.quickAddHolder&&this.quickAddHolder.classList.contains(Lr.disabled)&&this.quickAddHolder.classList.remove(Lr.disabled),this.resetAnimatedItems(),window.theme.hasOpenModals()||document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0})),document.removeEventListener("theme:product:added",this.modalCloseOnProductAdded),this.a11y.removeTrapFocus(),this.a11y.autoFocusLastElement()}}modalEvents(){var t;null===(t=this.modal.querySelector(xr))||void 0===t||t.addEventListener("click",(t=>{t.preventDefault(),this.modalClose()})),this.modal.addEventListener("click",(t=>{"DIALOG"===t.target.nodeName&&"click"===t.type&&this.modalClose()})),this.modal.addEventListener("keydown",(t=>{"Escape"==t.code&&(t.preventDefault(),this.modalClose())})),this.modal.addEventListener("theme:modal:close",(()=>{this.modalClose()})),this.modal.addEventListener("animationend",(t=>{t.target===this.modal&&(this.isAnimating=!1,this.modal.hasAttribute(Fr)?this.modalClose():setTimeout((()=>{this.a11y.trapFocus(this.modal);const t=this.modal.querySelector("[autofocus]")||this.modal.querySelector(Cr);null==t||t.focus()}),50))}))}modalCloseOnProductAdded(){this.resetQuickAddButtons(),this.modal&&this.modal.hasAttribute("open")&&this.modalClose()}quickAddLoadingToggle(t){t.target==this.quickAddHolder&&this.quickAddHolder.classList.remove(Lr.disabled)}errorHandler(){this.quickAddHolder.addEventListener("theme:cart:error",(t=>{const e=t.detail.holder,s=e.closest(`[${Ir}]`);if(!s)return;const i=e.querySelector(qr),o=s.classList.contains(Lr.overlayText),r=s.querySelector(Hr),n=e.querySelector(kr);if(n){n.classList.remove(Lr.added,Lr.loading),e.classList.add(Lr.error);const t=()=>{this.resetQuickAddButtons(),o&&r.classList.remove(Lr.hidden),e.removeEventListener("animationend",t)};e.addEventListener("animationend",t)}i&&(i.innerText=t.detail.description),o&&r.classList.add(Lr.hidden)}))}resetQuickAddButtons(){this.quickAddHolder&&this.quickAddHolder.classList.remove(Lr.visible,Lr.error),this.buttonQuickAdd&&(this.buttonQuickAdd.classList.remove(Lr.added),this.buttonQuickAdd.disabled=!1)}renderModal(){this.modal?this.modalOpen():window.fetch(`${window.theme.routes.root}products/${this.handle}?section_id=api-product-upsell`).then(this.upsellErrorsHandler).then((t=>t.text())).then((t=>{this.modalCreate(t)}))}modalCreatedCallback(){this.modalEvents(),this.modalOpen(),E(this.modal)}upsellErrorsHandler(t){return t.ok?t:t.json().then((function(e){throw new B({status:t.statusText,headers:t.headers,json:e})}))}resetAnimatedItems(){var t;null===(t=this.modal)||void 0===t||t.querySelectorAll(Er).forEach((t=>{t.classList.remove(Lr.animated)}))}constructor(){var t;(super(),this.quickAddHolder=this.querySelector(Or),this.quickAddHolder)&&(this.modal=null,this.currentModal=null,this.productId=this.quickAddHolder.getAttribute(Wr),this.modalButton=this.quickAddHolder.querySelector(Tr),this.handle=null===(t=this.modalButton)||void 0===t?void 0:t.getAttribute($r),this.buttonQuickAdd=this.quickAddHolder.querySelector(Ar),this.buttonATC=this.quickAddHolder.querySelector(kr),this.button=this.modalButton||this.buttonATC,this.modalClose=this.modalClose.bind(this),this.modalCloseOnProductAdded=this.modalCloseOnProductAdded.bind(this),this.a11y=window.theme.a11y,this.isAnimating=!1,this.modalButtonClickEvent=this.modalButtonClickEvent.bind(this),this.quickAddLoadingToggle=this.quickAddLoadingToggle.bind(this))}};customElements.get("quick-add-product")||customElements.define("quick-add-product",Nr),customElements.get("grid-swatch")||customElements.define("grid-swatch",dr),customElements.get("hover-images")||customElements.define("hover-images",yr);const Rr="[data-button-arrow]",Ur="[data-deferred-media-button]",Vr="model-viewer, video, iframe, button, [href], input, [tabindex]",jr="[data-image-id]",zr="[data-product-media-list]",Jr="[data-section-type]",Xr="slider__arrows",Qr="is-dragging",Yr="is-focused",Kr="media--active",Gr="media--hidden",Zr="media--hiding",tn="data-active-media",en="data-button-prev",sn="data-button-next",on="data-image-id",rn="data-media-id",nn="data-type",an="data-fader-desktop",ln="data-fader-mobile";function cn(t){return t.replace(/http(s)?:/,"")}customElements.get("product-images")||customElements.define("product-images",class extends HTMLElement{connectedCallback(){1!==Object.keys(this.productMediaItems).length&&(this.productMediaObserver(),this.toggleEvents(),this.listen(),this.setHeight())}disconnectedCallback(){this.unlisten()}listen(){document.addEventListener("theme:resize:width",this.toggleEvents),document.addEventListener("theme:resize:width",this.setHeight),this.addEventListener("theme:media:select",this.selectMediaEvent)}unlisten(){document.removeEventListener("theme:resize:width",this.toggleEvents),document.removeEventListener("theme:resize:width",this.setHeight),this.removeEventListener("theme:media:select",this.selectMediaEvent)}toggleEvents(){const t=window.theme.isMobile();t&&this.hasAttribute(ln)||!t&&this.hasAttribute(an)?this.bindEventListeners():this.unbindEventListeners()}bindEventListeners(){this.initialized||(this.productMediaList.addEventListener("mousedown",this.handleMouseDown),this.productMediaList.addEventListener("mouseleave",this.handleMouseLeave),this.productMediaList.addEventListener("mouseup",this.handleMouseUp),this.productMediaList.addEventListener("mousemove",this.handleMouseMove),this.productMediaList.addEventListener("touchstart",this.handleMouseDown,{passive:!0}),this.productMediaList.addEventListener("touchend",this.handleMouseUp,{passive:!0}),this.productMediaList.addEventListener("touchmove",this.handleMouseMove,{passive:!0}),this.productMediaList.addEventListener("keyup",this.handleKeyUp),this.initArrows(),this.resetScrollPosition(),this.initialized=!0)}unbindEventListeners(){this.initialized&&(this.productMediaList.removeEventListener("mousedown",this.handleMouseDown),this.productMediaList.removeEventListener("mouseleave",this.handleMouseLeave),this.productMediaList.removeEventListener("mouseup",this.handleMouseUp),this.productMediaList.removeEventListener("mousemove",this.handleMouseMove),this.productMediaList.removeEventListener("touchstart",this.handleMouseDown),this.productMediaList.removeEventListener("touchend",this.handleMouseUp),this.productMediaList.removeEventListener("touchmove",this.handleMouseMove),this.productMediaList.removeEventListener("keyup",this.handleKeyUp),this.removeArrows(),this.initialized=!1)}handleMouseDown(t){this.isDown=!0,this.startX=(t.pageX||t.changedTouches[0].screenX)-this.offsetLeft,this.startY=(t.pageY||t.changedTouches[0].screenY)-this.offsetTop}handleMouseLeave(){this.isDown&&(this.isDown=!1)}handleMouseUp(t){const e=(t.pageX||t.changedTouches[0].screenX)-this.offsetLeft,s=(t.pageY||t.changedTouches[0].screenY)-this.offsetTop,i=e-this.startX,o=s-this.startY,r=i>0?1:-1,n=this.getCurrentMedia().hasAttribute(nn)&&"image"===this.getCurrentMedia().getAttribute(nn);Math.abs(i)>10&&Math.abs(i)>Math.abs(o)&&n&&(r<0?this.showNextImage():this.showPreviousImage()),this.isDown=!1,requestAnimationFrame((()=>{this.classList.remove(Qr)}))}handleMouseMove(){this.isDown&&this.classList.add(Qr)}handleKeyUp(t){"ArrowLeft"===t.code&&this.showPreviousImage(),"ArrowRight"===t.code&&this.showNextImage()}handleArrowsClickEvent(){var t;null===(t=this.querySelectorAll(Rr))||void 0===t||t.forEach((t=>{t.addEventListener("click",(t=>{t.preventDefault(),t.target.hasAttribute(en)&&this.showPreviousImage(),t.target.hasAttribute(sn)&&this.showNextImage()}))}))}resetScrollPosition(){0!==this.productMediaList.scrollLeft&&(this.productMediaList.scrollLeft=0)}initArrows(){if(!this.buttons.length){const t=document.createElement("div");t.classList.add(Xr),t.innerHTML=theme.sliderArrows.prev+theme.sliderArrows.next,this.productMediaList.append(t),this.buttons=this.querySelectorAll(Rr),this.buttonPrev=this.querySelector(`[${en}]`),this.buttonNext=this.querySelector(`[${sn}]`)}this.handleArrowsClickEvent(),this.preloadImageOnArrowHover()}removeArrows(){var t;null===(t=this.querySelector(`.${Xr}`))||void 0===t||t.remove()}preloadImageOnArrowHover(){var t,e;null===(t=this.buttonPrev)||void 0===t||t.addEventListener("mouseover",(()=>{const t=this.getPreviousMediaId();this.preloadImage(t)})),null===(e=this.buttonNext)||void 0===e||e.addEventListener("mouseover",(()=>{const t=this.getNextMediaId();this.preloadImage(t)}))}preloadImage(t){var e;null===(e=this.querySelector(`[${rn}="${t}"] img`))||void 0===e||e.setAttribute("loading","eager")}showMediaOnVariantSelect(t){const e=t.detail.id;this.setActiveMedia(e)}getCurrentMedia(){return this.querySelector(`${jr}.${Kr}`)}getNextMediaId(){const t=this.getCurrentMedia(),e=(null==t?void 0:t.nextElementSibling.hasAttribute(on))?null==t?void 0:t.nextElementSibling:this.querySelector(jr);return null==e?void 0:e.getAttribute(rn)}getPreviousMediaId(){const t=this.getCurrentMedia(),e=this.productMediaItems.length-1,s=(null==t?void 0:t.previousElementSibling)||this.productMediaItems[e];return null==s?void 0:s.getAttribute(rn)}showNextImage(){const t=this.getNextMediaId();this.selectMedia(t)}showPreviousImage(){const t=this.getPreviousMediaId();this.selectMedia(t)}selectMedia(t){this.dispatchEvent(new CustomEvent("theme:media:select",{detail:{id:t}}))}setActiveMedia(t){if(!t)return;this.setAttribute(tn,t);const e=this.querySelector(`${jr}.${Kr}`),s=this.querySelector(`[${rn}="${t}"]`),i=null==s?void 0:s.querySelector(Vr),o=s.querySelector("deferred-media");var r;(null==e||e.classList.add(Zr),null==e||e.classList.remove(Kr),null==s||s.classList.remove(Zr,Gr),null==s||s.classList.add(Kr),o&&!0!==o.getAttribute("loaded"))&&(null===(r=s.querySelector(Ur))||void 0===r||r.dispatchEvent(new Event("click",{bubbles:!1})));requestAnimationFrame((()=>{this.setHeight(),document.body.classList.contains(Yr)&&(null==i||i.focus())}))}setHeight(){var t,e;const s=(null===(t=this.querySelector(`${jr}.${Kr}`))||void 0===t?void 0:t.offsetHeight)||(null===(e=this.productMediaItems[0])||void 0===e?void 0:e.offsetHeight);this.style.setProperty("--height",`${s}px`)}productMediaObserver(){this.productMediaItems.forEach((t=>{t.addEventListener("transitionend",(e=>{e.target==t&&t.classList.contains(Zr)&&(t.classList.remove(Zr),t.classList.add(Gr))})),t.addEventListener("transitioncancel",(e=>{e.target==t&&t.classList.contains(Zr)&&(t.classList.remove(Zr),t.classList.add(Gr))}))}))}constructor(){super(),this.initialized=!1,this.buttons=!1,this.isDown=!1,this.startX=0,this.startY=0,this.scrollLeft=0,this.onButtonArrowClick=t=>this.buttonArrowClickEvent(t),this.container=this.closest(Jr),this.handleMouseDown=this.handleMouseDown.bind(this),this.handleMouseLeave=this.handleMouseLeave.bind(this),this.handleMouseUp=this.handleMouseUp.bind(this),this.handleMouseMove=this.handleMouseMove.bind(this),this.handleKeyUp=this.handleKeyUp.bind(this),this.productMediaItems=this.querySelectorAll(jr),this.productMediaList=this.querySelector(zr),this.setHeight=this.setHeight.bind(this),this.toggleEvents=this.toggleEvents.bind(this),this.selectMediaEvent=t=>this.showMediaOnVariantSelect(t)}});const dn={productCutline:"[data-product-cutline]",productLink:"[data-product-link]",productGridItem:"[data-grid-item]",productInfo:"[data-product-information]",productImage:"[data-product-image-default]",productImageSibling:"[data-product-image-sibling]",productPrice:"[data-product-price]",siblingCount:"[data-sibling-count]",siblingFieldset:"[data-sibling-fieldset]",siblingLink:"[data-sibling-link]"},hn="is-visible",un="is-fade",pn="no-events",mn="is-active",vn="data-sibling-cutline",gn="data-sibling-image",bn="data-sibling-link",wn="data-sibling-price",fn="data-product-link";let yn=class extends HTMLElement{connectedCallback(){this.product=this.closest(dn.productGridItem),this.siblingCount=this.querySelector(dn.siblingCount),this.siblingFieldset=this.querySelector(dn.siblingFieldset),this.siblingLinks=this.querySelectorAll(dn.siblingLink),this.productInfo=this.closest(dn.productInfo),this.productLink=this.closest(dn.link),this.hideSwatchesTimer=0,this.swatchesStyle=theme.settings.collectionSwatchStyle,this.siblingFieldset&&this.productInfo&&("grid"!=this.swatchesStyle&&"slider"!=this.swatchesStyle&&"limited"!=this.swatchesStyle||this.siblingFieldset.classList.add(hn),this.siblingCount&&(this.siblingCount.addEventListener("mouseenter",(()=>this.showSiblings())),this.productInfo.addEventListener("mouseleave",(()=>this.hideSiblings())))),this.siblingLinks.length&&new class{init(){this.cacheDefaultValues(),this.product.addEventListener("mouseleave",(()=>this.resetProductValues())),this.swatches.forEach((t=>{t.addEventListener("mouseenter",(t=>this.showSibling(t)))})),this.productLinks.length&&this.swatches.forEach((t=>{t.addEventListener("click",(()=>{this.productLinks[0].click()}))}))}cacheDefaultValues(){this.productLinkValue=this.productLinks[0].hasAttribute(fn)?this.productLinks[0].getAttribute(fn):"",this.productPriceValue=this.productPrice.innerHTML,this.productCutline&&(this.productCutlineValue=this.productCutline.innerHTML)}resetProductValues(){this.product.classList.remove(mn),this.productLinkValue&&this.productLinks.forEach((t=>{t.href=this.productLinkValue})),this.productPrice&&(this.productPrice.innerHTML=this.productPriceValue),this.productCutline&&this.productCutline&&(this.productCutline.innerHTML=this.productCutlineValue,this.productCutline.title=this.productCutlineValue),this.hideSiblingImage()}showSibling(t){const e=t.target,s=e.hasAttribute(bn)?e.getAttribute(bn):"",i=e.hasAttribute(wn)?e.getAttribute(wn):"",o=e.hasAttribute(vn)?e.getAttribute(vn):"",r=e.hasAttribute(gn)?e.getAttribute(gn):"";s&&this.productLinks.forEach((t=>{t.href=s})),i&&(this.productPrice.innerHTML=`<span class="price">${i}</span>`),this.productCutline&&(o?(this.productCutline.innerHTML=o,this.productCutline.title=o):(this.productCutline.innerHTML="",this.productCutline.title="")),r&&this.showSiblingImage(r)}showSiblingImage(t){if(!this.productImageSibling)return;const e=window.devicePixelRatio||1,s=this.productImage.offsetWidth*e,i=function(t,e){if(null===e)return t;if("master"===e)return cn(t);const s=t.match(/\.(jpg|jpeg|gif|png|bmp|bitmap|tiff|tif)(\?v=\d+)?$/i);if(s){const i=t.split(s[0]),o=s[0];return cn(`${i[0]}_${e}${o}`)}return null}(t,180*Math.ceil(s/180)+"x"),o=this.productImageSibling.querySelector(`[src="${i}"]`),r=()=>{this.productImageSibling.classList.add(hn),this.productImageSibling.querySelector(`[src="${i}"]`).classList.add(un)},n=()=>{this.productImageSibling.querySelectorAll("img").forEach((t=>{t.classList.remove(un)})),requestAnimationFrame(r)};if(o)n();else{const t=document.createElement("img");t.src=i,this.productCutline&&(t.alt=this.productCutline.innerText),t.addEventListener("load",(()=>{this.productImageSibling.append(t),n()}))}}hideSiblingImage(){this.productImageSibling&&(this.productImageSibling.classList.remove(hn),this.productImageSibling.querySelectorAll("img").forEach((t=>{t.classList.remove(un)})))}constructor(t,e){this.swatches=t,this.product=e,this.productLinks=this.product.querySelectorAll(dn.productLink),this.productCutline=this.product.querySelector(dn.productCutline),this.productPrice=this.product.querySelector(dn.productPrice),this.productImage=this.product.querySelector(dn.productImage),this.productImageSibling=this.product.querySelector(dn.productImageSibling),this.init()}}(this.siblingLinks,this.product)}showSiblings(){this.hideSwatchesTimer&&clearTimeout(this.hideSwatchesTimer),this.productLink&&this.productLink.classList.add(pn),"text"!=this.swatchesStyle&&this.siblingFieldset.classList.add(hn)}hideSiblings(){this.hideSwatchesTimer=setTimeout((()=>{this.productLink&&this.productLink.classList.remove(pn),this.siblingFieldset.classList.remove(hn)}),100)}constructor(){super()}};customElements.get("product-siblings")||customElements.define("product-siblings",yn),Shopify.Products=function(){const t={howManyToShow:4,howManyToStoreInMemory:10,wrapperId:"recently-viewed-products",section:null,target:"api-product-grid-item",onComplete:null};let e=[],s=null,i=null;const o=new Date,r=new Date;r.setTime(o.getTime()+7776e6);const n={configuration:{expires:r.toGMTString(),path:"/",domain:window.location.hostname,sameSite:"none",secure:!0},name:"shopify_recently_viewed",write:function(t){const e=encodeURIComponent(t.join(" "));document.cookie=`${this.name}=${e}; expires=${this.configuration.expires}; path=${this.configuration.path}; domain=${this.configuration.domain}; sameSite=${this.configuration.sameSite}; secure=${this.configuration.secure}`},read:function(){let t=[],e=null;return-1!==document.cookie.indexOf("; ")&&document.cookie.split("; ").find((t=>t.startsWith(this.name)))&&(e=document.cookie.split("; ").find((t=>t.startsWith(this.name))).split("=")[1]),null!==e&&(t=decodeURIComponent(e).split(" ")),t},destroy:function(){document.cookie=`${this.name}=null; expires=${this.configuration.expires}; path=${this.configuration.path}; domain=${this.configuration.domain}`},remove:function(t){const e=this.read(),s=e.indexOf(t);-1!==s&&(e.splice(s,1),this.write(e))}},a=(e,s,o,r,l,c)=>{s.length&&e<c?fetch(`${window.theme.routes.root}products/${s[0]}?section_id=${l}`).then((t=>t.text())).then((t=>{const i=100*e,n=o.id?`#${o.id}`:"",d=document.createElement("div");let h=t;h=h.includes("||itemAnimationDelay||")?h.replaceAll("||itemAnimationDelay||",i):h,h=h.includes("||itemAnimationAnchor||")?h.replaceAll("||itemAnimationAnchor||",n):h,d.innerHTML=h,o.innerHTML+=d.querySelector("[data-api-content]").innerHTML,s.shift(),e++,a(e,s,o,r,l,c)})).catch((()=>{n.remove(s[0]),s.shift(),a(e,s,o,r,l,c)})):((e,s)=>{e.classList.remove("hidden");const o=n.read().length;if(Shopify.recentlyViewed&&i&&o&&o<i&&e.children.length){let t=[],s=[],i=0;for(const e in Shopify.recentlyViewed){i+=1;const o=Shopify.recentlyViewed[e].split(" "),r=parseInt(e.split("_")[1]);t=[...t,...o],(n.read().length===r||i===Object.keys(Shopify.recentlyViewed).length&&!s.length)&&(s=[...s,...o])}for(let i=0;i<e.children.length;i++){const o=e.children[i];t.length&&o.classList.remove(...t),s.length&&o.classList.add(...s)}}if(t.onComplete)try{t.onComplete(e,s)}catch(t){console.log(t)}})(o,r)};return{showRecentlyViewed:function(o){const r=o||{};Object.assign(t,r),e=n.read(),s=document.querySelector(`#${t.wrapperId}`),i=t.howManyToShow,t.howManyToShow=Math.min(e.length,t.howManyToShow),t.howManyToShow&&s&&a(0,e,s,t.section,t.target,i)},getConfig:function(){return t},clearList:function(){n.destroy()},recordRecentlyViewed:function(e){const s=e||{};Object.assign(t,s);let i=n.read();if(-1!==window.location.pathname.indexOf("/products/")){let e=decodeURIComponent(window.location.pathname).match(/\/products\/([a-z0-9\-]|[\u3000-\u303F]|[\u3040-\u309F]|[\u30A0-\u30FF]|[\uFF00-\uFFEF]|[\u4E00-\u9FAF]|[\u2605-\u2606]|[\u2190-\u2195]|[\u203B]|[\w\u0430-\u044f]|[\u0400-\u04FF]|[\u0900-\u097F]|[\u0590-\u05FF\u200f\u200e]|[\u0621-\u064A\u0660-\u0669 ])+/)[0].split("/products/")[1];t.handle&&(e=t.handle);const s=i.indexOf(e);-1===s?(i.unshift(e),i=i.splice(0,t.howManyToStoreInMemory)):(i.splice(s,1),i.unshift(e)),n.write(i)}},hasProducts:n.read().length>0}}();const Ln="[data-aos]",En=".collection-item__image",Sn="[data-column-image]",An=".flickity-button.next",kn=".flickity-button.previous",Cn="a:not(.btn)",qn=".product-item__image",Tn="[data-section-type]",Mn="[data-slide]",xn="[data-slider-thumb]",In="data-arrow-position-middle",Hn="data-slide-index",On="data-options",Pn="data-slide-text-color",Dn="aos-animate",Fn="desktop",_n="is-focused",$n="hidden",Bn="is-initialized",Wn="is-loading",Nn="is-selected",Rn="mobile",Un="single-slide";customElements.get("slider-component")||customElements.define("slider-component",class extends HTMLElement{connectedCallback(){this.initSlider()}initSlider(){var t;if(this.slides.length<=1)return;this.hasAttribute(On)&&(this.customOptions=JSON.parse(decodeURIComponent(this.getAttribute(On)))),this.classList.add(Wn);let e=Mn;const s=!window.theme.isMobile(),i=`${Mn}:not(.${Rn})`,o=`${Mn}:not(.${Fn})`;var r;(this.querySelectorAll(i).length||this.querySelectorAll(o).length)&&(e=s?i:o,null===(r=this.flkty)||void 0===r||r.destroy());if(this.querySelectorAll(e).length<=1)return this.classList.add(Un),void this.classList.remove(Wn);this.sliderOptions={cellSelector:e,contain:!0,wrapAround:!0,adaptiveHeight:!0,...this.customOptions,on:{ready:()=>{requestAnimationFrame((()=>{this.classList.add(Bn),this.classList.remove(Wn),this.parentNode.dispatchEvent(new CustomEvent("theme:slider:loaded",{bubbles:!0,detail:{slider:this}}))})),this.slideActions(),this.sliderOptions.prevNextButtons&&this.positionArrows()},change:t=>{const e=this.slides[t];if(!e||this.sliderOptions.groupCells)return;const s=e.querySelectorAll(Ln);s.length&&s.forEach((t=>{t.classList.remove(Dn),requestAnimationFrame((()=>{setTimeout((()=>{t.classList.add(Dn)}),0)}))}))},resize:()=>{this.sliderOptions.prevNextButtons&&this.positionArrows()}}},this.initFlickity(),this.flkty.on("change",(()=>this.slideActions(!0))),null===(t=this.thumbs)||void 0===t||t.forEach((t=>{t.addEventListener("click",(e=>{e.preventDefault();const s=[...t.parentElement.children].indexOf(t);this.flkty.select(s)}))})),this.flkty&&this.flkty.isActive||this.classList.remove(Wn)}initFlickity(){this.sliderOptions.fade?this.flkty=new window.theme.FlickityFade(this,this.sliderOptions):this.flkty=new window.theme.Flickity(this,this.sliderOptions)}bindEvents(){this.addEventListener("theme:slider:init",(()=>{this.initSlider()})),this.addEventListener("theme:slider:select",(t=>{this.flkty.selectCell(t.detail.index),this.flkty.stopPlayer()})),this.addEventListener("theme:slider:deselect",(()=>{this.flkty&&this.sliderOptions.hasOwnProperty("autoPlay")&&this.sliderOptions.autoPlay&&this.flkty.playPlayer()})),this.addEventListener("theme:slider:reposition",(()=>{var t;null===(t=this.flkty)||void 0===t||t.reposition()})),this.addEventListener("theme:slider:destroy",(()=>{var t;null===(t=this.flkty)||void 0===t||t.destroy()})),this.addEventListener("theme:slider:remove-slide",(t=>{var e,s;t.detail.slide&&(null===(e=this.flkty)||void 0===e||e.remove(t.detail.slide),0===(null===(s=this.flkty)||void 0===s?void 0:s.cells.length)&&this.section.classList.add($n))}))}slideActions(t=!1){const e=this.querySelector(`.${Nn}`);if(!e)return;const s=e.hasAttribute(Pn)?e.getAttribute(Pn):"",i=e.querySelector(Cn),o=this.querySelectorAll(`${Mn} a, ${Mn} button`);if(document.body.classList.contains(_n)&&i&&this.sliderOptions.groupCells&&t&&i.focus(),o.length&&o.forEach((t=>{const e=t.closest(Mn);if(e){const s=e.classList.contains(Nn)?0:-1;t.setAttribute("tabindex",s)}})),this.style.setProperty("--text",s),this.thumbs.length&&this.thumbs.length===this.slides.length&&e.hasAttribute(Hn)){const t=parseInt(e.getAttribute(Hn)),s=this.querySelector(`${xn}.${Nn}`);s&&s.classList.remove(Nn),this.thumbs[t].classList.add(Nn)}}positionArrows(){if(!this.hasAttribute(In)||!this.sliderOptions.prevNextButtons)return;const t=this.querySelector(En)||this.querySelector(qn)||this.querySelector(Sn);t&&(this.querySelector(kn).style.top=t.clientHeight/2+"px",this.querySelector(An).style.top=t.clientHeight/2+"px")}disconnectedCallback(){this.flkty&&(this.flkty.options.watchCSS=!1,this.flkty.destroy())}constructor(){super(),this.flkty=null,this.slides=this.querySelectorAll(Mn),this.thumbs=this.querySelectorAll(xn),this.section=this.closest(Tn),this.bindEvents()}});const Vn="[data-related-section]",jn="[data-aos]",zn="[data-tab]",Jn=".tab-link__recent",Xn=".tab-content",Qn="current",Yn="hidden",Kn="aos-animate",Gn="aos-no-transition",Zn="is-focused",ta="data-tab",ea="data-tab-index";customElements.get("tabs-component")||customElements.define("tabs-component",class extends HTMLElement{connectedCallback(){const t=this.querySelectorAll(zn);this.addEventListener("theme:tab:check",(()=>this.checkRecentTab())),this.addEventListener("theme:tab:hide",(()=>this.hideRelatedTab())),null==t||t.forEach((t=>{const e=parseInt(t.getAttribute(ta)),s=this.querySelector(`${Xn}-${e}`);t.addEventListener("click",(()=>{this.tabChange(t,s)})),t.addEventListener("keyup",(e=>{"Space"!==e.code&&"Enter"!==e.code||!document.body.classList.contains(Zn)||this.tabChange(t,s)}))}))}tabChange(t,e){if(t.classList.contains(Qn))return;const s=this.querySelector(`${zn}.${Qn}`),i=this.querySelector(`${Xn}.${Qn}`);null==s||s.classList.remove(Qn),null==i||i.classList.remove(Qn),t.classList.add(Qn),e.classList.add(Qn),t.classList.contains(Yn)&&e.classList.add(Yn),this.a11y.a11y.removeTrapFocus(),this.dispatchEvent(new CustomEvent("theme:tab:change",{bubbles:!0})),t.dispatchEvent(new CustomEvent("theme:form:sticky",{bubbles:!0,detail:{element:"tab"}})),this.animateItems(e)}animateItems(t,e=!0){const s=t.querySelectorAll(jn);s.length&&s.forEach((t=>{t.classList.remove(Kn),e&&(t.classList.add(Gn),requestAnimationFrame((()=>{t.classList.remove(Gn),t.classList.add(Kn)})))}))}checkRecentTab(){const t=this.querySelector(Jn);if(t){t.classList.remove(Yn);const e=parseInt(t.getAttribute(ta)),s=this.querySelector(`${Xn}[${ea}="${e}"]`);s&&(s.classList.remove(Yn),this.animateItems(s,!1))}}hideRelatedTab(){const t=this.querySelector(Vn);if(!t)return;const e=t.closest(`${Xn}.${Qn}`);if(!e)return;const s=parseInt(e.getAttribute(ea)),i=this.querySelectorAll(zn);if(i.length>s){const t=i[s].nextSibling;t&&(i[s].classList.add(Yn),t.dispatchEvent(new Event("click")))}}constructor(){super(),this.a11y=window.a11y}});const sa="[data-actions]",ia="[data-content]",oa="[data-button]",ra="data-height",na="is-open",aa="is-enabled";let la=class extends HTMLElement{connectedCallback(){this.setHeight(this.initialHeight),this.trigger.addEventListener("click",(()=>{this.setHeight(this.content.offsetHeight),this.classList.add(na)})),this.setHeight(this.initialHeight),this.toggleActions(),document.addEventListener("theme:resize",this.toggleActions),document.addEventListener("theme:collapsible:toggle",this.toggleActions)}disconnectedCallback(){document.removeEventListener("theme:resize",this.toggleActions),document.removeEventListener("theme:collapsible:toggle",this.toggleActions)}setHeight(t){this.style.setProperty("--height",`${t}px`)}toggleActions(){this.classList.toggle(aa,this.content.offsetHeight+this.actions.offsetHeight>this.initialHeight)}constructor(){super(),this.initialHeight=this.getAttribute(ra),this.content=this.querySelector(ia),this.trigger=this.querySelector(oa),this.actions=this.querySelector(sa),this.toggleActions=this.toggleActions.bind(this)}};customElements.get("toggle-ellipsis")||customElements.define("toggle-ellipsis",la);const ca="[data-section-id]",da="data-tooltip",ha="data-tooltip-stop-mouseenter",ua="tooltip-default",pa="is-visible",ma="is-hiding";customElements.get("tooltip-component")||customElements.define("tooltip-component",class extends HTMLElement{connectedCallback(){if(!document.querySelector(`.${ua}`)){const t=`<div class="${ua}__arrow"></div><div class="${ua}__inner"><div class="${ua}__text"></div></div>`,e=document.createElement("div");e.className=ua,e.innerHTML=t,document.body.appendChild(e)}this.addEventListener("mouseenter",this.addPinMouseEvent),this.addEventListener("mouseleave",this.removePinMouseEvent),this.addEventListener("theme:tooltip:init",this.addPinEvent),document.addEventListener("theme:tooltip:close",this.removePinEvent)}addPin(t=!1){const e=document.querySelector(`.${ua}`),s=this.closest(ca),i=Array.from(s.classList).find((t=>t.startsWith("color-scheme-")));if(null==e||e.classList.add(i),this.label&&e&&(t&&!this.hasAttribute(ha)||!t)){const t=e.querySelector(`.${ua}__arrow`),s=e.querySelector(`.${ua}__inner`);e.querySelector(`.${ua}__text`).innerHTML=this.label;const i=s.offsetWidth,o=this.getBoundingClientRect(),r=o.top,n=o.width,a=r+o.height+window.scrollY;let l=o.left-i/2+n/2;const c=24,d=l+i-window.theme.getWindowWidth()+c;d>0&&(l-=d),l<0&&(l=0),t.style.left=`${o.left+n/2}px`,e.style.setProperty("--tooltip-top",`${a}px`),s.style.transform=`translateX(${l}px)`,e.classList.remove(ma),e.classList.add(pa),document.addEventListener("theme:scroll",this.removePinEvent)}}removePin(t,e=!1,s=!1){const i=document.querySelector(`.${ua}`),o=i.classList.contains(pa);i&&(e&&!this.hasAttribute(ha)||!e)&&(o&&(s||t.detail.hideTransition)&&(i.classList.add(ma),this.hideTransitionTimeout&&clearTimeout(this.hideTransitionTimeout),this.hideTransitionTimeout=setTimeout((()=>{i.classList.remove(ma)}),this.transitionSpeed)),i.classList.remove(pa),document.removeEventListener("theme:scroll",this.removePinEvent))}disconnectedCallback(){this.removeEventListener("mouseenter",this.addPinMouseEvent),this.removeEventListener("mouseleave",this.removePinMouseEvent),this.removeEventListener("theme:tooltip:init",this.addPinEvent),document.removeEventListener("theme:tooltip:close",this.removePinEvent),document.removeEventListener("theme:scroll",this.removePinEvent)}constructor(){super(),this.label=this.hasAttribute(da)?this.getAttribute(da):"",this.transitionSpeed=200,this.hideTransitionTimeout=0,this.addPinEvent=()=>this.addPin(),this.addPinMouseEvent=()=>this.addPin(!0),this.removePinEvent=t=>window.theme.throttle(this.removePin(t),50),this.removePinMouseEvent=t=>this.removePin(t,!0,!0)}});const va={};function ga(t={}){if(t.type||(t.type="json"),t.url)return va[t.url]?va[t.url]:function(t,e){const s=new Promise(((s,i)=>{"text"===e?fetch(t).then((t=>t.text())).then((t=>{s(t)})).catch((t=>{i(t)})):function(t,e,s){let i=document.getElementsByTagName("head")[0],o=!1,r=document.createElement("script");r.src=t,r.onload=r.onreadystatechange=function(){o||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState?s():(o=!0,e())},i.appendChild(r)}(t,(function(){s()}),(function(){i()}))}));return va[t]=s,s}(t.url,t.type);if(t.json)return va[t.json]?Promise.resolve(va[t.json]):window.fetch(t.json).then((t=>t.json())).then((e=>(va[t.json]=e,e)));if(t.name){const e="".concat(t.name,t.version);return va[e]?va[e]:function(t){const e="".concat(t.name,t.version),s=new Promise(((e,s)=>{try{window.Shopify.loadFeatures([{name:t.name,version:t.version,onLoad:t=>{!function(t,e,s){s?e(s):t()}(e,s,t)}}])}catch(t){s(t)}}));return va[e]=s,s}(t)}return Promise.reject()}document.addEventListener("DOMContentLoaded",(function(){const t=document.querySelector("[data-scroll-top-button]");t&&(t.addEventListener("click",(()=>{window.scrollTo({top:0,left:0,behavior:"smooth"})})),document.addEventListener("theme:scroll",(()=>{t.classList.toggle("is-visible",window.scrollY>window.innerHeight)}))),window.self!==window.top&&document.querySelector("html").classList.add("iframe"),"scrollBehavior"in document.documentElement.style||ga({url:window.theme.assets.smoothscroll})})),window.navigator.cookieEnabled&&(document.documentElement.className=document.documentElement.className.replace("supports-no-cookies","supports-cookies"))}(themeVendor.ScrollLock);
