!function(){"use strict";customElements.get("addresses-component")||customElements.define("addresses-component",class extends HTMLElement{constructor(){super(),this.addressNewForm=this.querySelector("#AddressNewForm")}connectedCallback(){this.addressNewForm&&(this.customerAddresses(),this.querySelectorAll(".address-new-toggle")?.forEach((e=>{e.addEventListener("click",(()=>{this.addressNewForm.classList.toggle("hidden")}))})),this.querySelectorAll(".address-edit-toggle").forEach((e=>{e.addEventListener("click",(()=>{const t=e.getAttribute("data-form-id");this.querySelector(`#EditAddress_${t}`).classList.toggle("hidden")}))})),this.querySelectorAll(".address-delete")?.forEach((e=>{e.addEventListener("click",(()=>{const t=e.getAttribute("data-form-id"),s=e.getAttribute("data-confirm-message");confirm(s)&&Shopify.postLink(window.theme.routes.addresses_url+"/"+t,{parameters:{_method:"delete"}})}))})))}customerAddresses(){Shopify.CountryProvinceSelector&&new Shopify.CountryProvinceSelector("AddressCountryNew","AddressProvinceNew",{hideElement:"AddressProvinceContainerNew"});this.querySelectorAll(".address-country-option").forEach((e=>{const t=e.getAttribute("data-form-id"),s=`AddressCountry_${t}`,r=`AddressProvince_${t}`,d=`AddressProvinceContainer_${t}`;new Shopify.CountryProvinceSelector(s,r,{hideElement:d})}))}})}();
