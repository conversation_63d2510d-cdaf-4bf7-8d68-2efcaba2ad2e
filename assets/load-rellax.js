!function(){"use strict";const e={};function t(t={}){if(t.type||(t.type="json"),t.url)return e[t.url]?e[t.url]:function(t,n){const o=new Promise(((e,o)=>{"text"===n?fetch(t).then((e=>e.text())).then((t=>{e(t)})).catch((e=>{o(e)})):function(e,t,n){let o=document.getElementsByTagName("head")[0],s=!1,r=document.createElement("script");r.src=e,r.onload=r.onreadystatechange=function(){s||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState?n():(s=!0,t())},o.appendChild(r)}(t,(function(){e()}),(function(){o()}))}));return e[t]=o,o}(t.url,t.type);if(t.json)return e[t.json]?Promise.resolve(e[t.json]):window.fetch(t.json).then((e=>e.json())).then((n=>(e[t.json]=n,n)));if(t.name){const n="".concat(t.name,t.version);return e[n]?e[n]:function(t){const n="".concat(t.name,t.version),o=new Promise(((e,n)=>{try{window.Shopify.loadFeatures([{name:t.name,version:t.version,onLoad:t=>{!function(e,t,n){n?t(n):e()}(e,n,t)}}])}catch(e){n(e)}}));return e[n]=o,o}(t)}return Promise.reject()}window.theme.LoadRellax=window.theme.LoadRellax||null;window.theme.LoadRellax=class{constructor(e,n,o=""){const s={center:!0,round:!0,frame:e};this.options=""!==o?o:s,this.selector=n,t({url:window.theme.assets.rellax}).then((()=>this.init())).catch((e=>console.error(e)))}init(){const e=window.themeRellax?.Rellax||window.Rellax;this.rellaxInstance=new e(this.selector,this.options)}refresh(){this.rellaxInstance&&"function"==typeof this.rellaxInstance.refresh?this.rellaxInstance.refresh():console.warn("Rellax instance is not initialized yet.")}}}();
