!function(){"use strict";const e="[data-newsletter-form]",t="[data-newsletter-heading]",s="has-success",o="has-error",i="hidden";customElements.get("newsletter-component")||customElements.define("newsletter-component",class extends HTMLElement{constructor(){super(),this.newsletter=this.querySelector(e),this.sessionStorage=window.sessionStorage,this.stopSubmit=!0,this.formID=null}connectedCallback(){"/challenge"!==window.location.pathname&&(this.newsletterSubmit=e=>this.newsletterSubmitEvent(e),this.newsletter.addEventListener("submit",this.newsletterSubmit),this.showMessage())}newsletterSubmitEvent(e){this.stopSubmit&&(e.preventDefault(),e.stopImmediatePropagation(),this.removeStorage(),this.writeStorage(),this.stopSubmit=!1,this.newsletter.submit())}writeStorage(){void 0!==this.sessionStorage&&this.sessionStorage.setItem("newsletter_form_id",this.newsletter.id)}readStorage(){this.formID=this.sessionStorage.getItem("newsletter_form_id")}removeStorage(){this.sessionStorage.removeItem("newsletter_form_id")}showMessage(){if(this.readStorage(),this.newsletter.id===this.formID){const e=document.getElementById(this.formID),n=e.parentElement.querySelector(t),r=-1!==window.location.search.indexOf("?customer_posted=true"),a=-1!==window.location.search.indexOf("accepts_marketing");r?(e.classList.remove(o),e.classList.add(s),n&&(n.classList.add(i),e.classList.remove(i))):a&&(e.classList.remove(s),e.classList.add(o),n&&(n.classList.add(i),e.classList.remove(i))),(r||a)&&window.addEventListener("load",(()=>{this.scrollToForm(e)}))}}scrollToForm(e){const t=e.getBoundingClientRect();t.top>=0&&t.left>=0&&t.bottom<=window.theme.getWindowHeight()&&t.right<=window.theme.getWindowWidth()||setTimeout((()=>{window.theme.scrollTo(e.getBoundingClientRect().top)}),500)}})}();
