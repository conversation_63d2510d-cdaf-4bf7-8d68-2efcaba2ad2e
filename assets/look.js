!function(){"use strict";const t="[data-slider-mobile]",e="[data-slide]",s="[data-slider-thumb]",o="[data-popup-container]",i="[data-popup-close]",n="is-animating",p="is-open",l="data-slider-thumb";customElements.get("look-component")||customElements.define("look-component",class extends HTMLElement{constructor(){super(),this.slider=this.querySelector(t),this.slides=this.querySelectorAll(e),this.thumbs=this.querySelectorAll(s),this.popupContainer=this.querySelector(o),this.popupClose=this.querySelectorAll(i),this.popupCloseByEvent=this.popupCloseByEvent.bind(this)}connectedCallback(){this.slider&&this.slides.length&&this.thumbs.length&&(this.popupContainer.addEventListener("transitionend",(t=>{t.target==this.popupContainer&&(this.popupContainer.classList.remove(n),t.target.classList.contains(p)?this.popupOpenCallback():this.popupCloseCallback())})),this.popupContainer.addEventListener("transitionstart",(t=>{t.target==this.popupContainer&&this.popupContainer.classList.add(n)})),this.popupClose.forEach((t=>{t.addEventListener("click",(()=>{this.popupContainer.classList.remove(p),document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0}))}))})),this.thumbs.forEach(((t,e)=>{t.addEventListener("click",(s=>{s.preventDefault();const o=t.hasAttribute(l)&&""!==t.getAttribute(l)?parseInt(t.getAttribute(l)):e,i=this.slides[o];if(window.theme.isMobile()){const t=parseInt(window.getComputedStyle(this.slider).paddingLeft);this.slider.scrollTo({top:0,left:i.offsetLeft-t,behavior:"auto"}),document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),this.popupContainer.classList.add(n,p)}else{const{stickyHeaderHeight:t}=window.theme.readHeights(),e=i.getBoundingClientRect().top,s=i.offsetHeight/2,o=window.innerHeight;let n=e+s-o/2+window.scrollY;const p=this.getBoundingClientRect().top+window.scrollY,l=p+this.offsetHeight;n<p?n=p-t:n+o>l&&(n=l-o),window.scrollTo({top:n,left:0,behavior:"smooth"})}}))})))}popupCloseByEvent(){this.popupContainer.classList.remove(p)}popupOpenCallback(){document.addEventListener("theme:quick-add:open",this.popupCloseByEvent,{once:!0}),document.addEventListener("theme:product:added",this.popupCloseByEvent,{once:!0})}popupCloseCallback(){document.removeEventListener("theme:quick-add:open",this.popupCloseByEvent,{once:!0}),document.removeEventListener("theme:product:added",this.popupCloseByEvent,{once:!0})}})}();
