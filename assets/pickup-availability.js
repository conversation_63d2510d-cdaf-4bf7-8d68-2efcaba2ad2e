!function(){"use strict";function t(t){this.status=t.status||null,this.headers=t.headers||null,this.json=t.json||null,this.body=t.body||null}t.prototype=Error.prototype;const e="data-store-availability-container",i=".shopify-section",a="[data-pickup-drawer]",s="[data-section-type]",n="hidden";class r extends HTMLElement{constructor(){super()}connectedCallback(){this.container=this.closest(s),this.drawer=null,this.container.addEventListener("theme:variant:change",(t=>this.fetchPickupAvailability(t))),this.fetchPickupAvailability()}fetchPickupAvailability(t){if(t&&!t.detail.variant||t&&t.detail.variant&&!t.detail.variant.available)return void this.classList.add(n);const s=t&&t.detail.variant?t.detail.variant.id:this.getAttribute(e);s&&fetch(`${window.theme.routes.root}variants/${s}/?section_id=api-pickup-availability`).then(this.handleErrors).then((t=>t.text())).then((t=>{const e=(new DOMParser).parseFromString(t,"text/html").querySelector(i).innerHTML;this.innerHTML=e,this.drawer=this.querySelector(a),this.drawer?this.classList.remove(n):this.classList.add(n)})).catch((t=>{console.error(t)}))}handleErrors(e){return e.ok?e:e.json().then((function(i){throw new t({status:e.statusText,headers:e.headers,json:i})}))}}customElements.get("pickup-availability")||customElements.define("pickup-availability",r)}();
