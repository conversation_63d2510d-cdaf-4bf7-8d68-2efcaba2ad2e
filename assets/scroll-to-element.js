!function(){"use strict";const t="[data-scroll-to]",e="[data-tooltip]",o="[data-collapsible-trigger]",l="open",n="data-scroll-to",s="data-tooltip-stop-mouseenter";customElements.get("scroll-to-element")||customElements.define("scroll-to-element",class extends HTMLElement{constructor(){super(),this.scrollToButton=this.querySelector(t)}connectedCallback(){this.scrollToButton&&this.scrollToButton.addEventListener("click",(()=>{const t=document.querySelector(this.scrollToButton.getAttribute(n));t&&"A"!==this.scrollToButton.tagName&&this.scrollToElement(t)}))}scrollToElement(t){window.theme.scrollTo(t.getBoundingClientRect().top+1);const n=t.nextElementSibling.matches("details")?t.nextElementSibling:null;if(n){const t=n?.querySelector(o);n.hasAttribute(l)||t?.dispatchEvent(new Event("click"))}const c=document.querySelectorAll(`${e}:not([${s}])`);c.length&&c.forEach((t=>{t.setAttribute(s,""),setTimeout((()=>{t.removeAttribute(s)}),1e3)}))}})}();
