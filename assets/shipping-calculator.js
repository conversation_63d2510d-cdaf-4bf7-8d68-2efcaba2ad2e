!function(){"use strict";var t=["DA","DE","EN","ES","FR","IT","JA","NL","PT","PT_BR"];function e(e){var n=e.replace(/-/,"_").toUpperCase();return-1!==t.indexOf(n)?n:-1!==t.indexOf(n.substring(0,2))?n.substring(0,2):"EN"}var n=/({\w+})/g,r={lastName:'[name="address[last_name]"]',firstName:'[name="address[first_name]"]',company:'[name="address[company]"]',address1:'[name="address[address1]"]',address2:'[name="address[address2]"]',country:'[name="address[country]"]',zone:'[name="address[province]"]',postalCode:'[name="address[zip]"]',city:'[name="address[city]"]',phone:'[name="address[phone]"]'};function s(t,n,s){n=n||"en";var o=function(t,e){var n={};return Object.keys(r).forEach((function(r){var s=t.querySelector(e[r]);n[r]=s?{wrapper:s.parentElement,input:s,labels:document.querySelectorAll('[for="'+s.id+'"]')}:{}})),n}(t,function(){for(var t=Object({}),e=0;e<arguments.length;e++){var n=arguments[e];if(n)for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}(r,(s=s||{inputSelectors:{}}).inputSelectors));return function(t){Object.keys(t).forEach((function(e){var n=t[e].input,r=t[e].labels;if(n){if("object"!=typeof n)throw new TypeError(t[e]+" is missing an input or select.");if("object"!=typeof r)throw new TypeError(t[e]+" is missing a label.")}}))}(o),function(t){if(!t)return Promise.resolve(null);return fetch(location.origin+"/meta.json").then((function(t){return t.json()})).then((function(t){return-1!==t.ships_to_countries.indexOf("*")?null:t.ships_to_countries})).catch((function(){return null}))}(s.shippingCountriesOnly).then((function(r){return function(t){return fetch("https://country-service.shopifycloud.com/graphql",{method:"POST",headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*"},body:JSON.stringify({query:"query countries($locale: SupportedLocale!) {  countries(locale: $locale) {    name    code    labels {      address1      address2      city      company      country      firstName      lastName      phone      postalCode      zone    }    formatting {      edit    }    zones {      name      code    }  }}",operationName:"countries",variables:{locale:e(t)}})}).then((function(t){return t.json()})).then((function(t){return t.data.countries}))}(n).then((function(e){!function(t,e,n){!function(t,e){var n=t.country.input,r=n.cloneNode(!0);e.forEach((function(t){var e=document.createElement("option");e.value=t.code,e.textContent=t.name,r.appendChild(e)})),n.innerHTML=r.innerHTML,n.dataset.default&&(n.value=n.dataset.default)}(e,n);var r=e.country.input?e.country.input.value:null;(function(t,e,n){e.country.input.addEventListener("change",(function(r){i(t,e,r.target.value,n)}))})(t,e,n),i(t,e,r,n)}(t,o,function(t,e){if(!e)return t;return t.filter((function(t){return-1!==e.indexOf(t.code)}))}(e,r))}))}))}function i(t,e,r,s){var i=function(t,e){return t=t||"CA",e.filter((function(e){return e.code===t}))[0]}(r,s);!function(t,e){Object.keys(t).forEach((function(n){t[n].labels.forEach((function(t){t.textContent=e.labels[n]}))}))}(e,i),function(t,e,r){var s=r.formatting.edit,i=e.country.wrapper,o=!1;(a=s,a.split("_").map((function(t){var e=t.match(n);return e?e.map((function(t){var e=t.replace(/[{}]/g,"");switch(e){case"zip":return"postalCode";case"province":return"zone";default:return e}})):[]}))).forEach((function(n){n.forEach((function(r){e[r].wrapper.dataset.lineCount=n.length,e[r].wrapper&&("country"!==r?o?t.append(e[r].wrapper):t.insertBefore(e[r].wrapper,i):o=!0)}))}));var a}(t,e,i),function(t,e){var n=t.zone;if(!n)return;if(0===e.zones.length)return n.wrapper.dataset.ariaHidden="true",void(n.input.innerHTML="");n.wrapper.dataset.ariaHidden="false";var r=n.input,s=r.cloneNode(!0);s.innerHTML="",e.zones.forEach((function(t){var e=document.createElement("option");e.value=t.code,e.textContent=t.name,s.appendChild(e)})),r.innerHTML=s.innerHTML,r.dataset.default&&(r.value=r.dataset.default)}(e,i)}const o=(t,e=[],n=!1)=>{const r=Object.keys(t).map((r=>{let s=t[r];if("[object Object]"===Object.prototype.toString.call(s)||Array.isArray(s))return Array.isArray(t)?e.push(""):e.push(r),o(s,e,Array.isArray(s));{let t=r;if(e.length>0){t=(n?e:[...e,r]).reduce(((t,e)=>""===t?e:`${t}[${e}]`),"")}return n?`${t}[]=${s}`:`${t}=${s}`}})).join("&");return e.pop(),r};function a(t){this.status=t.status||null,this.headers=t.headers||null,this.json=t.json||null,this.body=t.body||null}a.prototype=Error.prototype;const c="[data-template-no-shipping]",u=".get-rates",l="#address_container",h="#address_country",p="#address_province",d="#address_zip",f="#wrapper-response",m="#shipping-calculator-response-template",g="data-template-no-shipping",y="data-default",v="is-hidden",C="error",b="center",E="success",w="disabled",B="get-rates--trigger",A="Error : country is not supported.",L="We do not ship to this destination.",S="Error : ";class j extends HTMLElement{constructor(){super(),this.getRatesButton=this.querySelector(u),this.fieldsContainer=this.querySelector(l),this.selectCountry=this.querySelector(h),this.selectProvince=this.querySelector(p),this.template=this.querySelector(m),this.wrapper=this.querySelector(f),this.onCountryChangeEvent=()=>this.onCountryChange(),this.onButtonClickEvent=()=>this.onButtonClick()}connectedCallback(){const t=document.querySelector("html");let e="en";if(t.hasAttribute("lang")&&""!==t.getAttribute("lang")&&(e=t.getAttribute("lang")),this.fieldsContainer&&s(this.fieldsContainer,e,{shippingCountriesOnly:!0}),this.selectCountry&&this.selectCountry.hasAttribute(y)&&this.selectProvince&&this.selectProvince.hasAttribute(y)&&this.selectCountry.addEventListener("change",this.onCountryChangeEvent),this.getRatesButton&&(this.getRatesButton.addEventListener("click",this.onButtonClickEvent),theme.settings.customerLoggedIn&&this.getRatesButton.classList.contains(B))){const t=document.querySelector(d);t&&t.value&&this.getRatesButton.dispatchEvent(new Event("click"))}}disconnectedCallback(){this.selectCountry&&this.selectCountry.hasAttribute(y)&&this.selectProvince&&this.selectProvince.hasAttribute(y)&&this.selectCountry.removeEventListener("change",this.onCountryChangeEvent),this.getRatesButton&&this.getRatesButton.removeEventListener("click",this.onButtonClickEvent)}onCountryChange(){this.selectCountry.removeAttribute(y),this.selectProvince.removeAttribute(y)}onButtonClick(){for(this.disableButtons();this.wrapper.firstChild;)this.wrapper.removeChild(this.wrapper.firstChild);this.wrapper.classList.add(v);const t={};let e=this.selectCountry.value,n=this.selectProvince.value;const r=this.selectCountry.getAttribute(y);""===e&&r&&""!==r&&(e=r);const s=this.selectProvince.getAttribute(y);""===n&&s&&""!==s&&(n=s),t.zip=document.querySelector(d).value||"",t.country=e||"",t.province=n||"",this.getCartShippingRatesForDestination(t)}formatRate(t){return"0.00"===t?window.theme.strings.free:window.theme.formatMoney(t,theme.moneyFormat)}render(t){if(this.template&&this.wrapper){this.wrapper.innerHTML="";let e="",n="",r=`${C} ${b}`,s=this.template.innerHTML;const i=/[^[\]]+(?=])/g;if(t.rates&&t.rates.length){let n=i.exec(s)[0];t.rates.forEach((t=>{let r=n;r=r.replace(/\|\|rateName\|\|/,t.name),r=r.replace(/\|\|ratePrice\|\|/,this.formatRate(t.price)),e+=r}))}if(t.success){r=`${E} ${b}`;const e=document.createElement("div");e.innerHTML=this.template.innerHTML;const s=e.querySelector(c);t.rates.length<1&&s&&(n=s.getAttribute(g))}else n=t.errorFeedback;s=s.replace(i,"").replace("[]",""),s=s.replace(/\|\|ratesList\|\|/g,e),s=s.replace(/\|\|successClass\|\|/g,r),s=s.replace(/\|\|ratesText\|\|/g,n),this.wrapper.innerHTML+=s,this.wrapper.classList.remove(v)}}enableButtons(){this.getRatesButton.removeAttribute("disabled"),this.getRatesButton.classList.remove(w),this.getRatesButton.textContent=theme.strings.shippingCalcSubmitButton}disableButtons(){this.getRatesButton.setAttribute("disabled","disabled"),this.getRatesButton.classList.add(w),this.getRatesButton.textContent=theme.strings.shippingCalcSubmitButtonDisabled}getCartShippingRatesForDestination(t){const e=encodeURI(o({shipping_address:t})),n=`${theme.routes.cart_url}/shipping_rates.json?${e}`;fetch(n).then(this.handleErrors).then((t=>t.text())).then((e=>{const n=JSON.parse(e).shipping_rates;this.onCartShippingRatesUpdate(n,t)})).catch((t=>{this.onError(t.json)}))}fullMessagesFromErrors(t){const e=[];for(const n in t)for(const r of t[n])e.push(r);return e}handleErrors(t){return t.ok?t:t.json().then((function(e){throw new a({status:t.statusText,headers:t.headers,json:e})}))}onError(t){this.enableButtons();let e="";e=t.message?t.message+"("+t.status+"): "+t.description:S+this.fullMessagesFromErrors(t).join("; "),e===A&&(e=L),this.render({rates:[],errorFeedback:e,success:!1})}onCartShippingRatesUpdate(t,e){this.enableButtons();let n="";e.zip&&(n+=e.zip+", "),e.province&&(n+=e.province+", "),n+=e.country,this.render({rates:t,address:n,success:!0})}}customElements.get("shipping-calculator")||customElements.define("shipping-calculator",j)}();
