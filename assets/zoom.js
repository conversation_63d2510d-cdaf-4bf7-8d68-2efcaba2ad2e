!function(){"use strict";const t="[data-zoom-caption]",e="[data-zoom-image]",o="[data-pswp-thumbs-template]",s="[data-section-type]",i=".pswp__thumbs",n="product-images",a="product-component",c="is-dragging",l="variant--soldout",r="variant--unavailable",m="pswp-zoom-gallery",h="pswp-zoom-gallery--single",d="data-image-src",p="data-image-width",u="data-image-height";class g extends HTMLElement{constructor(){super(),this.container=this.closest(s),this.images=this.querySelectorAll(e),this.zoomCaptions=this.container.querySelector(t),this.thumbsContainer=document.querySelector(i)}connectedCallback(){this.images.forEach(((t,e)=>{t.addEventListener("click",(o=>{o.preventDefault(),t.closest(n).classList.contains(c)||(this.createZoom(e),window.a11y.lastElement=t)})),t.addEventListener("keyup",(o=>{"Enter"===o.code&&(o.preventDefault(),this.createZoom(e),window.a11y.lastElement=t)}))}))}createZoom(t){const e=this.container.querySelector(o)?.innerHTML;let s=[],i=0;this.images.forEach((o=>{const n=o.getAttribute(d);if(i+=1,s.push({src:n,w:parseInt(o.getAttribute(p)),h:parseInt(o.getAttribute(u)),msrc:n}),this.images.length===i){const o=this.getColorScheme(),n={history:!1,focus:!1,index:t,mainClass:`${m}${1===i?` ${h}`:""}${o?` ${o}`:""}`,showHideOpacity:!0,howAnimationDuration:150,hideAnimationDuration:250,closeOnScroll:!1,closeOnVerticalDrag:!1,captionEl:!0,closeEl:!0,closeElClasses:["caption-close","title"],tapToClose:!1,clickToCloseNonZoomable:!1,maxSpreadZoom:2,loop:!0,spacing:0,allowPanToNext:!0,pinchToClose:!1,addCaptionHTMLFn:(t,e,o)=>{this.zoomCaption(t,e,o)},getThumbBoundsFn:()=>{const e=this.images[t],o=window.scrollY||document.documentElement.scrollTop,s=e.getBoundingClientRect();return{x:s.left,y:s.top+o,w:s.width}}};new window.theme.LoadPhotoswipe(s,n),this.thumbsContainer&&""!==e&&(this.thumbsContainer.innerHTML=e)}}))}zoomCaption(t,e){let o="";const s=e.children[0];return this.zoomCaptions&&(o=this.zoomCaptions.innerHTML,this.zoomCaptions.closest(`.${l}`)?s.classList.add(l):s.classList.remove(l),this.zoomCaptions.closest(`.${r}`)?s.classList.add(r):s.classList.remove(r)),s.innerHTML=o,!1}getColorScheme(){const t=this.closest(a);return Array.from(t.classList).find((t=>t.startsWith("color-scheme")))}}customElements.get("zoom-images")||customElements.define("zoom-images",g)}();
