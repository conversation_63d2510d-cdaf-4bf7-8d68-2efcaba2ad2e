/* ==================== Variables ==================== */

$iterations: 5; // Used to generate classes


/* ==================== Layout ==================== */

@each $breakpoint in $breakpoints {

  $name: nth($breakpoint, 1);
  $declaration: nth($breakpoint, 2);

  @include respond-to($name) {
    .hidden--#{$name} {
      display: none !important;
    }
    .visually-hidden--#{$name} {
      @include visually-hidden();
    }
    .no-padding--#{$name} {
      padding: 0 !important;
    }
  }

}

// Layout

.no-margin {
  margin: 0 !important;
}

@each $direction in $layout-directions {
  .no-margin--#{$direction} {
    margin-#{$direction}: 0 !important;
  }

  @for $i from 1 through $iterations {
    .margin-#{$direction}--#{$i}0 {
      margin-#{$direction}: #{$i}0px !important;
    }
  }
}

.no-padding {
  padding: 0 !important;
}

@each $direction in $layout-directions {
  .no-padding--#{$direction} {
    padding-#{$direction}: 0 !important;
  }

  @for $i from 1 through $iterations {
    .padding-#{$direction}--#{$i}0 {
      padding-#{$direction}: #{$i}0px !important;
    }
  }
}

/* --- Overflow --- */

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}



/* ==================== Typography ==================== */

/* --- Text Directions --- */

@each $direction in $text-directions {
  .text-align--#{$direction} {
    text-align: #{$direction} !important;
  }

  .text-align--#{$direction}--mobile {
    @include respond-to($small-down) {
      text-align: #{$direction} !important;
    }
  }
}

/* --- Text Style --- */

// Style

.text--subdued {
  opacity: 0.7;
}

// Font Weight

.strong,
.font-weight--bold {
  font-weight: var(--font-weight-body-bold) !important;
}

.font-weight--normal {
  font-weight: var(--font-weight-body) !important;
}

// Text Transform

.text-transform--uppercase {
  text-transform: uppercase !important;
}

.text-transform--none {
  text-transform: none !important;
}


.italic {
  font-style: italic;
}

.strikethrough {
  text-decoration: line-through;
}


/* ==================== Colors ==================== */

/* --- Text --- */

@each $color in $semiotics {

  .color--#{$color} {
    color: var(---color--#{$color}) !important;
  }

}

/* --- Background --- */

@each $color in $semiotics {

  .background-color--#{$color} {
    background: RGB(var(---color--#{$color}));
  }

}

/* --- Object Position --- */

@each $direction in $position-directions {
  .object-position--#{$direction} {
    object-position: #{$direction} !important;
  }
}


/* --- Flex - Justify --- */

@each $direction in $layout-flex-directions {
  .justify--#{$direction} {
    justify-content: #{$direction} !important;
  }
}

@each $direction in $layout-flex-directions {
  .align--#{$direction} {
    align-items: #{$direction} !important;
  }
}

@each $column in $columns {
  .columns--#{$column} {
    @include respond-to($medium-up) {
      columns: $column;
      gap: var(--spacing-8);
    }
  }
}





/*  ==============================
    Effects
    ============================== */

@each $effect in $prefix-effects {
  @each $size in $suffix-sizes-basic {
    $stub: '';
    @if $size != '' {
      $stub: '-#{$size}';
    }
    .#{$effect}#{$stub} {
      border-radius: var(--#{$effect}#{$stub});
    }
  }
}