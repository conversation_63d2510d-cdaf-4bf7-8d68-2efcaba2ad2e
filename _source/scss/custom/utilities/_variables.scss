/* ==================== BROADCAST THEME 7.0.0 ==================== */

/*  ------------------------------
    Grid Variables
    ------------------------------ */

/**
 * Define your breakpoints. The first value is the prefix that shall be used for
 * your classes (e.g. `.small--one-half`), the second value is the media query
 * that the breakpoint fires at.
 */

$grid-small: 480px;
$grid-medium: 750px;
$grid-large: 990px;
$grid-xl: 1400px;
$grid-xxl: 1600px;

$small: 'small';
$small-down: 'small-down';
$small-up: 'small-up';
$medium: 'medium';
$medium-down: 'medium-down';
$medium-up: 'medium-up';
$large: 'large';
$large-down: 'large-down';
$large-up: 'large-up';
$xlarge: 'xlarge';
$xlarge-down: 'xlarge-down';
$xlarge-up: 'xlarge-up';
$xxlarge: 'xxlarge';
$xxlarge-down: 'xxlarge-down';
$xxlarge-up: 'xxlarge-up';

// The `$breakpoints` list is used to build our media queries.
// You can use these in the media-query mixin.
$breakpoints: (
  $small-down '(max-width: #{$grid-small})',
  $small '(min-width: #{$grid-small}) and (max-width: #{$grid-medium - 1})',
  $small-up '(min-width: #{$grid-small})',
  $medium-down '(max-width: #{$grid-medium})',
  $medium '(min-width: #{$grid-medium}) and (max-width: #{$grid-large - 1})',
  $medium-up '(min-width: #{$grid-medium})',
  $large-down '(max-width: #{$grid-large})',
  $large '(min-width: #{$grid-large}) and (max-width: #{$grid-xl - 1})',
  $large-up '(min-width: #{$grid-large})'
  $xlarge-down '(max-width: #{$grid-xl})',
  $xlarge '(min-width: #{$grid-xl}) and (max-width: #{$grid-xl - 1})',
  $xlarge-up '(min-width: #{$grid-xl})',
  $xxlarge-down '(max-width: #{$grid-xxl})',
  $xxlarge '(min-width: #{$grid-xxl}) and (max-width: #{$grid-xxl - 1})',
  $xxlarge-up '(min-width: #{$grid-xxl})'
);


/**
 * Define which namespaced breakpoints you would like to generate for each of
 * widths, push and pull. This is handy if you only need pull on, say, desk, or
 * you only need a new width breakpoint at mobile sizes. It allows you to only
 * compile as much CSS as you need. All are turned on by default, but you can
 * add and remove breakpoints at will.
 *
 * Push and pull shall only be used if `$push` and/or `$pull` and `$responsive`
 * have been set to ‘true’.
 */
/*

$breakpoint-has-widths: (
  'small',
  'small-up',
  'small-down',
  'medium',
  'medium-up',
  'medium-down',
  'large',
  'large-up',
  'large-down'
  'xlarge',
  'xlarge-up',
  'xlarge-down'
  ) !default;

$breakpoint-has-push: (
  'small',
  'small-up',
  'small-down',
  'medium',
  'medium-up',
  'medium-down',
  'large',
  'large-up',
  'large-down'
  'xlarge',
  'xlarge-up',
  'xlarge-down'
  ) !default;

$breakpoint-has-pull: (
  'small',
  'small-up',
  'small-down',
  'medium',
  'medium-up',
  'medium-down',
  'large',
  'large-up',
  'large-down'
  'xlarge',
  'xlarge-up',
  'xlarge-down'
  ) !default;

*/

/* =============== Colors =============== */

$color-default: 'default';
$color-primary: 'primary';
$color-secondary: 'secondary';
$color-tertiary: 'tertiary';
$color-success: 'success';
$color-warning: 'warning';
$color-danger: 'danger';
$color-info: 'info';
$color-link: 'link';
$color-special: 'special';

$semiotics: (
  $color-default,
  $color-primary,
  $color-secondary,
  $color-tertiary,
  $color-success,
  $color-warning,
  $color-danger,
  $color-info,
  $color-link,
  $color-special
);

/* =============== Layout =============== */

$columns: (
  1,
  2,
  3
);

$text-directions: (
  'left',
  'center',
  'right'
);

$position-directions: (
  'top',
  'bottom',
  'left',
  'right',
  'center'
);

$layout-directions: (
  'top',
  'bottom',
  'left',
  'right'
);

$layout-flex-directions: (
  'start',
  'end',
  'flex-start',
  'flex-end',
  'self-start',
  'self-end',
  'stretch',
  'space-between',
  'space-around',
  'anchor-center'
);

$colors-semiotics: (
  'default',
  'primary',
  'secondary',
  'tertiary',
  'success',
  'warning',
  'danger',
  'info',
  'link'
);

$colors-system: (
  'default',
  'primary',
  'secondary',
  'tertiary'
);

/* =============== Utilites =============== */

$suffix-sizes: (
  'xs',
  'sm',
  '',
  'lg',
  'xl'
);

$suffix-sizes-basic: (
  'sm',
  '',
  'lg'
);

$prefix-effects: (
  'corner-radius',
  'block-radius',
  'section-radius'
);