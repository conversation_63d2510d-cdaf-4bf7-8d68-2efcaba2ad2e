/* ========== Backgrounds ========== */

.background--accent {
  background: var(--accent);
}
.background--accent-fade {
  background: var(--accent-fade);
}
.background--accent-hover {
  background: var(--accent-hover);
}
.background--icons {
  background: var(--icons);
}
.background--bg {
  background: var(--bg);
}
.background--bg-accent {
  background: var(--bg-accent);
}
.background--bg-accent-lighten {
  background: var(--bg-accent-lighten);
}
.background--bg-accent-darken {
  background: var(--bg-accent-darken);
}


/* ========== Borders ========== */

.border-color {
  background: var(--border);
}

.border-color--dark {
  background: var(--border-dark);
}

.border-color--light {
  background: var(--border-light);
}

.border-color--hairline {
  background: var(--border-hairline);
}


/* ========== Colors ========== */

.color--icons {
  color: var(--icons, currentColor);
}
.color--link {
  color: var(--link, currentColor);
}
.color--link-a50 {
  color: var(--link-a50, currentColor);
}
.color--link-a70 {
  color: var(--link-a70, currentColor);
}
.color--link-hover {
  color: var(--link-hover, currentColor);
}
.color--link-opposite {
  color: var(--link-opposite, currentColor);
}
.color--text {
  color: var(--text, currentColor);
}
.color--text-dark {
  color: var(--text-dark, currentColor);
}
.color--text-light {
  color: var(--text-light, currentColor);
}
.color--text-hover {
  color: var(--text-hover, currentColor);
}
.color--text-a5 {
  color: var(--text-a5, currentColor);
}
.color--text-a35 {
  color: var(--text-a35, currentColor);
}
.color--text-a50 {
  color: var(--text-a50, currentColor);
}
.color--text-a80 {
  color: var(--text-a80, currentColor);
}