/*

  1. Utilities
  2. Responsive
  3. UI Elements
    3.1. Buttons

*/

/*  ==============================
    1. Utilities
    ============================== */

@mixin hide-scrollbars() {
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
  scrollbar-width: none;

  /* Firefox */
  &::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
  }
}

@mixin clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }

  // sass-lint:disable
  *zoom: 1;
}

@mixin visually-hidden() {
  // sass-lint:disable no-important
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  max-width: 1px;
  max-height: 1px;
  font-size: 0;
  margin: -1px;
  padding: 0;
  border: 0;
}

@mixin visually-shown($position: inherit) {
  // sass-lint:disable no-important
  position: $position !important;
  overflow: auto;
  clip: auto;
  width: auto;
  height: auto;
  margin: 0;
}

/*  ==============================
    2. Responsive
    ============================== */

@mixin respond-to($media-query) {
  $breakpoint-found: false;

  @each $breakpoint in $breakpoints {
    $name: nth($breakpoint, 1);
    $declaration: nth($breakpoint, 2);

    @if $media-query ==$name and $declaration {
      $breakpoint-found: true;

      @media only screen and #{$declaration} {
        @content;
      }
    }
  }

  @if $breakpoint-found ==false {
    @warn 'Breakpoint "#{$media-query}" does not exist';
  }
}

/*  ==============================
    3. UI Elements
    ============================== */

/*  ------------------------------
    3.1. Buttons
    ------------------------------ */

/* ------------------------------
   Headings
   ------------------------------ */

@mixin headings() {

  // Headings

  .h0,
  h1,
  .h1,
  h2,
  .h2,
  h3,
  .h3,
  h4,
  .h4,
  h5,
  .h5 {

    // --text-color: var(--heading-color, var(---color-heading));
    // --text-shadow: var(---text-knockout);

    // color: RGB(var(--text-color));

  }

  .h0 {
    line-height: 1;
  }

  .h1,
  h1 {
    font-family: var(--heading-font-family);
    font-weight: 400;
    letter-spacing: -0.05em;
  }

  .h2,
  h2 {
    font-family: var(--heading-font-family);
  }

  .h3,
  h3 {
    font-family: var(--heading-font-family);
  }

  .h4,
  h4 {
    font-family: var(--heading-font-family);
  }

  .h5,
  h5 {
    font-family: var(--text-font-family);
  }

  .h6,
  h6 {
    font-family: var(--heading-font-family);
  }



  // Subheadings

  p.bold,
  .subheading {

    --text-color: var(--subheading-color, var(--text-color));

  }

  .subheading {
    font-size: var(--text-subheading);
  }

  .subheading--small {
    font-size: var(--text-subheading-small);
  }

  .subheading--large {
    font-size: var(--text-subheading-large);
  }

}

@mixin heading-style() {}

@mixin subheading-style() {}


/* ------------------------------
   Labels
   ------------------------------ */

@mixin label-structure() {}

@mixin label-style() {}

/* ------------------------------
   Inputs
   ------------------------------ */

@mixin input-structure() {}

@mixin input() {}

@mixin input--large() {}

@mixin input-style() {}

@mixin button-style() {

  font-family: var(--heading-font-family-alt);
  text-transform: uppercase;

  transition:
    color 0.25s,
    outline-color 0.25s,
    background-color 0.25s;

  &:hover {
    color: RGB(var(--button-background));
    background-color: RGB(var(--button-text-color));
  }

  outline-width: 1px;
  outline-style: solid;
  outline-color: RGB(var(--button-background));
  outline-offset: 2px;

  &:hover {
    color: RGB(var(--button-background));
    background-color: RGB(var(--button-text-color));
    outline-color: RGB(var(--button-text-color));
  }

}

/*  ------------------------------
    3.3. Shopify
    ------------------------------ */

@mixin when-logged-in {

  body.logged-in & {
    @content;
  }

}

@mixin when-logged-out {

  body.logged-out & {
    @content;
  }

}