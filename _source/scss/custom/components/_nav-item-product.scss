.nav-item-product {
  display: flex;

  border-radius: var(--block-radius);

  background-color: var(--product-item-image-background-color);
  overflow: hidden;
}

.nav-item-product__inner {
  display: flex;
  align-items: center;
  justify-content: stretch;
  width: 100%;
  gap: var(--gap);
  padding: var(--inner);
}

.nav-item-product__image {
  min-width: 70px;
  width: 70px;

  img {
    // width: 100%;
    max-height: 80px;
    width: 70px;
    object-fit: cover;
    height: auto;
    mix-blend-mode: darken;
  }
}

.nav-item-product__info {
  display: flex;
  flex-direction: column;
  gap: 0.25em;
  // padding: var(--inner);
  padding-left: 0;
}

.nav-item-product__price {
  margin-top: 0.5em;
}