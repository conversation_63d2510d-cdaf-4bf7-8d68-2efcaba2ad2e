[data-collapsible-trigger] {
  .icon {
    right: 0;
  }
}

.accordion {
  border-top: none;

  +.accordion {
    // border-top: 1px solid var(--border);
  }
}

.accordion__title {
  gap: 0.8em;
  padding: 1rem 0 1rem 0;
}

.accordion-icon {
  &.accordion-icon--number {

    --icon-inner-size: 28px;

    position: relative;
    margin-right: var(--icon-inner-size);
    height: 100%;

    .accordion-icon__inner {

      position: absolute;
      top: 0;

      display: flex;
      align-items: center;
      justify-content: center;
      height: var(--icon-inner-size);
      width: var(--icon-inner-size);
      background: var(--bg-accent);
      border-radius: 100px;

      transform: translateY(-50%);
    }
  }
}