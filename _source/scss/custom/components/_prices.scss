.product-information .price,
.price {
  font-family: var(--font-family-product-price);
  font-weight: var(--font-weight-product-price);
  font-size: var(--font-size-product-card-price);
  color: var(--color-price);
}

.product-information .new-price,
.new-price {
  color: var(--color-price--sale);
}

.product-information .old-price,
.product__price--strike,
.old-price {
  color: var(--color-price--compare);
  text-decoration: line-through;
}

.product-item__price {

}
.product__price__wrap {

}
.product__price {
  
}
.product__sale {

}

.new-price {
  margin-right: 4px;
}