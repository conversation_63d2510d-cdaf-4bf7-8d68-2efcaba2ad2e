.comparison-table {
  display: table;
  width: 100%;
  border: none;

  td, .comparison-table__row {
    padding: 0;
  }

  thead {
    th {
      border: none;
    }
  }

  tr {
    >td {
      border: none;
    }
  }

  td {}

  .comparison-table__head {
    display: table-row;
  }

  .comparison-table__body {
    display: table-row-group;
  }

  .comparison-table__row {

    display: table-row;
    background-color: var(--bg);

    > .comparison-table__cell {
      overflow: hidden;
      
      &:first-child {
        border-top-left-radius: var(--corner-radius);
        border-bottom-left-radius: var(--corner-radius);
        @include respond-to($medium-up) {
          padding-inline-start: 50px;
        }
      }
      &:last-child {
        border-top-right-radius: var(--corner-radius);
        border-bottom-right-radius: var(--corner-radius);
      }

    }

    &:nth-child(2n) {
      background-color: var(--bg-accent);
    }

  }

  .comparison-table__cell {

    --padding-y: 0.7em;
    --padding-x: 0.8em;

    display: table-cell;
    min-height: 40px;
    vertical-align: center;

    padding: var(--padding-y) 0 var(--padding-y) var(--padding-x);

    &:first-child {
      text-align: end;
    }

    &:last-child {
      padding-end: 0;
    }
  }

  .comparison-table__cell--label {
    vertical-align: top;
    // width: min-content;
    width: auto;
  }

  .comparison-table__cell--spacer {
    padding: 0;
    // width: 100%;
  }

  .comparison-table__cell--head-product {
    width: 25%;
    padding-block: 0;
    vertical-align: bottom;

    &:last-child {}
  }

  .comparison-table__cell__label {
    white-space: nowrap;
  }

}