.hero__spacer {
  
  border: 0;
  min-height: 30px;
  margin: auto;

}

.hero__max-width {

  max-width: var(--block-max-width);

  @include respond-to($large-down) {
    max-width: calc(var(--block-max-width) * 1.5);
  }

  @include respond-to($small-down) {
    max-width: none;
  }

}

.hero__content {
  
  padding: calc(var(--gutter) / 2);
  width: 100%;

  @include respond-to($medium-up) {
    padding: var(--gutter);
  }
  
  &.hero__content--compact {
    margin-bottom: 0; // Just a fix for offset margins on teh original hero.

    @include respond-to($medium-up) {
      padding: 0;
    }

  }

}

.hero__description {
  @include respond-to($large-up) {
    max-width: var(--content-max-width, 100%);
    margin-inline: auto;
  }
}

.hero__button {
  margin: 0;
}

