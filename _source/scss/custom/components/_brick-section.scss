/* ========== Brick Section ========== */

.brick__block {
  
  position: relative;

  &.brick__block--mobile-reduce-padding {
  
    @include respond-to($medium-down) {
      padding: var(--outer);

      .hero__spacer {
        display: none;
      }

      .hero__content {
        padding: 0;
      }

      .brick__block__text {
        padding: 0;
      }

      .hero__rte {
        margin-bottom: 0 !important;
      }
    }

  }

  /* ----- Products ----- */

  &.brick__block--products {

    --inner: calc(var(--gutter) / 4);

    @include respond-to($medium-up) {
      --inner: calc(var(--gutter) / 2);
      padding-right: calc(var(--gutter) / 2);
    }

  }

  /* ----- Text ----- */

  &.brick__block--text {

    .brick__block__text {
      margin: 0;
      flex-basis: unset;

      @include respond-to($medium-up) {
        padding: var(--gutter);
      }
    }

    .hero__subheading,
    .hero__rte {
      margin: var(--block-padding-bottom, var(--line)) 0;
    }
  }

  /* ----- Background Image ----- */

  &.brick__block--background-image {

    .hero__content,
    .brick__block__text {
      background: none;
    }

    .brick__block__background-image {
      opacity: var(--background-image-opacity);
    }

    .brick__block__text {
      position: relative;
      z-index: 1;
    }

  }

}

.brick__block__text {

  justify-content: center;
  
}

.brick__block.brick__section__extra-padding {

  @include respond-to($medium-up) {
    padding-right: var(--outer);
  }

  @include respond-to($medium-down) {
    max-width: none;
    margin: 0 auto;
    padding: var(--outer);
  }

}

.brick__block__background-image {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
}