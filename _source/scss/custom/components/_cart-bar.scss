.cart-bar__info {
    gap: var(--gutter);
}

.cart-bar__options {
    display: flex;
    align-items: center;
    gap: var(--gap);
    
    @media (max-width: 1000px) {
        margin-top: 0.75em;
        justify-content: center;
    }
}

.cart-bar__option-text {
    display: flex;
    flex-direction: column;
}


.cart-bar-swatch {
    display: flex;
    gap: 0.5em;
}

.cart-bar-swatch__swatch {
    --swatch-color: var(--bg-accent);
    
    position: relative;
    top: 2px;
    width: 16px;
    height: 16px;
    margin: 0;
    border-radius: 100%;
    background: var(--swatch-color);
}


#PBarNextFrameWrapper {
    display: none;
}