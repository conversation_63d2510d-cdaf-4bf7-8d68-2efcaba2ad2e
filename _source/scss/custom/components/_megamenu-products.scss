.megamenu-products {

  display: grid;
  gap: var(--gap);

  grid-template-columns: repeat(var(--megamenu-collection-columns), minmax(0, 1fr));
}

.megamenu-product {
  display: flex;
  flex-direction: column;

  border-radius: var(--block-radius);
  background-color: var(--product-item-image-background-color);
  overflow: hidden;
}

.megamenu-product__inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: stretch;
  gap: var(--gap);
}

.megamenu-product__image {
  img {
    display: block;
    height: 300px;
    width: 100%;
    object-fit: cover;
    
    transition: transform var(--transition-duration) var(--transition-ease);
    // mix-blend-mode: darken;
  }
}

.megamenu-product__info {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 0.25em;
  padding: var(--inner);
  padding-top: 0;
}

.megamenu-product__price {
  
}

.megamenu-product__cutline {
  margin-bottom: 0.5em;
}


a.megamenu-product {
  &:hover, &:focus {
    .megamenu-product__image {
      img {
        transform: scale(1.05);
      }
    } 
  }
}