.product-upsell {
  --upsell-image-width: 90px;

  min-height: 120px;

  flex-wrap: nowrap;

  .product-upsell__image__thumb {
    padding: 0;
    // @include respond-to($medium-down) {
      // padding-top: var(--PRODUCT-GRID-ASPECT-RATIO--MOBILE);
    // }
  }

  .product-upsell__content {
    padding: calc(var(--gap) / 2) var(--gap);
    width: calc(100% - calc(var(--gap)));
  }

  .product-upsell__holder--button,
  .product-upsell__content {
    padding-right: calc(var(--gap) / 2 + var(--outer)) !important;
  }

  .product-upsell__link {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: var(--gap);
  }

  .product-upsell__title {
    margin-bottom: 0.25em;
  }

  .product-upsell__image {
    flex: 0 0 var(--upsell-image-width);
    width: var(--upsell-image-width);
  }

  .product-upsell__content-bottom {
    margin-top: auto;
  }

  .product-upsell__price {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: calc(var(--gap) / 2);

    > * {
      margin: 0;
    }
  }

}

