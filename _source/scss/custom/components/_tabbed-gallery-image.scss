.tabbed-gallery-image {
  position: relative;
  // min-height: var(--item-height);

  // min-width: 300px;

  width: var(--item-width, 300px);
  height: var(--item-height, auto);

  background: var(--bg);

  .btn__outer {
    bottom: calc(var(--gap) / 2);
    right: calc(var(--gap) / 2);
  }

}

.tabbed-gallery-image--link {

  @media (pointer: fine) {

    transition: opacity var(--transition-duration) var(--transition-ease);

    &:focus,
    &:hover {
      opacity: 0.8;
    }

  }
}