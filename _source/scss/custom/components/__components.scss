/* ----- Content Box ----- */
/*

.content-box {

  --padding: var(--spacing-6);

  padding: var(--padding);
  background: #fff;
  box-shadow: var(--section-shadow);
  border-radius: var(--block-radius);

  @include respond-to($medium-up) {
    --padding: var(--spacing-12);
  }

}
*/

.media-container {
  
  .video__poster {
    height: 100%;
  }

}

/* ----- HRs ----- */

hr, .hr {

  width: 100%;
  margin: 1em 0;
  border-width: 0.5px;
  border-bottom: 0;
  border-style: solid;
  border-color: var(--border);

  &.hr--light {
    border-color: var(--border-light);
  }

  &.hr--dark {
    border-color: var(--border-dark);
  }

  &.hr--clear {
    border-color: transparent;
  }

  &.hr--small {
    margin: 0.5em 0;
  }

}

/* ----- UI Elements ----- */


/* ----- Accordions ----- */


/* ----- Note ----- */

.note {

  --note-color: var(--text);
  --note-background-color: var(--bg-accent);
  --note-border-color: var(--border);
  --note-font-size: var(--font-size-text);

  display: flex;

  // border: 1px solid var(--note-border-color);
  padding: .6em 1em;

  font-size: var(--note-font-size);
  
  // color: RGB(var(--note-color) / 0.8);
  // background-color: RGB(var(--note-background-color));
  
  color: var(--note-color);
  background-color: var(--note-background-color);
  border-radius: var(--corner-radius);

  p {
    color: var(--note-color);

    &:last-child {
      margin: 0;
    }

  }

  /* ----- layout ----- */

  &.note--inline {
    display: inline-flex;
  }

  /* ----- Styles ----- */



  /* ----- Sizes ----- */

  &.note--sm {
    --note-font-size: var(--font-size-text-sm);
  }

}

/* ----- Quotes ----- */

.text-quotes {
  &::before {
    content: "\201C";
  }
  &::after {
    content: "\201D";
  }
}


/* ----- Swatches ----- */

.simple-swatch {
  
  --swatch-size: 16px;
  
  display: inline-flex;
  min-width: var(--swatch-size);
  min-height: var(--swatch-size);
  
  background: var(--swatch-color);
  
  border-radius: 100%;
  
}


/* ----- Links ----- */

.link {
  @include style-link();
}

.link-animated {

  position: relative;

  &:after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: currentColor;
    transition: transform 0.3s cubic-bezier(0.39, 0.575, 0.565, 1);
    transform: scaleX(0);
    transform-origin: left;
  }

  @media (hover: hover) {
    &:hover {
      &:after {
        transform: scaleX(1);
      }
    }
  }

}

.cart-bar {
  z-index: 6001 !important;
}

.span--comma {
  &:not(:last-of-type) {
    &::after {
      content: ', ';
    }
  }
}