.product-carousel-item {
  position: relative;
  min-height: var(--item-height);

  background: var(--bg);

  .btn__outer {
    bottom: calc(var(--gap) / 2);
    right: calc(var(--gap) / 2);
  }

}

.product-carousel-item--link {

  @media (pointer: fine) {

    transition: opacity var(--transition-duration) var(--transition-ease);
  
    &:focus,
    &:hover {
      opacity: 0.8;
    }

  }
}

.product-carousel-item__overlay {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 100%;
  height: 100%;

  display: flex;
  flex-direction: column;
  padding: calc(var(--gap) / 2);

  background: RGBA(0, 0, 0, 0);

}

.product-carousel-item__overlay-content {
  position: relative;
  z-index: 1;
  
  display: flex;
  margin-top: auto;
  
  background: var(--bg);

}

.product-carousel-item__overlay-text {
  display: flex;
  flex-direction: column;
  gap: 5px;

  width: 100%;
  padding: calc(var(--gap) / 2);

  >* {
    margin: 0;
  }
}

.product-carousel-item__overlay-thumbnail {
  max-width: 80px;
  height: 100%;
  flex: 1 0 80px;

  >figure {
    height: 100%;
  }
}

.product-carousel-item__price {
  margin-top: auto;
}

.product-carousel-item__link {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
  width: 100%;
  height: 100%;

  cursor: pointer;

}

.product-carousel-item__background {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
  width: 100%;
  height: 100%;

}

