.product__block {
  // margin-bottom: 0;
}

.product__block--lines {
  border-color: var(--border-light);
}

.product__submit {
  .btn__price {
    &:before {
      visibility: hidden;
    }
  }
}

/* ----- Filtering product images by selected option ----- */

[data-filter-images-by-option] {

  &.product__images {
    transition:
      transform 0.25s,
      opacity 0.25s;
  }

  &.product__images--fade-out {
    opacity: 0;
    transform: translateY(calc(-1 * var(--gap)));
  }

  &.product__images--fade-in {
    opacity: 1;
    transform: translateY(0);
  }

  .product__slide {

    &.media--hiding {
      opacity: 0;
    }

    &.media--hidden {
      opacity: 0;
      display: none;
    }

    &.media--active {
      opacity: 1;
      display: block;
    }

  }

}