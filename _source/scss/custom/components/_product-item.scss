.product-item {

  --aspect-ratio-mobile: calc(var(--PRODUCT-GRID-ASPECT-RATIO--MOBILE) + var(--product-item-image-background-padding--mobile));
  --aspect-ratio-desktop: calc(var(--PRODUCT-GRID-ASPECT-RATIO) + var(--product-item-image-background-padding));

  overflow: hidden;
  border-radius: var(--block-radius);

  /* ===== Variations ===== */

  &.product-item--left {
    .radio__fieldset--swatches {
      .swatch__button {
        --swatch-size: 12px;
        margin-right: 5px;
      }
    }
  }

  &.product-item--featured {
    .grid__heading-holder {
      @include respond-to($medium-down) {
        padding-top: var(--aspect-ratio-mobile);
      }
    }
  }

  &.product-item--aligned {
    .grid__heading-holder {
      @include respond-to($medium-down) {
        padding-top: var(--aspect-ratio-desktop);
      }
    }
  }

  &.product-item--extra-padding--mobile {
    .image-wrapper--cover img {
      @include respond-to($medium-down) {
        // object-fit: contain;
      }
    }
  }

  &.product-item--extra-padding--desktop {
    .image-wrapper--cover img {
      @include respond-to($medium-up) {
        // object-fit: contain;
      }
    }
  }

  /* ===== Elements ===== */

  /* --- Image --- */

  .product-item__image {
    padding-top: var(--aspect-ratio-mobile);
    @include respond-to($medium-up) {
      padding-top: var(--aspect-ratio-desktop);
    }
  }

  .product-item__badge-list {
    position: absolute;
    top: 8px;
    left: 8px;
  }

  hover-images {
    @include respond-to($medium-down) {
      display: none;
    }
  }

  .product-item__bg__slider {
    @include respond-to($medium-down) {
      height: 100%;
    }
  }

  .product-item__bg__slide {
    &:not(:first-child) {
      @include respond-to($medium-down) {
        display: none;
      }
    }
  }

  .product-item__bg,
  .product-item__bg__under {
    background: var(--product-item-image-background-color);
  }

  /* --- Product Info --- */

  .product-information {
    @include respond-to($large-down) {
      padding-left: 0;
      padding-right: 0;
    }
  }

  .product-item__title {
    @include style-product-title();
  }

  .product-item__description {
    @include style-product-description();
  }

  .product-item__price {
    @include style-product-price();
  }

  .product-item__info {
    padding-inline: 10px;
  }

  .product-item__price__holder {
    
  }

  .product-item__info-bottom {
    margin-top: 0.5em;
    padding-top: 0.5em;
    border-top: 1px solid var(--border);
    @include respond-to($medium-down) {
      display: none;
    }
  }
  
  .product-item__info-bottom-inner {
    display: flex;
  }

  .product-item__info-top {
    display: grid;
    grid-auto-flow: row;
    gap: 4px;

    > * {
      margin: 0;
    }
  }

  /* --- Swatches --- */

  .selector-wrapper__scrollbar {
    min-height: 22px;
    padding-block: 0;
    padding-bottom: 5px;
  }

  .product-item__swatches__holder {

    min-height: 22px;
    padding-top: 5px;

    .radio__fieldset__arrow--prev {
      transform: translateX(-150%);
      visibility: hidden;
    }

    .radio__fieldset {
      padding: 0;
    }

  }

  /* --- Quick-Add --- */

  .quick-add__holder {
    
    left: 10px;
    right: 10px;
    width: unset;

    .btn {
      border: 1px solid var(--border);
    }

    @include respond-to($medium-down) {
      left: 0;
      right: unset;
    }

  }

}