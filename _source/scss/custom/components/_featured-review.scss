.featured-review {

  --content-width: 320px;

  /* ----- LAYOUT ----- */

  .featured-review__media,
  .featured-review__content {
    width: 100%;
    max-width: var(--section-width);
  }

  .featured-review__inner {
    display: flex;
    height: 100%;
    @include respond-to($medium-down) {
      flex-direction: column;
    }
  }

  /* ----- MEDIA ----- */

  .featured-review__media {
    position: relative; // Positioning of video overlay
    @include respond-to($medium-down) {
      min-height: var(--content-width);
    }
    @include respond-to($medium-up) {
      min-height: 470px;
    }
  }

  /* ----- CONTENT ----- */

  .featured-review__rating {
    .icon {
      width: 16px;
    }
  }

  .featured-review__content {
    display: flex;
    flex-direction: column;
    gap: var(--gap);

    height: 100%;
    padding: var(--gap);
  }

  .featured-review__content-top,
  .featured-review__content-bottom {
    display: grid;
    gap: 1rem;
    grid-auto-flow: row;

    >* {
      margin: 0;
    }
  }

  .featured-review__content-top {
    margin-bottom: auto;
  }

  .featured-review__content-bottom {
    margin-top: auto;
  }

  .featured-review__text {
    p {
      margin-top: 0;
    }
  }

  .featured-review__author {
    color: var(--text-light);
  }

  .featured-review__caption {
    display: flex;
    align-items: flex-start;
    gap: 0.5em;
    padding: 0.8em;

    background: var(--bg-accent);
    font-size: var(--text-sm);
    border-radius: var(--corner-radius);
  }

}