.badge-list {

  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  
  @include respond-to($medium-up) {
    gap: 6px;
  }

}

.badge {

  --badge-border: RGBA(var(--COLOR-BORDER--RGB) / 0.4);

  padding: 2px 4px;
  border-width: 1px;
  
  font-family: var(--font-family-badge);
  font-style: normal;
  font-weight: var(--font-weight-badge);
  font-size: var(--font-size-badge);

  text-transform: uppercase;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;

  color: var(--text);
  background: var(--bg-accent);
  border-color: var(--bg-accent);
  border-radius: var(--corner-radius-sm);
  border-style: solid;

  @include respond-to($medium-up) {
    padding: 4px 8px;
  }

  /* ----- Sizes ----- */

  &.badge--small {
    padding: 3px 8px;
  }

  &.badge--xs {
    padding: 2px 6px;
  }


  /* ----- Styles ----- */




  /* ----- Styles ----- */

  &.badge--reversed {
    color: var(--text);
    background: var(--bg);
    border-color: var(--badge-border);
  }

  &.badge--white {
    color: var(--text);
    background: var(--color-basic-white);
  }

  &.badge--secondary {
    color: var(--text);
    background: var(--bg-accent-lighten);
    border-color: var(--bg-accent-lighten);
  }

  &.badge--soldout,
  &.badge--darken {
    color: var(--text);
    background: var(--bg-accent-darken);
    border-color: var(--bg-accent-darken);
  }

  &.badge--lighten {
    color: var(--text);
    background: var(--bg-accent-lighten);
    border-color: var(--bg-accent-lighten);
  }

  &.badge--border {
    border-color: var(--text);
  }

}