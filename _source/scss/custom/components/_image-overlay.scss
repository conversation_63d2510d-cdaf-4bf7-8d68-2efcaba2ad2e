.image-overlay__content {
  
  // display: grid;
  // grid-auto-flow: row;
  // margin-bottom: auto;
  
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 0.5rem;

  width: 100%;

  margin-top: auto;
  > * {
      margin: 0;
  }
}

.image-overlay__subheading {

}

.image-overlay__title {

}

.image-overlay__product {
  margin: auto;
  min-width: 300px;
  width: 40%;
}

.image-overlay__actions {
  margin-top: auto;
}

.custom-products-image {
  
  .image-overlay {
    
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding: var(--gutter);

    opacity: 1;
    background-color: RGBA(var(--overlay-color--rgb), var(--overlay-opacity));

  }

}