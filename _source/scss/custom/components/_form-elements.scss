/* ========== Form Elements ========== */

input,
textarea,
select,
.popout__toggle,
.input-group {
  margin: 0;
  background: var(--bg);
}


/* ----- Custom Form ----- */

.custom-form__label {
  margin-bottom: 0.5em;
}


/* ----- Input Group ----- */

.input-group {

  display: flex;
  gap: var(--gap);
  border: none;

  &.input-group--bordered {
    border: 1px solid var(--border);
  }

  .input-group__field {
    align-items: center;
    flex: 1 0 auto;
  }

  .input-group__input {
    align-items: center;
  }

  .input-group__btn {
    display: flex;
    align-items: center;
    > span {
        line-height: 1;
    }
  }

}


/* ----- Field ----- */

.field {
  
  --border: var(--COLOR-BORDER);
  
  padding: 1em;
  border: 1px solid var(--border);

  &:hover {
    border: 1px solid var(--border-light)
  }

  &:focus {
    border: 1px solid var(--border-dark)
  }

}


