.btn {

  padding: 1em 1.8em;
  line-height: var(--line-height-button);
  font-size: var(--font-size-button);
  cursor: pointer;

  .price {
    font-size: calc(var(--font-size-product-card-price) - 0.2rem); // Fiddly manual adjustment to get the different fonts of the price and button label looking the same size.
  }

  /* --- Sizes --- */

  &.btn--small {
    padding: 0.6em 1.4em;
  }

  &.btn--medium {
    // padding: 1em 1.8em;
  }
  
  &.btn--large {
    padding: 1.4em 2em;
    font-size: var(--font-size-button-lg);
  }
  
  &.btn--huge {
    padding: 1.8em 2.4em;
    font-size: var(--font-size-button-lg);
  }

  
  /* --- Layout --- */
  
  &.btn--text {
    padding-inline: 0;
  }
  
  &.btn--full {
    width: 100%;
  }
  
  &.btn--no-padding-x {
    padding-left: 0;
    padding-right: 0;
  }


  /* --- Type --- */
  
  &.btn--secondary {
    --btn-border: var(--BTN-SECONDARY-BORDER);
    --btn-border-hover: var(--BTN-SECONDARY-BORDER);
    --btn-bg: var(--BTN-SECONDARY-BG);
    --btn-text: var(--BTN-SECONDARY-TEXT);
  }


  /* --- Style --- */
  
  &.btn--outline {
    
  }

  
  /* --- Elements --- */

  .btn__price {
    &::before {
      content: "•";
      margin: 0 5px;
      visibility: hidden;
    }
  }
      

}





/* --- Button Outer --- */
// Used in upsell buttons to contain an icon button and expand it when hovered..

.btn__outer {

  > button, .btn {
    box-shadow: 0 0 0 1px var(--border) inset;
    border-radius: 100px;
  }

  .btn__plus {

    --icon-outer-size: 32px;
    --icon-size: 30px;
    --icon-offset: 4px;
    // --icon-offset: calc(calc(var(--icon-outer-size) - var(--icon-size) / 2));

    // margin: 0 0 0 calc(var(--icon-offset)/2);
    margin: 0 0 0 var(--icon-offset);
    mask-image: var(--icon-plus);
    
    transition:
      width var(--transition-duration) var(--transition-ease),
      opacity var(--transition-duration) var(--transition-ease);

    > button {
      justify-content: inherit;
    }

    + .btn__text {
      margin-left: 4px;
    }

    &:hover,
    &:focus {
      opacity: 0.5;
    }

    &.btn__plus--add {
      // mask-image: var(--icon-plus);
    }

    &.btn__plus--preorder,
    &.btn__plus--quick-add {
      --icon-size: 20px;
      --icon-offset: 6px;
      mask-image: var(--icon-add-cart);
    }

    /*
    &.btn__plus--preorder {
      --icon-size: 24px;
      mask-image: var(--icon-add-cart);
    }
    */

    .btn__text {
      margin-left: 2px;
      font-size: var(--font-size-text-xs);
    }

  }

}