/* 1. Variables */

@import "./utilities/_variables.scss";

/* 2. Mixins */

@import "./utilities/_mixins.scss";

/* 3. Fonts  */

// @import "./fonts/_fonts.scss";

/* 4. Basic Styles */
// @import "./utilities/_normalize.scss";

@import
  // "./basic-styles/_theme-overrides.scss",
  // "./basic-styles/_basic-styles.scss",
  "./basic-styles/_typography.scss",
  // "./basic-styles/_form-elements.scss",
  // "./basic-styles/_icons.scss",
  // "./basic-styles/_tables.scss",
  "./basic-styles/__basic-styles.scss"
;

/* 5. Layout */

@import
  // "./layout/_body-modifiers.scss",
  // "./layout/_shopify-policy.scss",
  // "./layout/_shopify-marketing-confirmation.scss",
  // "./layout/_shopify-challenge.scss",
  "./layout/__layout.scss"
  ;

/* 6. Sections */

@import

  // ---------- Global Section Styling ----------
  "./sections/_header.scss",
  "./sections/_footer.scss",
  "./sections/_footer-supporting-menu.scss",


  // ---------- Overlays ----------
  // "./sections/_cart-drawer.scss",

  
  // ---------- Theme Sections ----------

  // ---------- Custom Sections ----------

  "./sections/_theme-main-collection.scss",

  // Collection Sections
  // "./sections/_custom-collection.scss",
  "./sections/_custom-collections-list-hover.scss",
  "./sections/_custom-products-image.scss",
  
  
  // Product Sections
  "./sections/_custom-main-product.scss",
  
  // Content Section
  "./sections/_custom-section-hero.scss",
  "./sections/_custom-press-logos.scss",
  "./sections/_custom-highlights.scss",
  "./sections/_custom-section-columns.scss",
  "./sections/_custom-double.scss",
  "./sections/_custom-accordion-group.scss",
  "./sections/_custom-multicolumn.scss",
  "./sections/_custom-custom-content.scss",
  "./sections/_custom-text-promo.scss",

  
  // ---------- Bespoke Sections ----------
  // "./sections/_bespoke-section.scss",

  // Products
  "./sections/_bespoke-products-carousel.scss",
  "./sections/_bespoke-product-compare.scss",
  "./sections/_bespoke-featured-reviews.scss",
  "./sections/_bespoke-tabbed-gallery.scss",


  // ---------- Misc. Section Styling ----------
  "./sections/__sections.scss" 

;

/* 7. Page-Specific Styles */

@import
  // "./pages/_home.scss",
  "./pages/__pages.scss"
  ;

/* 8. Components */

@import

  // Global Elements
  // "./components/_rte.scss",
  "./components/_icons.scss",
  "./components/_radio.scss",
  "./components/_buttons.scss",
  "./components/_form-elements.scss",
  "./components/_prices.scss",
  "./components/_badges.scss",
  "./components/_accordion.scss",
  "./components/_image-overlay.scss",
  "./components/_quick-add.scss",
  "./components/_rating-dots.scss",
  
  "./components/_product.scss",
  "./components/_product-item.scss",
  "./components/_product-upsell.scss",
  "./components/_product-carousel-item.scss",
  
  "./components/_grid.scss",
  "./components/_grid-slider.scss",

  "./components/_featured-review.scss",
  
  "./components/_tabs.scss",
  "./components/_tabbed-gallery-image.scss",
  
  "./components/_hero.scss",
  "./components/_brick-section.scss",
  "./components/_newsletter.scss",
  
  "./components/_loyalty-points.scss",
  
  "./components/_search-results-item.scss",
 
  // Content Elements
  // "./components/_contact-form.scss",
  
  // Overlays
  // "./components/_drawers.scss",
  
  // Collection Elements
  // "./components/_collection-facets.scss",
  
  // Product Elements
  
  // Cart Elements
  // "./components/_quantity-selector.scss",
  
  // Blog Elements
  // "./components/_article-banner.scss",

  // Misc. Elements
  
  "./components/__components.scss"
  ;

/* 9. Apps  */

@import
  "./apps/__apps.scss"
  ;

/* 10. Utility Classes */

@import 
  "./utilities/_classes.scss",
  "./utilities/_classes--broadcast.scss"
  ;

/* 11. Third-Party Styles */

@import 
  "./third-party/__thirdparty.scss"
  ;

/* 12. Animations */

@import 
  "./utilities/_animations.scss"
  ;
 