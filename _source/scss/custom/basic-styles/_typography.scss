
@mixin style-h1() {
  font-family: var(--font-family-heading-1);
  font-size: var(--font-size-h1);
  font-weight: 600;
  line-height: 100%;
  letter-spacing: -0.03em;
  text-transform: uppercase;
}

@mixin style-h2() {
  font-family: var(--font-family-heading-1);
  font-size: var(--font-size-h2);
  font-weight: 600;
  line-height: 105%;
  letter-spacing: -0.01em;
  text-transform: uppercase;
}

@mixin style-h3() {
  font-family: var(--font-family-heading-1);
  font-size: var(--font-size-h3);
  font-style: normal;
  font-weight: 600;
  line-height: 105%;
  letter-spacing: -0.01em;
  text-transform: uppercase;
}

@mixin style-h4() {
  font-family: var(--font-family-heading-2);
  font-size: var(--font-size-h4);
  font-style: normal;
  font-weight: 300;
  line-height: 115%;
  letter-spacing: -0.02em;
}

@mixin style-h5() {
  font-family: var(--font-family-heading-2);
  font-size: var(--font-size-h5);
  font-style: normal;
  font-weight: 300;
  line-height: 115%;
  letter-spacing: -0.01em;
}

@mixin style-h6() {
  font-family: var(--font-family-heading-2);
  font-size: var(--font-size-h6);
  font-style: normal;
  font-weight: 300;
  line-height: 135%;
  letter-spacing: -0.01em;
}

/* ----- Text  ----- */

@mixin style-text-xs() {
  font-size: var(--font-size-text-xs);
}

@mixin style-text-sm() {
  font-size: var(--font-size-text-sm);
}

@mixin style-text() {
  font-size: var(--font-size-text);
}

@mixin style-text-lg() {
  font-size: var(--font-size-text-lg);
}

@mixin style-text-xl() {
  font-size: var(--font-size-text-xl);
}

/* ----- Subheadings  ----- */

@mixin style-subheading() {
  font-family: var(--font-family-heading-2);
  font-size: var(--font-size-subheading);
  font-style: normal;
  font-weight: var(--font-weight-subheading);
  letter-spacing: var(--letter-spacing-subheading);
  line-height: 115%;
}

@mixin style-eyebrow() {
  font-family: var(--font-family-heading-1-alt);
  font-size: var(--font-size-eyebrow);
  font-style: normal;
  font-weight: var(--font-weight-eyebrow);
  line-height: 125%;
  letter-spacing: var(--letter-spacing-eyebrow);
  text-transform: uppercase;
}

@mixin style-eyebrow-2() {
  font-family: var(--font-family-heading-2);
  font-size: var(--font-size-eyebrow-2);
  font-style: normal;
  font-weight: var(--font-weight-eyebrow-2);
  line-height: 115%;
  letter-spacing: var(--letter-spacing-eyebrow-2);
  text-transform: uppercase;
}

@mixin style-badge() {
  font-family: var(--font-family-heading-2-alt);
  font-size: var(--font-size-badge);
  font-weight: var(--font-weight-badge);
  text-transform: uppercase;
}

@mixin style-badge-lg() {
  font-family: var(--font-family-heading-2-alt);
  font-size: var(--font-size-badge-lg);
  font-weight: var(--font-weight-badge-lg);
  text-transform: uppercase;
}

/* ----- Product Cards  ----- */

@mixin style-product-title() {
  font-family: var(--font-family-heading-1);
  font-size: var(--font-size-product-card-title);
  font-weight: var(--font-weight-product-title);
  text-transform: uppercase;
}

@mixin style-product-description() {
  font-size: var(--font-size-product-card-description);
  font-weight: var(--font-weight-product-description);
}

@mixin style-product-price() {
  font-family: var(--font-family-product-price);
  font-size: var(--font-size-product-card-price);
  font-weight: var(--font-weight-product-price);
}

@mixin style-link() {
  position: relative;
  &:after {
    --main-color: var(--link);
    --hover-color: var(--link-a70);
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    height: 1px;
    width: 100%;
    background: linear-gradient(to right, var(--hover-color) 0% 50%, var(--main-color) 50% 100%);
    background-size: 200% 100%;
    background-position: 100% 0;
    transition: background-position 0.3s cubic-bezier(0.39, 0.575, 0.565, 1);
    pointer-events: none;
  }
}

/* ========== Fonts ========== */

.text-larger {
  font-size: 1.15em;
}

.text-smaller {
  font-size: 0.85em;
}

/* ----- Heading Font 1 - URW DIN  ----- */

.heading-font-1 {
  font-family: var(--font-family-heading-1);
}

/* ----- Heading Font 2 - Maison Neue Extended  ----- */

.heading-font-2 {
  font-family: var(--font-family-heading-2);
}

/* ----- Body Font 1 - Maison Neue  ----- */

.body-font {
  font-family: var(--font-family-body);
}

.font-heading {
  text-transform: unset;
}


/* ========== Typography ========== */

/* ----- Headings  ----- */

.h0,
.heading-x-large,
.h1, .text-h1 {
  @include style-h1();
}

.heading-large,
.h2, .text-h2 {
  @include style-h2();
}

.heading-medium,
.h3, .text-h3 {
  @include style-h3();
}

.heading-small,
.h4, .text-h4 {
  @include style-h4();
}

.heading-x-small,
.h5, .text-h5 {
  @include style-h5();
}

.heading-mini,
.h6, .text-h6 {
  @include style-h6();
}

@include respond-to($medium-down) {

  .heading-mobile-mini {
    @include style-h6();
  }

  .heading-mobile-x-small {
    @include style-h5();
  }

  .heading-mobile-small {
    @include style-h4();
  }

  .heading-mobile-medium {
    @include style-h3();
  }

  .heading-mobile-large {
    @include style-h2();
  }

  .heading-mobile-x-large {
    @include style-h1();
  }

}

@include respond-to($medium-up) {

  .heading-desktop-mini {
    @include style-h6();
  }

  .heading-desktop-x-small {
    @include style-h5();
  }

  .heading-desktop-small {
    @include style-h4();
  }

  .heading-desktop-medium {
    @include style-h3();
  }

  .heading-desktop-large {
    @include style-h2();
  }

  .heading-desktop-x-large {
    @include style-h1();
  }

}

/* ----- Body  ----- */



/* ----- Subheadings ----- */

.subheading {
  @include style-subheading();
}

.subheading-eyebrow {
  @include style-eyebrow();
}

.subheading-eyebrow-2 {
  @include style-eyebrow-2();
}


/* ----- Body Text Styles ----- */

.body-x-small,
.text-xs {
  @include style-text-xs();
}

.body-small,
.text-sm {
  @include style-text-sm();
}

.p,
.body-medium,
.text-body {
  @include style-text();
}

.body-large,
.text-lg {
  @include style-text-lg();
}

.body-x-large,
.text-xl {
  @include style-text-xl();
}

@include respond-to($medium-down) {

  .text-xs--mobile {
    @include style-text-xs();
  }
  .text-sm--mobile {
    @include style-text-sm();
  }
  .text--mobile {
    @include style-text();
  }
  .text-lg--mobile {
    @include style-text-lg();
  }
  .text-xl--mobile {
    @include style-text-xl();
  }
  
}


/* ----- Misc. Text Styles ----- */

.text-caption {
  font-size: var(--font-size-caption);
}

.text-navigation {
  font-family: var(--font-family-body);
  font-size: var(--font-size-navigation);
  font-weight: var(--font-weight-body-bold);
  text-transform: uppercase;
}

.text-badge {
  @include style-badge();
  
}

.text-badge-lg {
  @include style-badge-lg();
}


/* ----- Product Card Text Styles ----- */


.text-product-title {
  @include style-product-title();
  &.text-product-title--large {
    font-size: var(--font-size-product-card-title-large);
  }
}


.text-product-description {
  
}

.text-product-price {
  @include style-product-price();
  // font-size: var(--font-size-product-card-price);
}



/* ========== Lists ========== */

/* ----- Unordered ----- */

ul, .ul {
  
  li, .li {

  }

  &.ul--ticks {
    li, .li {

      --marker-size: 20px;
      --marker-size: 20px;
      --marker-gutter: 10px;

      position: relative;
      list-style: none;

      margin-block: 0.8rem;

      &:last-of-type {
        margin-bottom: 0;
      }

      &::before {

        --offset-y: -.2em;
        --offset-x: calc(-100% - var(--marker-gutter));

        content: "";

        position: absolute;
        left: 0;
        top: 0;

        display: block;
        width: var(--marker-size);
        height: var(--marker-size);

        transform:
          translateX(var(--offset-x)) translateY(var(--offset-y));

        background: var(--icon-check);

        // background: var(--bg-accent);
        // border-radius: 100px;

      }

    }
  }

}