/* ==============================
   Sections
   ============================== */

    .section-header {

      display: flex;
      gap: var(--gap);
      margin-bottom: var(--gutter);
      // gap: calc(var(--gap) * 2);

      @include respond-to($medium-down) {
        flex-direction: column;
        text-align: center;
        align-items: center;
      }

      .section-header__actions,
      .section-header__text {
        > * {
          margin: 0;
        }
      }

      .section-header__text {
        display: grid;
        grid-auto-flow: row;
        gap: calc(var(--gap) / 2);
      }

      &.section-header--vertical {

        flex-direction: column;
        text-align: center;
        align-items: center;

      }

      &.section-header--horizontal {

        justify-content: space-between;
        align-items: flex-end;

        @include respond-to($medium-down) {
          flex-direction: column;
          text-align: left;
          align-items: flex-start;
          gap: calc(var(--gap) * 2);
        }

      }

    }

/* ----- Wrappers ----- */

  .wrapper--small {
    max-width: 870px;
    margin: 0 auto;
    padding-left: var(--outer);
    padding-right: var(--outer);
  }
