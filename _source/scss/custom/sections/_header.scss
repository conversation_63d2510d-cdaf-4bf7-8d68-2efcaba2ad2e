.page-header {

  /* ----- Toolbar ----- */

  .toolbar {
    
    padding: 0;

    .toolbar__utility,
    .popout__toggle__text,
    .navlink {
      font-weight: normal;
    }

    ticker-bar {
      width: auto;
    }

    .navlink--toplevel {
      position: relative;
      @include respond-to($medium-up) {
        padding-block: 15px;
      }
      &::after {
        content: '';
        display: block;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: currentColor;
        opacity: 0;
        transition: opacity 0.25s ease-out;
      }
      &:hover {
        &::after {
          opacity: 1;
        }
      }
    }

    .navlink--active {
      pointer-events: none;
      cursor: pointer;
      &::after {
        opacity: 1;
      }
    }

    .toolbar__rewards-link {
      
    }

  }

  /* ----- Mobile ----- */

  .header__mobile {
    
    padding-top: 15px;
    padding-bottom: 15px;

  }

  /* ----- Desktop ----- */

  .theme__header {
  
    &.theme__header--white {
  
      .header__wrapper,
      .toolbar {
        --bg: var(--color-basic-offwhite);
      }
    }
  
    .header__wrapper,
    .toolbar {
      background: var(--bg);
      transition: 0.25s background;
  
      .toolbar__utilities:before,
      .popout__toggle {
        background: transparent;
      }
    }
  
    .header__dropdown {
      --bg: var(--color-basic-offwhite);
      background: var(--bg);
      border-bottom: 1px solid var(--border);
    }
  
    .header__dropdown__actions {
      margin: 0 -1px -1px -1px;
    }
  
    &:hover,
    &:focus-within {
  
      .header__wrapper,
      .toolbar {
        --bg: var(--color-basic-offwhite);
        transition: 0.25s background;
      }
    }
  
  }

  .header__desktop {

    .header__desktop__upper {
      min-height: 85px;
    }

    .header__menu {

      > .menu__item {
        > .navlink--highlight {

          .navtext {

            --bg: var(--color-basic-offwhite);

            /* Auto layout */
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 4px 8px;
            gap: 10px;

            background: var(--bg);
            border: 1px solid var(--color-brand-6);
            border-radius: var(--corner-radius);

            &:after {
              content: none;
            }

          }
        }
      }

      .navlink {
        &.navlink--toplevel {
          @extend .text-navigation;
        }
        &.navlink--child {
          @extend .text-badge-lg;
        }
        
      }

    }

  }

  .header__backfill {
    margin-top: -1px;
  }

  .header__desktop__buttons {
    .header__menu {
      margin-right: 0;
    }
  }

  

  /* ---------- Dropdowns ---------- */

  .dropdown__family {
    padding: 0 !important;
  }

  .header__grandparent__links {
    gap: var(--gap);
  }

  .header__dropdown__wrapper {
    &.header__dropdown__wrapper--no-collection {
      .header__dropdown__outer,
      .header__dropdown__inner {
        width: 100%;
      }
    }
  }
 
  .header__dropdown__outer {

    --total-gutter-width: calc(var(--megamenu-columns-max) * var(--gap));
    --total-margin-width: calc(2 * var(--outer));

    --column-width: calc(calc(100vw - var(--total-gutter-width) - var(--total-margin-width)) / var(--megamenu-columns-max));

    display: flex;
    justify-content: space-between;
    padding: var(--outer) var(--outer);
    gap: var(--gap);

  }

  .header__dropdown__inner {
    display: flex;
    flex-wrap: wrap;
    gap: var(--gap);

    /*
      gap: var(--inner);
      flex-direction: column;
      justify-content: unset !important;
      */

  }

  .dropdown__column {
    min-width: var(--column-width);
  }

  .dropdown__family--major {
    flex: 1 0 auto;
    height: 100%;
  }

  .megamenu-products {
    display: flex;
  }

  

  /* Dropdown Link Styling */

  .grandparent .navlink--child {
    margin-bottom: 0px;
  }
  
  .grandparent .navlink--child,
  .grandparent .navlink--grandchild {
  
    display: inline-flex;
    align-items: center;
    gap: 0.5em;
  
    .navtext,
    .navbadge {
      margin: 0;
    }
  
    .navtext {
      padding: 0.1em 0;
  
      &:after {
        bottom: 0px !important;
      }
    }
  
    .navbadge .badge {
      font-size: var(--font-size-badge);
      margin: 0;
    }
  
  }

}