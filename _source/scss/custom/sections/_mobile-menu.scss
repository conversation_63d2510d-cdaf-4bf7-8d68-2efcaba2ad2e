.drawer.drawer--header {

  z-index: 6001;

  .btn--just-block {
    --icon-size: 15px;
  }


  /* ----- Head ----- */

  .drawer__head {
    flex-direction: row;
    padding: var(--gap);
  }

  .drawer__close {
    right: 0;
  }

  .header__logo__link {
    max-width: 90px;
  }


  /* ----- Mobile Menu Layout ----- */

  .drawer__content {
    --item-height: 35px;
  }

  .mobile-menu__block--half {
    display: inline-flex;
    gap: 0;
    justify-content: flex-start;

    &:nth-child(2n-1) {
      justify-content: flex-start;
    }

    &:nth-child(2n) {
      justify-content: flex-end;
    }

  }

  .drawer__foot__scroll {
    justify-content: space-between;
    .popout-header {
      justify-content: flex-end;
      padding-right: var(--inner) !important;
      gap: 0 !important;
    }

  }

  .socials.socials--compact {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .icon-fallback-text {
      display: none;
    }
  }


  .drawer__main-menu {
    padding: var(--inner) 0;
  }

  .mobile__menu__dropdown {
    padding: var(--inner) 0;
  }


  /* ----- Filters ----- */

  .menu-filters {

    margin-block: var(--gap);
    padding-block: var(--inner);
    border-block: 1px solid var(--border);

    .filter-heading {
      margin-bottom: 1em;
    }

  }

  .filter-swatches {
    display: flex;
    justify-content: flex-start;
    gap: 1.2em;
    flex-wrap: wrap;
    margin: 1em 0;
  }

  .filter-swatch {
    gap: 0.5em;
    display: flex;
    align-items: center;
  }

  .filter-swatch__swatch {
    
    --swatch-color: var(--bg-accent-darken);
    --swatch-size: 14px;

    width: var(--swatch-size);
    height: var(--swatch-size);
    border: 1px solid var(--border);

    background-color: var(--swatch-color);
    border-radius: 100px;
  }

  .filter-swatch__text {
    font-size: var(--font-size-text-lg);
  }

  /* ----- Blocks ----- */

  .mobile-menu__block {
    background: var(--bg);
  }


  /* ----- Slide Rows ----- */

  .sliderow__title {
    gap: 0.5em;
    .badge {
      margin-left: auto;
    }
    + {
      .badge {
        display: none;
      }
    }
  }

  .sliderow {
    .sliderow__links {
      .sliderow__title {
        font-size: var(--font-size-text-lg);
      }
    }
    .sliderule__chevron--right {
      width: auto;
    }
  }

  .sliderow.sliderow--back {

    padding-right: var(--inner);

    .sliderow__back-button {
      display: flex;
      align-items: center;
      position: relative;
      padding-left: var(--inner);
    }

    .sliderow__title {
      padding: 0;
      justify-content: flex-start !important;

      @include style-badge-lg();

      >span {
        margin: 0;
      }
    }

    .sliderow__shop-all-link {
      white-space: nowrap;
    }

    .sliderule__chevron--left {
      display: flex;
      align-items: center;
      justify-content: center;
      width: auto;
      min-width: 0;
    }

  }
  

  /* ----- Navigation Item ----- */

  .sliderow {
    // --item-height: 35px;
  }

  .sliderow--back {
    top: var(--inner);
  }

  .sliderow__title {}

  .sliderule__chevron {
    --icon-size: 10px;
  }



  /* ----- Dropdown Filters ----- */

  .dropdown-filters {

    margin-top: var(--gap);
    padding-block: var(--inner);
    border-top: 1px solid var(--border);

    .dropdown-filters__title {
      padding-inline: var(--inner);
      margin-bottom: 1em;
    }

  }

  /* ----- Dropdown Collection ----- */

  .dropdown-collection {

    margin-top: var(--gap);
    padding: var(--inner);

    display: grid;
    grid-auto-flow: row;
    gap: 1em;

  }


}
