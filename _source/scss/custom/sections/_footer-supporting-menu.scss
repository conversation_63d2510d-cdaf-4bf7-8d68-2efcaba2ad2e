.supporting-menu.custom-supporting-menu {

  // Popout

  .popout-footer {
    margin: 0; // Fix footer bug.
    flex: 1 0 0;
  }

  .popout-list {
    background: var(--COLOR-BG-ACCENT);
  }

  .popout__toggle {
    min-height: 20px;
    padding: 10px;
    margin: 0;
    @include respond-to(medium-up) {
      min-height: 40px;
    }
  }

  .supporting-menu__inner {
    
    min-height: 50px;
    padding-inline: var(--LAYOUT-OUTER-MEDIUM);
    gap: 0;
    column-gap: 10px;

    background: var(--COLOR-BG-ACCENT);

    @include respond-to($medium-down) {
      padding: var(--LAYOUT-OUTER-SMALL);
      row-gap: 10px;
    }

    @include respond-to($medium-up) {
      row-gap: 20px;
    }
  }

  .supporting-menu__wrapper {
    margin-top: -1px;
    padding-bottom: var(--LAYOUT-OUTER-MEDIUM);
  }

  
  /* ----- Menu Items ----- */

  .supporting-menu__item {
    flex: unset;
    @include respond-to($medium-up) {
      display: flex;
      align-items: center;
      min-height: 60px;
    }
  }

  .supporting-menu__item--copyright {
    order: 0;
  }

  .supporting-menu__copyright {
    li {
      @include respond-to(medium-down) {
        padding: 0 var(--gap);
      }
    }
  }

  .supporting-menu__item--localization {
    order: 2;
    @include respond-to(medium-up) {
      // margin-inline-start: auto;
    }
  }

  .supporting-menu__item--payment {
    order: 1;
    @include respond-to(medium-up) {
      // margin-inline-end: auto;
    }
  }

  .supporting-menu__item--credit {
    order: 3;
  }

  .popout-footer,
  .supporting-menu__copyright,
  .supporting-menu__payment,
  .supporting-menu__copyright {
    @include respond-to($medium-down) {
      justify-content: center;
    }
  }

}