.accordion-group.custom-accordion-group {

  @include respond-to($medium-up) {

    .accordion-group--columns {
    
      display: grid;
      grid-template-columns: minmax(0, 1fr) minmax(0, 3fr);
      gap: var(--gutter);

      position: relative;

      .section-header {
        display: inline-flex;
        position: sticky;
        top: var(--gap);
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: column;
      }

    }

  }

}