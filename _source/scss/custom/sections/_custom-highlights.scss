.highlights.custom-highlights {

  .highlights__items {
    --gap: 5px;
    @include respond-to($medium-up) {
      --gap: 10px;
    }
  }

  /*
  .highlights__items {
    --gap: 5px;

    margin: 0;
    
    @include respond-to($medium-up) {
      display: grid;
      gap: var(--gap);
      grid-template-columns: repeat(var(--columns-desktop), minmax(0, 1fr));
    }

  }

  .highlights__item {
    margin: var(--gap) 0;

    @include respond-to($large-up) {
      margin: 0;
      padding: 0;
    }
  }
    */

  a.highlights__item-inner {
    transition: opacity var(--transition-duration) var(--transition-ease);
    &:hover, &:focus {
      opacity: 0.8;
    }
  }

  /* ----- Mobile Grid ----- */

  .highlights__items--mobile-grid {

    margin: 0 calc(-1 * var(--gap));

    .highlights__items {
      margin: 0;

      @include respond-to($medium-up) {
        display: grid;
        gap: var(--gap);
        grid-template-columns: repeat(var(--columns-desktop), minmax(0, 1fr));
      }
    }

    .highlights__item {
      @include respond-to($medium-down) {
        flex: 1 0 50%;
        margin: 0 !important;
        padding-bottom: calc(var(--gap) * 2);
      }
    }
  }

    
  /* ----- Mobile Slider ----- */

  .highlights__items--mobile-slider {

    .highlights__item {
      margin: 0 !important;
      padding-bottom: calc(var(--gap) * 2);
    }

    @include respond-to($medium-down) {

      gap: calc(var(--gutter) / 2);
      padding-inline: calc(var(--gutter) / 2);

      .highlights__item {
        flex: 1 0 50%;

      }

      .highlights__item {
        width: calc(50% - var(--gutter));
        margin: 0 !important;
      }

      &:after {
        content: none;
      }

    }
  }

}
