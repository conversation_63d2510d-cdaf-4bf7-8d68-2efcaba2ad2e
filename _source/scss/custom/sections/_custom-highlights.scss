.highlights.custom-highlights {

  &:not(.custom-subcollections) {

    .highlights__items {
      --gap: 5px;

      @include respond-to($medium-up) {
        --gap: 10px;
      }
    }

  }

  a.highlights__item-inner {
    transition: opacity var(--transition-duration) var(--transition-ease);

    &:hover,
    &:focus {
      opacity: 0.8;
    }
  }

  /* ----- Mobile Grid ----- */

  .highlights__items:not(.highlights__items--mobile-slider) {

    --margin: 20px;

    justify-content: stretch;
    flex-wrap: wrap;

    margin: 0 calc(-1 * var(--margin) / 4) calc(-1 * var(--margin) / 2);

    >.highlights__item {
      padding: 0 calc(var(--margin) / 4) calc(var(--margin) / 2);
      margin: 0;
      width: 100%;

      flex: 1 1 50%;

      @include respond-to($medium-up) {
        flex: 1 1 25%;
      }

      @include respond-to($large-up) {
        flex: 1 1 20%;
      }

    }

  }


  /* ----- Mobile Slider ----- */

  .highlights__items--mobile-slider {

    .highlights__item {
      margin: 0 !important;
      padding-bottom: calc(var(--gap) * 2);
    }

    @include respond-to($medium-down) {

      gap: calc(var(--gutter) / 2);
      padding-inline: calc(var(--gutter) / 2);

      .highlights__item {
        flex: 1 0 50%;

      }

      .highlights__item {
        width: calc(50% - var(--gutter));
        margin: 0 !important;
      }

      &:after {
        content: none;
      }

    }
  }

}