.index-product {

  .product__page {
    @include respond-to($large-up) {
      display: flex;
    }
  }

  .product__images {
    @include respond-to($large-up) {
      width: 100%;
    }
  }

  .product__content {
    .form__width {
      @include respond-to($large-up) {
        max-width: 40vw !important;
      }
    }
  }

  /* =========== Product Blocks =========== */

  .product__block {

    &.block-padding {
      --block-padding-top: 10px;
      --block-padding-bottom: 10px;
      &:not(.block__icon__container--half) {
        &:first-of-type {
          --block-padding-top: 0;
        }
        &:last-of-type {
          // --block-padding-bottom: 0;
        }
        >* {
          margin: 0;
        }
      }
    }
    
    /* ----- Product Variant Picker ----- */

    &.product__block--variant-picker {
      border-top: none;

      .selector-wrapper--swatches {

        .radio__legend__option-name {
          display: none;
        }

        .radio__legend__value {
          @include style-badge-lg();
        }

        .radio__legend__info-link {
          
        }

      }

    }


    /* ----- Product Meta ----- */

    &.product__block--meta {

      .product-meta__top {
        display: flex;
        justify-content: space-between;
      }

      .meta-volume__field {
        + .meta-volume__field {
          &:before {
            content: "/";
          }
        }
      }

    }
    

    /* ----- Radios ----- */

    .radio__buttons {
      text-align: right;
      @include respond-to($medium-up) {
        // display: block;
      }
    }


    /* ----- Selector Wrapper ----- */

    .selector-wrapper {
      
      &.selector-wrapper--swatches {
        .radio__legend {
          display: flex;
          align-items: center;
          align-items: flex-start;
        }
      }

    }


    /* ----- Accordion ----- */

    &.block__icon__container {
      margin-bottom: var(--gap);
    }


    /* ----- Accordion ----- */

    &.product__block--accordion {
      margin-top: 0;
    }

    .product__title__wrapper {
      padding: 0;
    }

    .block__icon {
      margin-right: 5px;
    }
    
  }

  /* =========== Features =========== */

  .product-accordion {
    .accordion {
      &:first-of-type {
        border-top: 0;
      }
    }
  }

  /* =========== Features =========== */

  .product__feature {
    padding: var(--inner);
  }

  .product__feature__content {
    .btn--text {
      padding-bottom: 0;
    }
  }

}