.bespoke-products-carousel {

  .grid-item {

    --aspect-ratio: calc(640/360);
    --item-width: calc(100vw - var(--outer) * 2 - 50px);
    --item-height: calc(var(--item-width) * var(--aspect-ratio));

    // scroll-snap-align: start;
    flex: 0 0 var(--item-width);
    max-width: var(--item-width);
    margin-right: var(--gap);

    @include respond-to($medium-up) {
      --item-width: 360px;
    }

  }

  .grid--mobile-slider {
    .grid-item {
      scroll-snap-align: center;
    }
  }

}