collection-component.collection {

  .collection__nav {

    border: none;

    .popout__toggle {
      border: none;
      padding-block: var(--inner);
    }

  }

  .collection__sidebar__slider {
    border: none;
  }

  .inner-color-scheme {}

  .grid-outer {
    padding-top: 0;
  }

  .filter-group__heading {
    padding-bottom: 10px;
  }

}

.product-item--split-banner {

  @include respond-to($small-down) {
    display: grid;
    grid-column: 1 / span 2;
  }

  .product-item__split-banner-inner {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
    @include respond-to($small-down) {
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  .product-item__split-banner-bg {

    img {
      display: block;
      height: 100%;
      position: relative;
      object-fit: cover;
    }
  }

  .product-item__split-banner-content {
    display: grid;
    grid-auto-flow: row;
    gap: calc(var(--gap));

    padding: var(--inner);

    >* {
      margin: 0;
    }
  }

  .product-item__split-banner-image {
    height: 100%;
  }

  .product-item__split-banner-content-inner {
    max-width: 300px;
    margin: auto;
  }

}