.collection-image-with-title {

  .collection__title.collection__title--no-image {

    display: flex;
    gap: var(--gutter);
    align-items: flex-start;

    @include respond-to($medium-down) {
      flex-direction: column;
      gap: var(--gap);
    }

    .hero__title,
    .hero__description {
      flex: 1 0 40%;
      align-items: flex-start;
      margin: 0;
    }

    .hero__title {}

    .hero__description {
      display: grid;
      grid-auto-flow: row;
      gap: 1em;

      >* {
        margin: 0;
      }
    }
  }
}