.section-columns.custom-multicolumn {

  .column__image {
    position: relative;
    margin-bottom: 0;
    + .column__content {
      margin-top: var(--inner);
    }
  }

  .column__image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    width: 100%;
    height: 100%;

    display: flex;
    flex-direction: column;
    gap: var(--gap);

    padding: var(--gap);
    
  }
  
}