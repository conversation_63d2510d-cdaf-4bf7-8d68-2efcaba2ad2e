section.bespoke-product-comparison {

  .compare-wrapper {
    display: grid;

    @include respond-to($large-up) {
      grid-template-columns: minmax(0, 1fr) minmax(0, 2fr)
    }

    @include respond-to($large-down) {
      grid-auto-flow: row;
      gap: var(--gap);
    }

  }

  .compare-sidebar {
    padding: var(--outer);
  }

  .compare-content {
    padding: var(--inner);
  }
  
  
  /* ----- Compare Grid ----- */
  
  .product-compare-field {
    @include respond-to($medium-up) {
      overflow: hidden;
      width: 100%;
      flex: 1 0 auto;
      white-space: nowrap;
      padding-right: 1em;
    }
    @include respond-to($medium-down) {
      &:nth-child(2n) {
        .product-compare-field__label {
          display: none;
        }
      }
    }
  }

  .product-compare-field {

    @include respond-to($medium-down) {

      --padding: 10px;
    
      position: relative;
      align-items: flex-start;
      gap: calc(var(--padding) * 2);
    
      min-height: 50px !important;
      padding: var(--padding);
    
      .product-compare-field__label {
        position: absolute;
        top: var(--padding);
        left: var(--padding);
      }
    
      .product-compare-field__value {
        @include respond-to($medium-up) {
          display: block;
          width: 100%;
        }
        padding-top: calc(var(--padding) * 2);
      }

    }

  }

}