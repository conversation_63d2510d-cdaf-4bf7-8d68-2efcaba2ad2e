.text-promo.custom-text-promo {

  .hero__content {}

  .hero__content {
    flex-direction: column;
    gap: var(--gap);

    @include respond-to($small-up) {
      flex-direction: row;
      justify-content: space-between;
      flex-wrap: wrap;
    }
  }

  .hero__content-left,
  .hero__content-right {
    display: grid;
    grid-auto-flow: row;
    gap: var(--gap);
    width: 100%;
    @include respond-to($small-up) {
      max-width: 320px;
    }

    >* {
      margin: 0;
    }
  }

  .hero__content-left {}

  .hero__content-right {}

}