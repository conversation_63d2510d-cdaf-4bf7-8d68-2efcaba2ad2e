.bespoke-reviews-carousel {

  .grid-item {

    // --section-width: 640px;
    
    --item-width: calc(100vw - var(--outer) * 2 - 50px);

    flex: 0 0 var(--item-width);
    margin-right: var(--gap);

    @include respond-to($medium-up) {
      --item-width: calc(70vw - var(--outer) * 2 - 50px);
    }

    @include respond-to($xlarge-up) {
      --item-width: calc(50vw - var(--outer) * 2);
    }

  }

  .grid--mobile-slider {
    .grid-item {
      scroll-snap-align: center;
    }
  }

}