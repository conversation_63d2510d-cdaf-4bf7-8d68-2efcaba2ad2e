.section-columns.custom-section-columns {

  .grid__heading-holder {
    &.additional-padding {
      margin: 0 0 6rem;
    }
  }

  .column__icon-background {

    width: calc(var(--icon-size, 24px) * 2);
    height: calc(var(--icon-size, 24px) * 2);
    
    margin-bottom: 2rem;

    display: flex;
    align-items: center;
    justify-content: center;

    background-color: var(--icon-background);
    border-radius: 100%;

    .icon__animated {
      margin: 0;
      width: var(--icon-size,24px);
      height: var(--icon-size,24px);
    }

  }

  #PBarNextFrameWrapper {
    display: none;
  }

}