.drawer--cart {

  --inner: 16px;

  .cart__items-count {

    position: relative;
    top: -2px;

    &:after,
    &:before {
      content: none;
    }

    >span {
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-accent-lighten);
      min-width: 20px;
      height: 20px;
      border-radius: 100px;
      letter-spacing: 0;
    }
  }

  /* ----- Cart Blocks ----- */

  .cart-block {

    +.cart-block {

      .free-shipping {
        padding-top: 0;
      }
    }

    &.cart-block--top {
      border: 0;
    }

    &.cart-block--free-shipping {
      .cart-block__inner {
        width: 100%;
        padding: var(--inner);

        text-align: center;

        background-color: var(--bg);
        border-radius: var(--corner-radius);
      }

      .free-shipping__progress-bar {
        // --progress-value-bg: var(--bg-accent-lighten);
        height: 12px;
      }

      .drawer__message {
        width: 100%;
      }
    }

    &.cart-block--payment-icons {
      
    }

    &.cart-block--cart-message {

      .cart__message {
        background-color: var(--bg);
        justify-content: center;
        text-align: center;
      }
    }

    &.cart-block--checkout-buttons {
      .cart__buttons__fieldset {
        .btn {
          margin: 0;
        }
      }
      .cart__buttons-all {
        display: grid;
        grid-auto-flow: row;
        gap: calc(var(--gap) / 2);

        *:last-of-type {
          margin-bottom: 0;
        }
      }
    }

  }

  .cart__item,
  .drawer__inner,
  .additional-checkout-buttons,
  .cart__foot__inner,
  .accordion,
  .free-shipping {
    border: none;
  }


  .cart__title {
    gap: 0.5em;
    align-items: center;
  }

  /* ----- Cart Items ----- */

  .cart__item {
    align-items: flex-start;
    // padding-bottom: 0;

    .cart__item__image {
      a {
        position: relative;
        padding-top: var(--aspect-ratio-desktop);
      }

      .lazy-image {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
      }
    }

    .cart__quantity-counter {
      .cart__quantity {
        margin-bottom: 0.5em;
      }
    }
    .cart__item__content {
      padding-left: var(--gap);
    }
  }

  /* ----- Cart Widgets ----- */

  .cart__widget {
    .cart__widget__title {
      svg {
        right: var(--inner);
      }
    }
    .cart__widget__content__inner {
      padding-top: calc(var(--gap) / 2);
    }
  }

  /* ----- Elements ----- */

  .cart__total {
    font-weight: inherit;
    gap: 0.5em;
    margin-bottom: 0.5em;
  }

  /* ----- Elements ----- */

  .cart__field {
    background-color: var(--bg-accent);
  }

  .cart__payment-icons {
    padding-top: 0;
  }

  .cart__discount {
    padding: 0.6em 1em;
    margin-bottom: 0;
    background-color: var(--bg-accent);
    color: var(--text);
  }

  .cart__checkout {

    --icon-size: 20px;

    gap: 0.25em;

    .btn__text {
      margin-inline-end: calc(-1 * var(--icon-size));
    }

    .btn__icon {
      position: relative;
    }

    svg {
      position: absolute;
      margin: 0;
      transform: translateY(-1px);
      top: calc(-.5 * var(--icon-size));
      left: calc(-1 * var(--icon-size));
    }

    
  }


}