section.bespoke-product-compare {

  .compare-wrapper {
    display: grid;

    @include respond-to($large-up) {
      grid-template-columns: minmax(0, 1fr) minmax(0, 2fr)
    }

    @include respond-to($large-down) {
      grid-auto-flow: row;
      gap: var(--gap);
    }

  }

  .compare-sidebar {
    padding: var(--outer);
  }

  .compare-content {
    padding: var(--inner);
  }
  
  
  /* ----- Compare Grid ----- */
  
  .compare-grid {
    display: flex;
    align-items: flex-end;
  }
  
  .compare-grid__item {
    display: flex;
    flex-direction: column;
    @include respond-to($medium-down) {
      flex: 1 0 50%;
    }
  }
  
  .compare-grid__item-fields {
    
  }
  
  .compare-grid__item-field {

    // padding: 0 1em;
    
    display: flex;
    align-items: center;
    min-height: 40px;
  
    &:nth-child(2n-1) {
      background: var(--bg);
    }

    @include respond-to($medium-up) {
      height: 40px;
    }

  }
  
  .compare-grid__item-field--spacer {
    text-align: right;
  }
  
  .compare-grid__item-field--label {
    text-align: right;
  }
  
  .compare-grid__item-field--value {
    
  }

  /* ----- Compare Table ----- */

  .compare-table {
    @include respond-to ($medium-down) {
      flex-wrap: wrap;
    }
  }
  
  .compare-table__legend {
    flex: 0 1 230px;
    .compare-grid__item-field {
      justify-content: flex-end;
      text-align: end;
    }
  }
  
  .compare-table__legend .compare-grid__item-field {
    @include respond-to($medium-up) {
      border-top-left-radius: var(--corner-radius);
      border-bottom-left-radius: var(--corner-radius);
    }
  }
  
  .compare-table__legend .compare-grid__item-field {
    @include respond-to($medium-up) {
      border-top-left-radius: var(--corner-radius);
      border-bottom-left-radius: var(--corner-radius);
    }
  }
  
  .compare-table__product:last-of-type .product-compare-field {
    @include respond-to($medium-up) {
      border-top-right-radius: var(--corner-radius);
      border-bottom-right-radius: var(--corner-radius);
    }
  }
  
  .compare-table__spacer {
    flex: 1 0 20px;
  }
  
  .compare-table__product {
    .compare-grid__item-image {
      @include respond-to($medium-up) {
        min-width: 300px;
        height: 440px;
      }
    }
  }


  .product-compare-item__product {
    overflow: hidden;
    > .product-item {
      height: 100%;
    }
    .product-item__image {
      max-height: 100%;
    }
  }



  .product-compare-field {
    @include respond-to($medium-up) {
      overflow: hidden;
      width: 100%;
      flex: 1 0 auto;
      white-space: nowrap;
      padding-right: 1em;
    }
    @include respond-to($medium-down) {

      --padding: 10px;

      position: relative;
      align-items: flex-start;
      gap: calc(var(--padding) * 2);

      min-height: 50px !important;
      padding: var(--padding);

      &:nth-child(2n) {
        .product-compare-field__label {
          display: none;
        }
      }

      .product-compare-field__label {
        position: absolute;
        top: var(--padding);
        left: var(--padding);
      }

      .product-compare-field__value {
        @include respond-to($medium-up) {
          display: block;
          width: 100%;
        }
        padding-top: calc(var(--padding) * 2);
      }

      
    }
  }

}