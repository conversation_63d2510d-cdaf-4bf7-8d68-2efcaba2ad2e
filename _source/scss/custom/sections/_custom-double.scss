.index-image-text.custom-index-image-text {

  &.index-image-text--flush-padding {
    .brick__block__text {
      flex-basis: 100%;
      margin: 0;
    }
  }

  .hero__content {
    height: 100%;
  }

  .brick__section {
    &:not(.brick__section--reversed) {
      > .brick__block {
        overflow: hidden;
        &:first-of-type {
          @include respond-to($medium-down) {
            border-bottom-right-radius: var(--block-radius);
            border-bottom-left-radius: var(--block-radius);
          }
          @include respond-to($medium-up) {
            border-top-left-radius: var(--block-radius);
            border-bottom-left-radius: var(--block-radius);
          }
        }
        &:last-of-type {
          @include respond-to($medium-down) {
            border-top-right-radius: var(--block-radius);
            border-top-left-radius: var(--block-radius);
          }
          @include respond-to($medium-up) {
            border-top-right-radius: var(--block-radius);
            border-bottom-right-radius: var(--block-radius);
          }
        }
      }
    }
    &.brick__section--reversed {
      > .brick__block {
        overflow: hidden;
        &:first-of-type {
          @include respond-to($medium-down) {
            border-top-right-radius: var(--block-radius);
            border-top-left-radius: var(--block-radius);
          }
          @include respond-to($medium-up) {
            border-top-right-radius: var(--block-radius);
            border-bottom-right-radius: var(--block-radius);
          }
        }
        &:last-of-type {
          @include respond-to($medium-down) {
            border-bottom-right-radius: var(--block-radius);
            border-bottom-left-radius: var(--block-radius);
          }
          @include respond-to($medium-up) {
            border-top-left-radius: var(--block-radius);
            border-bottom-left-radius: var(--block-radius);
          }
        }
      }
    }
  }

  /* ----- Accordions ----- */

  collapsible-elements {
    display: flex;
    flex-direction: column;
    min-width: 0;
    height: 100%;
  }

}