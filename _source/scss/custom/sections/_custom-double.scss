.index-image-text.custom-index-image-text {

  &.index-image-text--flush-padding {
    .brick__block__text {
      flex-basis: 100%;
      margin: 0;
    }
  }

  .hero__content {
    height: 100%;
  }

  .inner-color-scheme {
    > .brick__block {
      overflow: hidden;
      &:first-child {
        border-top-right-radius: var(--block-radius);
        border-bottom-right-radius: var(--block-radius);
      }
      &:last-child {
        border-top-left-radius: var(--block-radius);
        border-bottom-left-radius: var(--block-radius);
      }
    }
    &.brick__section--reversed {
      > .brick__block {
        &:first-child {
          border-top-left-radius: var(--block-radius);
          border-bottom-left-radius: var(--block-radius);
        }
        &:last-child {
          border-top-right-radius: var(--block-radius);
          border-bottom-right-radius: var(--block-radius);
        }
      }
    }
  }

  /* ----- Accordions ----- */

  collapsible-elements {
    display: flex;
    flex-direction: column;
    min-width: 0;
    height: 100%;
  }

}