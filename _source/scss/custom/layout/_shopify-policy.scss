/* ----- BASE SHOPIFY RESET ----- */

.shopify-policy__container {
  max-width: unset;
  max-width: unset;
  margin: unset;
  padding-left: unset;
  padding-right: unset;
}

.shopify-policy__title {
  text-align: unset;
  margin: var(--section-outer-spacing-block) 0;
}

/* ----- Theme ----- */

// (Moved here from theme.css)
/*
.shopify-policy__container {
  gap: var(--spacing-12) !important;
  max-width: none !important;
  padding: var(--spacing-14) 0 !important;
  grid-auto-columns: minmax(0, 1fr) !important;
  margin-inline-start: max(var(--container-gutter), 50% - 80ch / 2) !important;
  margin-inline-end: max(var(--container-gutter), 50% - 80ch / 2) !important;
  display: grid !important;
}

.shopify-policy__container,{
  @media screen and (min-width: 700px) {
    padding-block-start: var(--spacing-16) !important;
    padding-block-end: var(--spacing-16) !important;
  }
}
*/

/* ----- Custom ----- */

.shopify-policy__title {
  
  --section-vertical-margins: calc(var(--background-differs-from-previous)*var(--section-outer-spacing-block-start, var(--section-outer-spacing-block)));
  margin-block-start: var(--section-vertical-margins);
  margin-block-end: var(--section-vertical-margins);
  
  h1 {
    @extend .h0;
    text-align: center;
  }

}

.shopify-policy__body {
  
  @extend .section;
  @extend .section--narrower;
  // @extend .section-boxed;
  // @extend .bg-custom;

  // --background: var(---background-color--content-1);

}