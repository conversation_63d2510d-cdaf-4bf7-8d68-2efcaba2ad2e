/* File: gulpfile.js */

/* ========== Paths ========== */

const devmode = true;

const sourceLiquid = './**/*.liquid',
      sourceSCSSCustom = './scss/custom/**/*.{scss,sass}',
      // sourceSCSSAccount = './scss/account/**/*.{scss,sass}',

      sourceJSCustom = './js/custom/**/*.js',
      sourceJSVendor =
        [
          './js/vendor/**/*.js'
        ],

      destinationJS = './../assets',
      destinationCSS = './../assets',

      filenameCSSCustom = 'custom.css',
      filenameCSSCustomMin = 'custom.min.css',
      // filenameCSSAccount = 'account.css',
      // filenameCSSAccountMin = 'account.min.css',

      filenameJSVendor = 'vendor.js',
      filenameJSVendorMin = 'vendor.min.js';
      filenameJSCustom = 'custom.js',
      filenameJSCustomMin = 'custom.min.js';


/* ========== Modules ========== */

const gulp = require('gulp'),
      { parallel } = require('gulp'),
      concat = require("gulp-concat"),
      replace = require("gulp-replace"),
      postcss = require("gulp-postcss"),
      autoprefixer = require("autoprefixer"),
      cssnano = require("cssnano"),
      sourcemaps = require("gulp-sourcemaps"),
      sass = require('gulp-sass')(require('sass')),
      uglify = require('gulp-uglify'),
      gulpif = require('gulp-if');


/* ========== SCSS ========== */

// Styles

function styleCustom() {

  if (devmode == true) {

    return gulp.src(sourceSCSSCustom)
      .pipe(sourcemaps.init()) // Registers sourcemaps
      .pipe(sass()).on("error", sass.logError) // Logs SCSS errors succinctly to terminal.
      .pipe(postcss([autoprefixer()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(concat(filenameCSSCustom))
      .pipe(gulp.dest(destinationCSS))
      .pipe(concat(filenameCSSCustomMin))
      .pipe(postcss([cssnano()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(sourcemaps.write('./')) // Generates sourcemaps for in-browser debugging
      .pipe(gulp.dest(destinationCSS))
      ;

  }
  else {

    return gulp.src(sourceSCSSCustom)
      .pipe(sass()).on("error", sass.logError) // Logs SCSS errors succinctly to terminal.
      .pipe(postcss([autoprefixer(), cssnano()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(concat(filenameCSSCustomMin)) // Changes the file to .liquid so Liquid tags can understand it.
      .pipe(gulp.dest(destinationCSS))
      ;

  }

}


function styleAccount() {

  if (devmode == true) {

    return gulp.src(sourceSCSSAccount)
      .pipe(sourcemaps.init()) // Registers sourcemaps
      .pipe(sass()).on("error", sass.logError) // Logs SCSS errors succinctly to terminal.
      .pipe(postcss([autoprefixer()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(concat(filenameCSSAccount)) // Changes the file to .liquid so Liquid tags can understand it.
      .pipe(gulp.dest(destinationCSS))
      .pipe(concat(filenameCSSAccountMin)) // Changes the file to .liquid so Liquid tags can understand it.
      .pipe(postcss([cssnano()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(sourcemaps.write('./')) // Generates sourcemaps for in-browser debugging
      .pipe(gulp.dest(destinationCSS))
      ;

  }
  else {

    return gulp.src(sourceSCSSAccount)
      .pipe(sass()).on("error", sass.logError) // Logs SCSS errors succinctly to terminal.
      .pipe(postcss([autoprefixer(), cssnano()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(concat(filenameCSSAccount)) // Changes the file to .liquid so Liquid tags can understand it.
      .pipe(gulp.dest(destinationCSS))
      ;

  }

}


function scriptsCustom() {

  if (devmode == true) {

    return gulp.src(sourceJSCustom)
      .pipe(sourcemaps.init()) // Registers sourcemaps
      .pipe(concat(filenameJSCustom))
      .pipe(gulpif('*.js', uglify()))
      .pipe(sourcemaps.write('./')) // Generates sourcemaps for in-browser debugging
      .pipe(gulp.dest(destinationJS));

  }
  else {

    return gulp.src(sourceJSCustom)
      .pipe(concat(filenameJSCustom))
      .pipe(gulp.dest(destinationJS));

  }

}

function scriptsVendor() {

  if (devmode == true) {

    return gulp.src(sourceJSVendor)
      .pipe(sourcemaps.init()) // Registers sourcemaps
      .pipe(concat(filenameJSVendor))
      .pipe(gulp.dest(destinationJS))
      .pipe(gulpif('*.js', uglify()))
      .pipe(concat(filenameJSVendorMin))
      .pipe(sourcemaps.write('./')) // Generates sourcemaps for in-browser debugging
      .pipe(gulp.dest(destinationJS));

  }
  else {

    return gulp.src(sourceJSVendor)
      .pipe(concat(filenameJSVendorMin))
      .pipe(gulpif('*.js', uglify()))
      .pipe(gulp.dest(destinationJS));

  }

}

/* ========== Watch ========== */

function watch() {

  console.log("👀");
  console.log("Development Mode == " + devmode);

  gulp.watch(sourceSCSSCustom, styleCustom);
  // gulp.watch(sourceSCSSAccount, styleAccount);

  gulp.watch(sourceJSCustom, scriptsCustom);
  // gulp.watch(sourceJSVendor, scriptsVendor);

}

exports.watch = watch;
exports.scriptsVendor = scriptsVendor;

exports.build = parallel(

  styleCustom,
  // styleAccount,

  scriptsVendor,
  scriptsCustom

);

exports.default = watch;
