<!doctype html>
<html class="no-js no-touch supports-no-cookies" lang="{{ request.locale.iso_code }}">
<head>
  {%- render 'head' -%}

  {{ content_for_header }}
</head>
<body id="password" class="template-password{% if customer %} customer-logged-in{% endif %}{% if settings.animations_enable %} aos-initialized{% endif %}" data-animations="{{ settings.animations_enable }}">
  {%- render 'loading' -%}

  <a class="in-page-link visually-hidden skip-link" href="#MainContent">{{ 'general.accessibility.skip_to_content' | t }}</a>

  <div class="container" data-site-container>

    <!-- CONTENT -->
    <main role="main" id="MainContent" class="main-content">

      {{ content_for_layout }}

      {% render 'zoom-pswp' %}
    </main>
  </div>
</body>
</html>
