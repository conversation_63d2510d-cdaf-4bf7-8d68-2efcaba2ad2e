<!doctype html>
<html class="no-js no-touch supports-no-cookies" lang="{{ request.locale.iso_code }}">
  <head>
    {%- render 'head' -%}

    {{ content_for_header }}
  </head>

  <body
    id="{{ page_title | handle }}"
    class="template-{{ template.name | handle }} grid-{{ settings.grid_style }}{% if customer %} customer-logged-in{% endif %}{% if settings.animations_enable %} aos-initialized{% endif %}{% if settings.product_grid_outline %} has-line-design{% endif %}"
    data-animations="{{ settings.animations_enable }}"
  >
    {%- render 'loading' -%}

    <a class="in-page-link visually-hidden skip-link" data-skip-content href="#MainContent">
      {{- 'general.accessibility.skip_to_content' | t -}}
    </a>

    <div class="container" data-site-container>
      <div class="header-sections">
        {% sections 'group-header' %}
      </div>

      {% sections 'group-overlay' %}

      <!-- CONTENT -->
      <main role="main" id="MainContent" class="main-content">
        {{ content_for_layout }}
      </main>
    </div>

    {% sections 'group-pre-footer' %}

    <footer class="footer-sections">
      {% sections 'group-footer' %}
    </footer>

    {%- if settings.swatches_type != 'disabled' -%}
      {% render 'template-swatch' %}
    {%- endif -%}

    {% render 'zoom-pswp' %}

    {%- if template.name != 'product' and settings.show_scroll_top_button -%}
      <button
        type="button"
        class="btn btn--scroll-top btn--black btn--solid"
        aria-label="{{ 'general.accessibility.scroll_to_top' | t }}"
        data-scroll-top-button
      >
        {%- render 'icon-arrow-up' -%}
      </button>
    {%- endif -%}

    {%- comment -%}
      In order to use your custom javascript file at assets/custom.js
      just cut this next line and paste it outside this comment:
    {%- endcomment -%}
    <script src="{{ 'custom.js' | asset_url }}" defer="defer"></script>
    <!-- Paste marketing code or third party scripts below this comment line ============== -->

    <script>function myInit(){ StampedFn.init({ apiKey: '529f5016-b8d2-4ab8-80fd-0663794f1046', storeUrl: '321539' }); }</script>
    <script async onload="myInit()" src="https://cdn1.stamped.io/files/widget.min.js" type="text/javascript"></script>

    <!-- And above this comment line ================================================== -->
  </body>
</html>
