{% comment %}
  /snippets/media.liquid

  Returns a media object for a product slideshow
  Accepts:
  -  media: {Object} - The media object we will use (required)
  -  product: {Object} - The current product (required)
  -  sectionkey: {String} - A section key for use in the media object js
  -  image_width: {Number} - Image width on desktop based on the setting
  -  attributes {String} - Additional attributes attached to the image wrapper tag (Optional)

  Usage:
  {% render 'media', media: media, featured_media: featured_media, enable_video_looping: enable_video_looping, sectionkey: section.id  %}
{% endcomment %}
{%- liquid
  assign image_size = '1024x1024'
  assign media_aspect_ratio = media.aspect_ratio
  unless media_aspect_ratio
    assign media_aspect_ratio = 1
  endunless
  assign media_padding_top = 1 | divided_by: media_aspect_ratio | times: 100 | round: 2

  capture image
    assign loading = loading | default: 'lazy'
    assign preload = nil
    assign fetchpriority = nil
    assign attributes = attributes | default: nil
    assign cover = cover | default: false
    assign autoplay = enable_video_autoplay | default: true
    assign show_controls = show_controls | default: false

    if template.name == 'product' and media == featured_media
      assign loading = 'eager'
      assign fetchpriority = 'high'
    endif

    if media.alt contains 'variant--'
      assign attributes = attributes | append: ' data-variant="' | append: media.alt | append: '"'
    endif

    if media.alt contains '|'
      assign alt = media.alt | split: '|' | first | strip
    endif

    assign image_sizes = '(min-width: 1400px) ' | append: image_width | append: 'px, (min-width: 750px) calc(50vw - 30px), calc(100vw - 32px)'

    render 'image' image: media.preview_image, alt: alt, sizes: image_sizes, loading: loading, fetchpriority: fetchpriority, preload: preload, attributes: attributes, aspect_ratio: media_aspect_ratio, cover: cover

  endcapture
-%}

<div
  class="product__slide {{ classes }}"
  data-image-id="{{ media.id }}"
  data-media-id="{{ sectionkey }}-{{ media.id }}"
  data-type="{{ media.media_type }}"
  {% if media.media_type == 'video' or media.media_type == 'external_video' %}
    data-video
    data-video-id="{{ media.id }}"
    data-enable-video-looping="{{ enable_video_looping }}"
  {% endif %}
  {% if media.media_type == 'model' %}
    data-model
    data-model-id="{{ media.id }}"
  {% endif %}
  {% if media.media_type == 'external_video' %}
    data-youtube-id="{{ media.external_id }}"
  {% endif %}
  data-slide
>

  {%- if media.media_type == 'image' -%}
    <div class="product__photo"
      tabindex="0"
      style="--aspect-ratio: {{ media_aspect_ratio }}"
      {% if section.settings.enable_zoom %}
        data-zoom-image
        data-image-src="{{ media | image_url: width: 3000 }}"
        data-image-width="{{ media.width }}"
        data-image-height="{{ media.height }}"
      {% endif %}
    >
      {{ image }}
    </div>
  {%- else -%}

  {%- if media.media_type == 'video' -%}
    <div class="product__media deferred-media" style="padding-top: {{ media_padding_top }}%;">
      {{ media | media_tag: image_size: image_size, class: 'media-video', autoplay: autoplay, loop: enable_video_looping, controls: show_controls, preload: 'none' }}
    </div>
  {%- else -%}
    
    {%- if media.media_type == 'model' -%}
      <product-model class="product__media deferred-media" style="padding-top: {{ media_padding_top }}%;">
    {%- else -%}
      <deferred-media class="product__media deferred-media" style="padding-top: {{ media_padding_top }}%;">
    {%- endif -%}
        <button type="button" class="deferred-media__poster" aria-label="{{ 'general.accessibility.view' | t }} {{ media.alt | strip_html | escape }}" data-deferred-media-button>
          <span class="deferred-media__poster-button">
            {%- if media.media_type == 'model' -%}
              {%- render 'icon-media-model' -%}
            {%- else -%}
              {%- render 'icon-media-video' -%}
            {%- endif -%}
          </span>
  
          {{ image }}
        </button>
  
        <template>
          {%- liquid
            case media.media_type
              when 'model'
                echo media | model_viewer_tag: image_size: image_size, toggleable: true, data-model-id: media.id
              when 'video'
                echo media | media_tag: image_size: image_size, class: 'media-video', autoplay: autoplay, loop: enable_video_looping, controls: true, preload: 'none'
              when 'external_video'
                if media.host == 'youtube'
                  echo media | external_video_url: autoplay: autoplay, playsinline: true, loop: enable_video_looping, playlist: media.external_id | external_video_tag: loading: 'lazy', data-host: media.host
                else
                  echo media | external_video_url: autoplay: autoplay, playsinline: true, loop: enable_video_looping | external_video_tag: loading: 'lazy', data-host: media.host
                endif
            endcase
          -%}
        </template>
  
    {%- if media.media_type == 'model' -%}
      </product-model>
    {%- else -%}
      </deferred-media>
    {%- endif -%}

  {%- endif -%}


  {%- endif -%}
</div>