<!-- Root element of PhotoSwipe. Must have class pswp. -->
<div class="pswp pswp--custom-loader" tabindex="-1" role="dialog" aria-hidden="true">
  <!-- Background of PhotoSwipe.
  It's a separate element as animating opacity is faster than rgba(). -->
  <div class="pswp__bg"></div>

  <!-- Slides wrapper with overflow:hidden. -->
  <div class="pswp__scroll-wrap">
    <!-- Container that holds slides.
        PhotoSwipe keeps only 3 of them in the DOM to save memory.
        Don't modify these 3 pswp__item elements, data is added later on. -->
    <div class="pswp__container">
      <div class="pswp__item"></div>
      <div class="pswp__item"></div>
      <div class="pswp__item"></div>
    </div>

    <!-- Default (PhotoSwipeUI_Default) interface on top of sliding area. Can be changed. -->
    <div class="pswp__ui pswp__ui--hidden">
      <div class="pswp__top-bar">
        <!--  Controls are self-explanatory. Order can be changed. -->

        <div class="pswp__counter"></div>

        <button class="pswp__button pswp__button--close pswp__custom-close pswp__ui-over-close" title="Close (Esc)">
          {%- render 'icon-cancel' -%}
        </button>

        <!-- Preloader demo https://codepen.io/dimsemenov/pen/yyBWoR -->
        <!-- element will get class pswp__preloader--active when preloader is running -->
        <div class="pswp__preloader">
          <div class="pswp__preloader__icn">
            <div class="pswp__preloader__cut">
              <div class="pswp__preloader__donut"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
        <div class="pswp__share-tooltip"></div>
      </div>

      <button class="pswp__button pswp__button--arrow--left" title="{{ 'products.general.previous' | t }}">
        {%- render 'icon-nav-arrow-left' -%}
        <span class="visually-hidden">{{ 'products.general.previous' | t }}</span>
      </button>

      <button class="pswp__button pswp__button--arrow--right" title="{{ 'products.general.next' | t }}">
        {%- render 'icon-nav-arrow-right' -%}
        <span class="visually-hidden">{{ 'products.general.next' | t }}</span>
      </button>

      <div class="pswp__thumbs"></div>

      <div class="pswp__caption">
        <div class="pswp__caption__center"></div>

        <a href="#" class="pswp__caption-close">
          {%- render 'icon-nav-arrow-left' -%}
          <span class="visually-hidden">{{ 'general.accessibility.close' | t }}</span>
        </a>
      </div>
    </div>
  </div>
</div>
