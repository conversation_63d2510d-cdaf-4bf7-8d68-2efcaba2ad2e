{% if product.metafields.custom.benefits != blank and product.metafields.custom.benefits.value.size > 0 %}

  {%- capture badges -%}
    {%- for benefit in product.metafields.custom.benefits.value -%}
      
      {%- capture badge_classes -%}
        badge--small
        badge--{{ benefit | handle }}
      {%- endcapture -%}

      {%- capture badge_attributes -%}
        {% if animation_name %}
          {%- assign animation_duration = animation_duration | times: foloop.index -%}
          data-animation="{{ animation_name }}"
          data-animation-duration="{{ animation_duration }}"
          data-animation-delay="{{ animation_delay }}"
        {% endif %}
      {%- endcapture -%}

      {%- render 'badge', text: benefit, classes: badge_classes, attributes: badge_attributes -%}
      
    {%- endfor -%}
  {%- endcapture -%}

  {%- if badges != blank -%}

    <div class="product__block product__head block-padding" {{ block_style }}>

      <div class="product__benefits__wrapper" {{ block.shopify_attributes }}>

        <div class="badge-list {{ classes }}">
          {{ badges }}
        </div>

      </div>

    </div>
    
  {%- endif -%}

  
  
{% endif %}

