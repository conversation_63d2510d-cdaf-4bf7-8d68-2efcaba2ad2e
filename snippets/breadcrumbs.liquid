{% assign breadcrumbs_modifier = breadcrumbs_modifier | default: '' %}

{% capture divider_html %}
  <span class="breadcrumbs__arrow">&nbsp;</span>
{% endcapture %}

<nav class="breadcrumbs {{ breadcrumbs_modifier }}">
  <a href="{{ routes.root_url }}">{{ 'general.breadcrumbs.home' | t }}</a>

  {{ divider_html }}

  {% if template contains 'product' %}
    {%- if collection -%}
      {{ collection.title | link_to: collection.url }}

      {{ divider_html }}
    {%- endif -%}
  {%- elsif template.name == 'article' -%}
    {{ blog.title | link_to: blog.url }}

    {{ divider_html }}
  {% endif %}

  <span>{{- page_title -}}</span>
</nav>