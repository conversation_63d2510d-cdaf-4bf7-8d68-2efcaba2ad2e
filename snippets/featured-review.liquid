{%- liquid
  if section.settings.select_color_scheme == "section"
    assign review_color_scheme = section.settings.review_color_scheme | default: section.settings.review_color_scheme
  elsif section.settings.select_color_scheme == "review"
    assign review_color_scheme = block.settings.color_scheme
  endif
  assign color_scheme = 'color-' | append: review_color_scheme
-%}

{%- capture classes -%}
  overflow-hidden
  block-radius
  {% if block.settings.link != blank %}product-carousel-item--link{% endif %}
  {{ color_scheme }}
  {{ classes }}
{%- endcapture -%}

{%- capture attributes -%}
  {% if block.settings.link %}
    href="{{ block.settings.link }}"
  {% endif %}
  {{ attributes }}
  data-product-carousel-id="{{ id }}"
  data-grid-item
  {{ block.shopify_attributes }}
{%- endcapture -%}

{%- capture id -%}
  id="
  {%- if id -%}
    {{- id -}}
  {%- else -%}
    section-{{ section.id }}--FeaturedReview--block-{{ block.id }}
  {%- endif -%}
  "
{%- endcapture -%}

{%- if block.settings.link != blank -%}
  {%- assign tag = 'a' -%}
{%- else -%}
  {%- assign tag = 'div' -%}
{%- endif -%}

<{{ tag }} class="featured-review {{ classes }}" {{ attributes }}>

  <div class="featured-review__inner">

    {%- comment -%} MEDIA {%- endcomment -%}

    {%- capture media -%}
      {%- capture image_content -%}
        {%- if block.settings.image != blank -%}
          {%- render 'image-hero', image: block.settings.image, desktop_height: desktop_height, mobile_height: mobile_height, sizes: image_sizes -%}
        {%- endif -%}
      {%- endcapture -%}
      {% if block.settings.video != blank and block.settings.enable_video == true %}
        <script src="{{ 'video-background.js' | asset_url }}" defer="defer"></script>
        <div
          class="video-background {{ desktop_height }} {{ mobile_height }}"
          style="--aspect-ratio: {{ video.aspect_ratio | default: 1 }};"
        >
          <div class="video__poster">
            {{ image_content }}
          </div>
          <video-background
            class="video__player is-loading"
            data-video-player
            data-video-id="{{ section.id }}-video-background"
          >
            <template data-video-template>
              {{ block.settings.video | video_tag: autoplay: true, image_size: '1085x', loop: true, muted: true, controls: false }}
            </template>
          </video-background>
        </div>
      {% else %}
        {{ image_content }}
      {% endif %}
    {%- endcapture -%}

    {%- comment -%} MEDIA {%- endcomment -%}
    
    {%- if media != blank -%}
      <div class="featured-review__media">
        {{ media }}  
      </div>
    {%- endif -%}

    {%- comment -%} CONTENT {%- endcomment -%}

    {%- capture content_top -%}
      
      {%- if block.settings.rating != "" -%}
        <div class="featured-review__rating">
          {% render 'rating', rating: block.settings.rating | plus: 0 %}
        </div>
      {%- endif -%}

      {%- if block.settings.eyebrow -%}
        <h3 class="featured-review__eyebrow {{ section.settings.review_eyebrow_font_size }}">{{ block.settings.eyebrow }}</h3>
      {%- endif -%}

      {%- if block.settings.title -%}
        <h3 class="featured-review__title {{ section.settings.review_title_font_size }}">{{ block.settings.title }}</h3>
      {%- endif -%}

      {%- if block.settings.review_text -%}
        <div class="featured-review__text {{ section.settings.review_text_font_size }}">{{ block.settings.review_text }}</div>
      {%- endif -%}

      {%- if block.settings.author -%}
        <div class="featured-review__author {{ section.settings.review_author_font_size }}">{{ block.settings.author }}</div>
      {%- endif -%}

    {%- endcapture -%}

    {%- capture content_bottom -%}

      {% comment %} IMAGE CAPTION {% endcomment %}

      {%- assign model_name = block.settings.model_name -%}
      {%- assign product_name = block.settings.product_name | default: product.title -%}
      {%- assign option_name = block.settings.option_name -%}

      {%- capture caption_content -%}
        {%- if model_name != blank and product_name != blank and option_name != blank -%}
          
          {%- comment -%}
          {%- capture option_icon -%}
            {%- if option_name -%}
            {{ swatch_color }}
            {%- endif -%}
            {{ option_icon }}
          {%- endcapture -%}
          {%- endcomment -%}

          {%- assign swatch_color = block.settings.swatch_color | default: '#DCBFA6' -%}

          {% if swatch_color != blank %}
            <span class="featured-review__caption-swatch simple-swatch" style="--swatch-color: {{ swatch_color }};"></span>
          {% endif %}

          <span class="featured-review__caption-text">{{ 'misc.product.image_caption_option' | t: model_name: model_name, product_name: product_name, option_name: option_name }}</span>
        {%- elsif model_name != blank and product_name != blank -%}
          <span class="featured-review__caption-text">{{ 'misc.product.image_caption' | t: model_name: model_name, product_name: product_name, option_name: block.settings.model_option }}</span>
        {%- endif -%}
      {%- endcapture -%}

      {%- if caption_content != blank -%}
        <div class="featured-review__caption">
          {{ caption_content }}
        </div>
      {%- endif -%}

    {%- endcapture -%}

    {%- capture review_content -%}
      {%- if content_top != blank -%}
        <div class="featured-review__content-top">
          {{ content_top }}
        </div>
      {%- endif -%}
      {%- if content_bottom != blank -%}
        <div class="featured-review__content-bottom">
          {{ content_bottom }}
        </div>
      {%- endif -%}
    {%- endcapture -%}
  
    {%- if review_content != blank -%}
      <div class="featured-review__content">
        {{ review_content }}
      </div>
    {%- endif -%}
    
  </div>

</{{ tag }}>