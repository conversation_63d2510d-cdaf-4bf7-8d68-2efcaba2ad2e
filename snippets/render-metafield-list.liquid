{% comment %} Special Parsing {% endcomment %}

{%- assign tag = tag | default: 'span' -%}
{%- assign tag_classes = tag_classes | default: 'span--comma' -%}

{%- if special == "badges" -%}
  {%- assign tag = 'div' -%}
  {%- assign tag_classes = 'badge badge--xs badge--secondary badge--border' -%}
  {%- assign wrapper_tag = 'div' -%}
  {%- assign wrapper_tag_classes = 'badge-list' -%}
{%- endif -%}

{% comment %} Tags {% endcomment %}

{%- capture tag_classes -%}
  {%- if tag_classes -%}
    class="{{ tag_classes }}"
  {%- endif -%}
{%- endcapture -%}

{% comment %} Wrapper {% endcomment %}

{%- capture wrapper_tag_classes -%}
  {%- if wrapper_tag_classes -%}
    class="{{ wrapper_tag_classes }}"
  {%- endif -%}
{%- endcapture -%}


{% comment %} DISPLAY {% endcomment %}

{%- assign metafield_value = metafield_value | strip -%}

{%- if wrapper_tag != blank -%}
  <{{ wrapper_tag }} {{ wrapper_tag_classes }}>
{%- endif -%}

{%- for metafield_value in metafield.value -%}
  {%- assign metafield_type = metafield.type | replace: "list.", "" -%}
  <{{ tag }} {{ tag_classes }} {{ tag_attributes }}>
    {{special}} {%- render 'render-metafield', metafield: metafield_value, metafield_type: metafield_type, metafield_value: item -%}
  </{{ tag }}>
{%- endfor -%}

{%- if wrapper_tag != blank -%}
</{{ wrapper_tag }}>
{%- endif -%}