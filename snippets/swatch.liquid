{% comment %} 
  This snippet outputs a simple swatch based on the shop's filter settings and theme's styling,
  without being connected to a link or a filter functionality.
{% endcomment %}

{%- liquid
  capture swatch_button_style
    case settings.swatch_style
      when 'square'
        echo 'swatch__button--square'
      when 'circle'
        echo 'swatch__button--circle'
    endcase
  endcapture
-%}

{%- for filter in filters_default -%}

  {%- for filter_value in filter.values -%}

    {%- liquid
      assign option_name_handle_separator = filter.label | handle | prepend: ',' | append: ','
      assign is_swatch_option = false
      assign is_native_swatch_option = false

      if settings.swatches_type == 'theme'
        assign swatch_translation = 'general.swatches.color' | t
        assign translation_string = swatch_translation | remove: '  ' | replace: ', ', ',' | replace: ' ,', ',' | replace: ',', '____' | handle | replace: '____', ',' | append: ',' | prepend: ','

        if translation_string contains option_name_handle_separator
          assign is_swatch_option = true
        endif
      elsif settings.swatches_type == 'native'
        for value in option.values
          if value.swatch
            assign is_native_swatch_option = true
            break
          endif
        endfor
      endif
    -%}

    {%- if is_swatch_option and filter.presentation != 'swatch' -%}
      <!-- Theme Swatch -->
      
      <span
        class="swatches swatch__button {{ swatch_button_style }} swatch-{{ filter_value.label | handle }}"
        data-swatch="{{ filter_value.label | escape_once }}"
        style="--swatch: var(--{{ filter_value.label | handle }});"
      ></span>

      {%- break -%}
    {%- elsif is_native_swatch_option -%}
      <!-- Native Swatch -->

      {%- liquid
        if value.swatch.image
          assign image_url = value.swatch.image | image_url: width: 96
          assign swatch_value = 'url(' | append: image_url | append: ')'
        elsif value.swatch.color
          assign swatch_value = 'rgb(' | append: value.swatch.color.rgb | append: ')'
        else
          assign swatch_value = nil
        endif

        assign is_white = false
        if swatch_value == 'rgb(255 255 255)' and value.swatch.image == blank
          assign is_white = true
        endif
      -%}

      <span
        class="swatches swatch__button {{ swatch_button_style }} swatch-{{ filter_value.label | handle }}"
        data-swatch="{{ filter_value.label | escape_once }}"
        style="--swatch: var(--{{ filter_value.label | handle }});"
      ></span>

      {%- break -%}
    {%- endif -}

  {%- endfor -}

{%- endfor -}