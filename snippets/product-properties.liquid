<div class="product__block{% if settings.show_lines %} product__block--lines{% endif %} select__fieldset block-padding" {{ block.shopify_attributes }} {{ block_style }}>
  {%- if block.settings.label != blank -%}
    {%- assign input_id = unique | append: '-' | append: block.id -%}
    {%- assign required = block.settings.required -%}
    {%- case block.settings.type -%}
      {%- when 'text' -%}
        <label for="{{ input_id }}" class="select__label">{{ block.settings.label | escape_once }}</label>

        <input
          type="text" class="properties__input"
          id="{{ input_id }}"
          form="{{ product_form_id }}"
          {% if required %} required {% endif %}
          name="properties[{{ block.settings.label | escape_once }}]">

      {%- when 'checkbox' -%}
        {%- if block.settings.unchecked_value != blank and block.settings.checked_value != blank -%}
          <div class="properties__checkbox checkbox">
            <input
              type="hidden"
              name="properties[{{ block.settings.label | escape_once }}]"
              form="{{ product_form_id }}"
              value="{{ block.settings.unchecked_value }}">

            <input
              type="checkbox"
              name="properties[{{ block.settings.label | escape_once }}]"
              id="{{ input_id }}"
              form="{{ product_form_id }}"
              value="{{ block.settings.checked_value }}">

            <label
              class="radio__fieldset__label"
              for="{{ input_id }}">{{ block.settings.label | escape_once }}</label>
          </div>
        {%- endif -%}

    {%- when 'dropdown' -%}
      {%- capture line_item_options -%}
        <ul id="{{ input_id }}" class="select-popout__list" data-popout-list data-scroll-lock-scrollable>
          {%- assign has_selected = false -%}
          {%- assign selected_value = '' -%}

          {%- for i in (1..3) -%}
            {%- assign value = 'option_' | append: forloop.index -%}
            {%- assign value = block.settings[value] -%}

            {%- if value != blank -%}
              <li class="select-popout__item{% unless has_selected %} is-active{% endunless %}">
                <a class="select-popout__option"
                  href="#"
                  {% unless has_selected %}aria-current="true"{% endunless %}
                  data-value="{{ value | escape_once }}"
                  data-popout-option>
                  <span>
                    {{ value | escape_once }}
                  </span>
                </a>
              </li>

              {%- unless has_selected -%}
                {%- assign selected_value = value -%}
              {%- endunless -%}
              {%- assign has_selected = true -%}
            {%- endif -%}
          {%- endfor -%}
        </ul>
      {%- endcapture -%}

      {%- if has_selected -%}
        <label
          class="select__label"
          id="{{ input_id }}-label"
          for="{{ input_id }}">{{ block.settings.label | escape_once }}</label>

        <popout-select class="select-popout">
          <button type="button"
            class="select-popout__toggle"
            aria-expanded="false"
            aria-controls="{{ input_id }}"
            aria-labelledby="{{ input_id }}-label"
            data-popout-toggle>
            <span data-popout-toggle-text>{{ selected_value }}</span>
            {%- render 'icon-nav-arrow-down' -%}
          </button>

          {{ line_item_options }}

          <input
            type="hidden"
            name="properties[{{ block.settings.label | escape_once }}]"
            id="{{ input_id }}"
            value="{{ selected_value | escape_once }}"
            form="{{ product_form_id }}"
            data-popout-input
            data-single-option-selector>
        </popout-select>
      {%- endif -%}
    {%- endcase -%}
  {%- endif -%}
</div>