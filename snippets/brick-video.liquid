{% comment %}
  Renders image block for custom content or newsletter sections

  Accepts:
  - block: {Object} Block object
  - animation_anchor: {String} ID of the element that will trigger animations

  Usage:
  {% render 'brick-image', block: block, animation_anchor: animation_anchor %}
{% endcomment %}

{%- liquid
  assign title = block.settings.title
  assign text = block.settings.text
  assign link = block.settings.link
  assign link_text = block.settings.link_text

  assign selected_color_scheme = section.settings.color_scheme | strip
  assign scheme_bg_color = settings.color_schemes[selected_color_scheme].settings.section_bg

  assign bg_color = block.settings.bg_color | default: scheme_bg_color
  assign text_color = block.settings.color
  assign overlay_opacity = block.settings.overlay_opacity | times: 0.01
  assign overlay_color = block.settings.overlay_color
  assign show_overlay_text = block.settings.show_overlay_text
  assign show_text_background = block.settings.show_text_background
  assign desktop_height = section.settings.height
  assign mobile_height = block.settings.mobile_height | default: section.settings.mobile_height
  assign wrapper_width = section.settings.width

  assign hero_transparent = true
  if show_text_background and scheme_bg_color.alpha != 0.0 or scheme_bg_color != blank or bg_color.alpha != 0.0 or bg_color != blank
    assign hero_transparent = false
  endif

  assign show_header_backdrop = false
  if hero_transparent and show_overlay_text
    assign show_header_backdrop = true
  endif

  assign button_style = block.settings.button_style
  if button_style == 'btn--text' and block.settings.show_arrow
    assign button_style = button_style | append: ' btn--text-no-underline'
  endif

  assign animation_order = 0

  capture video_poster
    render 'image-hero', image_desktop: image_desktop, image_mobile: image_mobile, desktop_height: desktop_height, mobile_height: mobile_height, aspect_ratio: video.aspect_ratio, aspect_ratio_mobile: video.aspect_ratio
  endcapture
  
-%}

{%- capture style -%}
  {%- unless bg_color.alpha == 0.0 or bg_color == blank -%}
    --bg: {{ bg_color }};
  {%- endunless -%}

  {%- unless text_color.alpha == 0.0 or text_color == blank -%}
    --text: {{ text_color }};
    --text-light: {{ text_color | color_mix: bg_color, 70 }};
    --text-dark: {{ text_color | color_saturate: 10 | color_darken: 15 }};
  {%- endunless -%}

  {%- unless overlay_color.alpha == 0.0 or overlay_color == blank -%}
    --overlay-bg: {{ overlay_color }};
  {%- endunless -%}
{%- endcapture -%}

<div
  class="brick__block {% if section.settings.block_radius == true %}block-radius overflow-hidden{% endif %}"
  {% if style != blank %}
    style="{{ style }}"
  {% endif %}
  {{ block.shopify_attributes }}
>

  <div
    class="video-background {{ desktop_height }} {{ mobile_height }}"
    style="--aspect-ratio: {{ video.aspect_ratio | default: 1 }};"
  >
    <div class="video__poster">
      {{ video_poster }}
    </div>
    <video-background
      class="video__player is-loading"
      data-video-player
      data-video-id="{{ section.id }}-video-background"
    >
      <template data-video-template>
        {{ video | video_tag: autoplay: true, image_size: '1085x', loop: true, muted: true, controls: false }}
      </template>
    </video-background>
  </div>

</div>