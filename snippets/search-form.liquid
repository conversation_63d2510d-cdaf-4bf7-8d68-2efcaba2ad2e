<main-search>
  <div class="search-outer">
    <form action="{{ routes.search_url }}" class="search-form">
      <input name="options[prefix]" type="hidden" value="last">
      <div class="input-holder">
        <input
          type="search"
          name="q"
          class="search-box"
          aria-label="{{ 'general.search.placeholder' | t }}"
          placeholder="{{ 'general.search.title' | t }}"
          value="{{ search.terms }}">
          <button type="reset" class="search-reset{% if search.terms == blank %} hidden{% endif %}" aria-label="{{ 'general.search.reset' | t }}">
            {{- 'general.search.clear' | t -}}
          </button>
        </div>
      <button type="submit" class="btn btn--primary btn--solid search-submit" aria-label="{{ 'general.search.search' | t }}">{%- render 'icon-search' -%}</button>
    </form>
  </div>
</main-search>