<!-- /snippets/cart-bar.liquid -->

{%- liquid
  assign current_variant = product.selected_or_first_available_variant
  assign selling_plan_hide = true
  assign button_add = false
  assign blocks_form = section.blocks | where: 'type', 'form'

  if settings.currency_code_enable
    assign current_variant_price = current_variant.price | money_with_currency
    assign current_variant_compare_at_price = current_variant.compare_at_price | money_with_currency
  else
    assign current_variant_price = current_variant.price | money
    assign current_variant_compare_at_price = current_variant.compare_at_price | money
  endif

  assign any_variant_available = false
  for variant in product.variants
    if variant.available
      assign any_variant_available = true
      break
    endif
  endfor
-%}

{% if product.selling_plan_groups.size > 0 and blocks_form.size > 0 and blocks_form[0].settings.subscriptions_enable_selectors %}
  {%- assign selling_plan_hide = false -%}
{% endif %}

{%- if product.has_only_default_variant and selling_plan_hide -%}
  {%- assign button_add = true -%}
{%- endif -%}

<cart-bar id="cart-bar" class="cart-bar color-scheme-1">
  <div class="cart-bar__form__wrapper form__wrapper{% if button_add and current_variant.available != true %} variant--soldout{% endif %}{% if settings.show_newsletter %} show-product-notification{% endif %}{% unless any_variant_available %} all-variants--soldout{% endunless %}" data-form-wrapper>
    <div class="cart-bar__info">
      <h4 class="cart-bar__product__title text-product-title text-product-title--large">{{ product.title | strip_html }}</h4>

      {%- capture cart_bar_options -%}

        {% assign swatch_translation = 'general.swatches.color' | t %}
        {% assign translation_string = swatch_translation | remove: '  ' | replace: ', ', ',' | replace: ' ,', ',' | replace: ',', '____' | handle | replace: '____', ',' | append: ',' | prepend: ',' %}

        {% for option in product.options_with_values %}

          {%- liquid

            comment
              Determine if current option matches swatch handle translations
            endcomment

            assign current_value = current_variant.options[forloop.index0]

            assign option_name = option.name
            assign option_name_downcase = option_name | handle
            assign option_value = current_value

            assign is_swatch_option = false
            assign is_native_swatch_option = false

            assign is_color_option = false
            assign swatch_translation = 'general.swatches.color' | t
            assign translation_string = swatch_translation | remove: '  ' | replace: ', ', ',' | replace: ' ,', ',' | replace: ',', '____' | handle | replace: '____', ',' | append: ',' | prepend: ','

            if translation_string contains option_name_downcase
              assign is_color_option = true

              if settings.swatches_type == 'theme'
                assign is_swatch_option = true
              endif
            endif

            if settings.swatches_type == 'native'
              for value in option.values
                if value.swatch
                  assign is_native_swatch_option = true
                  break
                endif
              endfor
            endif

          -%}

          <div class="cart-bar__option" data-option-name="{{ option.name }}" data-option='{{ option | json }}'>

            {%- if is_swatch_option or is_native_swatch_option -%}

              {%- liquid
                if is_native_swatch_option
                  if value.swatch.image
                    assign image_url = value.swatch.image | image_url: width: 96
                    assign swatch_value = 'url(' | append: image_url | append: ')'
                  elsif value.swatch.color
                    assign swatch_value = 'rgb(' | append: value.swatch.color.rgb | append: ')'
                  else
                    assign swatch_value = nil
                  endif
                endif

                assign is_white = false
                if swatch_value == 'rgb(255 255 255)' and value.swatch.image == blank
                  assign is_white = true
                endif
              -%}
              <div class="cart-bar-swatch swatches" style="--swatch: var(--{{ option_value | handle }});">
                <div class="cart-bar-swatch__swatch swatches swatch__button swatch__button--circle swatch-dark" data-option-color></div>
                <div class="cart-bar-swatch__label">
                  <span class="cart-bar__option-label visually-hidden text-xs">{{ option_name }}</span>
                  <span class="cart-bar__option-value text-badge-lg" data-option-value>{{ option_value }}</span>
                </div>
              </div>

            {%- else -%}

              <div class="cart-bar__option-text text-badge-lg">
                <span class="cart-bar__option-label visually-hidden text-xs">{{ option_name }}</span>
                <span class="cart-bar__option-value text-badge-lg" data-option-value>{{ option_value }}</span>
              </div>

            {%- endif -%}

          </div>

        {% endfor %}
      {%- endcapture -%}

      {%- if cart_bar_options != blank -%}
        <div class="cart-bar__options">
          {{ cart_bar_options }}
          <button type="button" class="text-sm product__submit__add {% unless button_add %} product__submit__add--default{% endunless %}" data-cart-bar-scroll>{{ 'misc.change' | t }}</button>
        </div>
      {%- endif -%}

      {%- comment -%}
      <div class="price cart-bar__product__price product__price" data-price-wrapper>
        <span data-product-price {% if current_variant.compare_at_price > current_variant.price %} class="product__price--sale"{% endif %}>
          {%- if current_variant.price == 0 -%}
            {{ 'general.money.free' | t }}
          {%- else -%}
            {{ current_variant_price }}
          {%- endif -%}
        </span>

        {% if product.compare_at_price_max > product.price %}
          <span class="visually-hidden" data-compare-text>{{ 'products.product.regular_price' | t }}</span>
          <s class="product__price--strike" data-compare-price>
            {% if current_variant.compare_at_price > current_variant.price %}
              {{ current_variant_compare_at_price }}
            {% endif %}
          </s>
        {% endif %}
      </div>
      {%- endcomment -%}
       
    </div>

    <div class="cart-bar__form">
      <div class="cart-bar__submit product__submit product__submit--spb">
        
        <button type="button"
          class="btn btn--solid btn--primary btn--large product__submit__add{% unless button_add %} product__submit__add--default{% endunless %}"
          {% if button_add %} data-add-to-cart-bar{% elsif any_variant_available %} data-cart-bar-scroll{% endif %}
          {% if any_variant_available == false or button_add and current_variant.available != true %} disabled="disabled"{% endif %}>
          <span class="btn__text"{% if button_add %} data-add-to-cart-text{% endif %}>
            {%- if button_add -%}
              {%- assign preorder_check = false -%}

              {%- if product.tags contains '_preorder' or product.metafields.theme.preorder.value -%}
                {%- assign preorder_check = true -%}
              {%- endif -%}

              {%- if current_variant.available and preorder_check -%}
                {{ 'products.product.pre_order' | t }}
              {%- elsif current_variant.available -%}
                {{ 'products.product.add_to_cart' | t }}
              {%- else -%}
                {{ 'products.product.sold_out' | t }}
              {%- endif -%}
            {%- elsif any_variant_available -%}
              {{ 'products.product.configure' | t }}
            {%- else -%}
              {{ 'products.product.sold_out' | t }}
            {%- endif -%}
          </span>

          <span class="btn__loader">
            <svg height="18" width="18" class="svg-loader">
              <circle r="7" cx="9" cy="9" />
              <circle stroke-dasharray="87.96459430051421 87.96459430051421" r="7" cx="9" cy="9" />
            </svg>
          </span>

          <span class="btn__added">&nbsp;</span>

          {%- comment -%}
          {%- unless button_add or any_variant_available == false -%}
            {%- render 'icon-nav-arrow-up' -%}
          {%- endunless -%}
          {%- endcomment -%}
        </button>

        {%- if settings.show_newsletter -%}
          {%- assign newsletter_text = 'general.newsletter_form.newsletter_product_availability' | t -%}
          {%- assign button_text = 'products.product.sold_out' | t | append: ' - ' | append: newsletter_text -%}

          <button type="button"
            class="btn btn--primary btn--outline product__submit__add product__submit__add--default product__cart-bar-notification-button"
            data-cart-bar-product-notification>
            <span class="btn__text">{{ button_text }}</span>
          </button>
        {%- endif -%}
      </div>
    </div>
  </div>
</cart-bar>
