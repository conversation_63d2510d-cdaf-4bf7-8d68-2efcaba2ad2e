{% style %}


  /* ======================== Fonts ======================== */


  /* ----- <PERSON><PERSON>eu<PERSON> ----- */

  @font-face {
    font-family: '<PERSON><PERSON> Neue';
    src: 
      url({{ 'maisonneue-regular-webfont.woff2' | asset_url }}) format('woff2'),
      url({{ 'maisonneue-regular-webfont.woff' | asset_url }}) format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: '<PERSON>son Neue';
    src: 
      url({{ 'maisonneue-medium-webfont.woff2' | asset_url }}) format('woff2'),
      url({{ 'maisonneue-medium-webfont.woff' | asset_url }}) format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: '<PERSON><PERSON> Neue';
    src: 
      url({{ 'maisonneue-semibold-webfont.woff2' | asset_url }}) format('woff2'),
      url({{ 'maisonneue-semibold-webfont.woff' | asset_url }}) format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
  }


  /* ----- Maison Neue Extended ----- */

  @font-face {
    font-family: 'Maison Neue Extended';
    src: 
      url({{ 'maisonneueextended-light-webfont.woff2' | asset_url }}) format('woff2'),
      url({{ 'maisonneueextended-light-webfont.woff' | asset_url }}) format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Maison Neue Extended';
    src: 
      url({{ 'maisonneueextended-book-webfont.woff2' | asset_url }}) format('woff2'),
      url({{ 'maisonneueextended-book-webfont.woff' | asset_url }}) format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Maison Neue Extended';
    src: 
      url({{ 'maisonneueextended-demi-webfont.woff2' | asset_url }}) format('woff2'),
      url({{ 'maisonneueextended-demi-webfont.woff' | asset_url }}) format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
  }

  html:root {

    /* ======================== FONTS ======================== */

    --font-family-heading-1: urw-din-condensed, sans-serif;
    --font-family-heading-1-alt: urw-din-semi-condensed, sans-serif;
    --font-family-heading-2: 'Maison Neue Extended', sans-serif;
    --font-family-heading-2-alt: 'Maison Neue', sans-serif;
    --font-family-body: 'Maison Neue', sans-serif;
    --font-family-button: 'Maison Neue Extended', sans-serif;

    --font-family-product-price: var(--font-family-body);


    /* ======================== Spacing ======================== */

    {% assign iterations = 10 %}
    {% assign spacing_value = 5 %}
    {% assign spacing_unit = 'px' %}

    --spacing-unit: {{ spacing_value }}{{ spacing_unit }};
    {% for i in (1..iterations) %}
    --spacing-{{i}}: calc(var(--spacing-unit) * {{i}}); // {{ spacing_value | times: i }}{{ spacing_unit }};
    {%- endfor %}


    /* ======================== Color Scheme (reference only) ======================== */

    /*
    --accent: var(--COLOR-ACCENT);
    --accent-fade: var(--COLOR-ACCENT-FADE);
    --accent-hover: var(--COLOR-ACCENT-HOVER);
    --border: var(--COLOR-BORDER);
    --border-dark: var(--COLOR-BORDER-DARK);
    --border-light: var(--COLOR-BORDER-LIGHT);
    --border-hairline: var(--COLOR-BORDER-HAIRLINE);
    --bg: var(--COLOR-BG-GRADIENT, var(--COLOR-BG));
    --bg--rgb: var(--COLOR-BG-RGB);
    --bg-accent: var(--COLOR-BG-ACCENT);
    --bg-accent-lighten: var(--COLOR-BG-ACCENT-LIGHTEN);
    --icons: var(--COLOR-TEXT);
    --link: var(--COLOR-LINK);
    --link-a50: var(--COLOR-LINK-A50);
    --link-a70: var(--COLOR-LINK-A70);
    --link-hover: var(--COLOR-LINK-HOVER);
    --link-opposite: var(--COLOR-LINK-OPPOSITE);
    --text: var(--COLOR-TEXT);
    --text-dark: var(--COLOR-TEXT-DARK);
    --text-light: var(--COLOR-TEXT-LIGHT);
    --text-hover: var(--COLOR-TEXT-HOVER);
    --text-a5: var(--COLOR-TEXT-A5);
    --text-a35: var(--COLOR-TEXT-A35);
    --text-a50: var(--COLOR-TEXT-A50);
    --text-a80: var(--COLOR-TEXT-A80);
    */

    /* ======================== Colors ======================== */

    /* ---------- Basic ---------- */

    --color-basic-black: {{ settings.color_black }};
    --color-basic-white: {{ settings.color_white }};
    --color-basic-offwhite: {{ settings.color_offwhite }};
    --color-basic-offwhite-warm: {{ settings.color_offwhite_warm }};
    --color-basic-offwhite-cool: {{ settings.color_offwhite_cool }};

    --color-basic-black--rgb: {{ settings.color_black }};
    --color-basic-white--rgb: {{ settings.color_white }};
    --color-basic-offwhite--rgb: {{ settings.color_offwhite }};
    --color-basic-offwhite-warm--rgb: {{ settings.color_offwhite_warm }};
    --color-basic-offwhite-cool--rgb: {{ settings.color_offwhite_cool }};

    /* ---------- Brand ---------- */

    --color-brand-1: {{ settings.color_brand_1 }};
    --color-brand-2: {{ settings.color_brand_2 }};
    --color-brand-3: {{ settings.color_brand_3 }};
    --color-brand-4: {{ settings.color_brand_4 }};
    --color-brand-5: {{ settings.color_brand_5 }};
    --color-brand-6: {{ settings.color_brand_6 }};
    --color-brand-7: {{ settings.color_brand_7 }};
    --color-brand-8: {{ settings.color_brand_8 }};

    --color-brand-1--rgb: {{ settings.color_brand_1.rgb }};
    --color-brand-2--rgb: {{ settings.color_brand_2.rgb }};
    --color-brand-3--rgb: {{ settings.color_brand_3.rgb }};
    --color-brand-4--rgb: {{ settings.color_brand_4.rgb }};
    --color-brand-5--rgb: {{ settings.color_brand_5.rgb }};
    --color-brand-6--rgb: {{ settings.color_brand_6.rgb }};
    --color-brand-7--rgb: {{ settings.color_brand_7.rgb }};
    --color-brand-8--rgb: {{ settings.color_brand_8.rgb }};

    /* ---------- Price ---------- */

    --color-price: {{ settings.color_price }};
    --color-price--compare: {{ settings.color_price_compare }};
    --color-price--sale: {{ settings.color_price_sale }};
    
    --color-price--rgb: {{ settings.color_price }};
    --color-price--compare--rgb: {{ settings.color_price_compare }};
    --color-price--sale--rgb: {{ settings.color_price_sale }};

    /* ---------- Components ---------- */

    /* --- Badges --- */

    --color-badge--secondary--background: {{ settings.color_badge_secondary_background }};
    --color-badge--secondary--text: {{ settings.color_badge_secondary_text }};
    --color-badge--secondary--border: {{ settings.color_badge_secondary_border }};


    /* --- Product Item --- */

    --product-item-image-background-padding: {{ settings.product_item_image_background_padding }}%;
    --product-item-image-background-padding--mobile: {{ settings.product_item_image_background_padding_mobile }}%;
    
    --product-item-image-extra-padding: {{ settings.product_item_image_bottom_padding }}px;

    {% if settings.product_item_image_background_color != blank and settings.product_item_image_background_color != 'rgba(0,0,0,0)' -%}
      --product-item-image-background-color: {{ settings.product_item_image_background_color }};
    {%- endif %}
    {% if settings.product_item_image_background_color != blank and settings.product_item_image_background_color != 'rgba(0,0,0,0)' -%}
      --product-item-image-background-color: {{ settings.product_item_image_background_color }};
    {%- endif %}
    --product-item-image-var: unset;
    

    /* ======================== Typography ======================== */

    /* ---------- Headings ---------- */

    --font-size-h0: 48px;
    --font-size-h1: 48px;
    --font-size-h2: 32px;
    --font-size-h3: 32px;
    --font-size-h4: 40px;
    --font-size-h5: 32px;
    --font-size-h6: 20px;

    --letter-spacing-heading: 0.08em;

    /* ---------- Subheadings ---------- */
    
    --font-size-subheading: 20px;
    --font-size-eyebrow: 13px;
    --font-size-eyebrow-2: 15px;
    
    --font-weight-subheading: 500;
    --font-weight-eyebrow: 500;
    --font-weight-eyebrow-2: 600;
    
    --letter-spacing-subheading: 0;
    --letter-spacing-eyebrow: 0.07em;
    --letter-spacing-eyebrow-2: 0.08em;

    /* ---------- Text ---------- */

    --font-size-text-xs: 11px;
    --font-size-text-sm: 12px;
    --font-size-text: 13px;
    --font-size-text-lg: 14.5px;
    --font-size-text-xl: 16px;

    --letter-spacing-text: 0;
    
    --font-weight-body: 400;
    --font-weight-body-bold: 600;

    /* ---------- Buttons ---------- */
    
    --font-size-button: 10px;
    --font-size-button-lg: 11px;

    --font-weight-button: 700;
    --line-height-button: 1;

    /* ---------- Product Card ---------- */

    --font-size-product-card-title: 16px;
    --font-size-product-card-title-large: 18px;
    --font-size-product-card-description: 12px;
    --font-size-product-card-price: 13px;

    --font-weight-product-title: 600;
    --font-weight-product-price: 600;
    --font-weight-product-price: 600;

    /* ---------- Misc. ---------- */

    --font-size-badge: 10px;
    --font-size-badge-lg: 13px;
    --font-size-caption: 11px;
    --font-size-navigation: 13px;

    --font-weight-badge: 600;
    --font-weight-badge-lg: 600;

    --letter-spacing-navigation: 0.03em;
    --letter-spacing-badge: 0.03em;
    --letter-spacing-badge-lg: 0.03em;


    /* ======================== Styles ======================== */

    /* ---------- Shadows ---------- */



    /* ---------- Border Radius ---------- */

    --corner-radius-sm: 2px;
    --corner-radius: 4px;
    --corner-radius-lg: 8px;

    --block-radius-sm: 2px;
    --block-radius: 4px;
    --block-radius-lg: 8px;

    --section-radius-sm: 2px;
    --section-radius: 4px;
    --section-radius-lg: 8px;


    /* ---------- Misc. ---------- */
    
    --transition-duration: 0.3s;
    --transition-ease: ease-out;

    --aspect-ratio-mobile: calc(var(--PRODUCT-GRID-ASPECT-RATIO--MOBILE) + var(--product-item-image-background-padding--mobile));
    --aspect-ratio-desktop: calc(var(--PRODUCT-GRID-ASPECT-RATIO) + var(--product-item-image-background-padding));

    /* ---------- SVGs ---------- */

    --icon-plus: url("{{ 'icon-plus.svg' | asset_url }}");
    --icon-check: url("{{ 'icon-check.svg' | asset_url }}");
    /* --icon-add-cart: url("{{ 'icon-add-cart.svg' | asset_url }}"); */

  }

  @media only screen and (min-width: 990px) {

    html:root {

      /* ---------- Headings ---------- */

      --font-size-h0: 101px;
      --font-size-h1: 101px;
      --font-size-h2: 48px;
      --font-size-h3: 40px;
      --font-size-h4: 64px;
      --font-size-h5: 48px;
      --font-size-h6: 28px;

      /* ---------- Subheadings ---------- */

      --font-size-subheading: 24px;
      --font-size-eyebrow: 15px;
      --font-size-eyebrow-2: 13px;

      /* ---------- Text ---------- */

      --font-size-text-xs: 12px;
      --font-size-text-sm: 13px;
      --font-size-text: 15px;
      --font-size-text-lg: 17px;
      --font-size-text-xl: 18px;

      --text-xs: var(--font-size-text-xs);
      --text-sm: var(--font-size-text-sm);
      --text-base: var(--font-size-text);
      --text-lg: var(--font-size-text-lg);
      --text-xl: var(--font-size-text-xl);

      /* ---------- Buttons ---------- */

      --font-size-button: 10px;
      --font-size-button-lg: 12px;

      /* ---------- Product Card ---------- */

      --font-size-product-card-title: 19px;
      --font-size-product-card-title-large: 24px;
      --font-size-product-card-description: 14px;
      --font-size-product-card-price: 15px;

      /* ---------- Misc. ---------- */

      --font-size-badge: 12px;
      --font-size-badge-lg: 14.5px;
      --font-size-caption: 12px;
      --font-size-navigation: 13px;

    }

  }

  
  /* ============ THEME OVERRIDES ============ */
  
  html:root {

    /* ======================== Layout ======================== */

    --gutter: var(--LAYOUT-GUTTER);
    --gap: calc(var(--gutter) / 4);


    /* ======================== Color ======================== */

    /* === Product sale color ===*/
    --COLOR-SALE: {{ settings.sale_color }};

    /* === Helper colors for form error states ===*/
    --COLOR-ERROR: {{ settings.color_error_text }};
    --COLOR-ERROR-BG: {{ settings.color_error_background }};
    --COLOR-ERROR-BORDER: {{ settings.color_error_border }};

    --COLOR-SUCCESS: {{ settings.color_success_text }};
    --COLOR-SUCCESS-BG: rgba({{ settings.color_success_background }}, 0.2);
    --COLOR-SUCCESS-BORDER: {{ settings.color_success_border }};

    /* --COLOR-HEADER-LINK: {{ settings.header_link }}; */

    /* --COLOR-MENU-BG: {{ settings.menu_bg_color }}; */

    /* --COLOR-SUBMENU-BG: {{ settings.submenu_bg_color }}; */

    {% comment %}
    {% unless template.name == 'product' %}
      --COLOR-MENU-TRANSPARENT: {{ settings.menu_transparent_color }};
      --COLOR-MENU-TRANSPARENT-HOVER: {{ settings.menu_transparent_color }};
    {% else %}
      --COLOR-MENU-TRANSPARENT: {{ settings.header_link }};
      --COLOR-MENU-TRANSPARENT-HOVER: {{ settings.header_link }};
    {% endunless %}
    {% endcomment %}

    /* === Default overlay opacity ===*/
    /* --overlay-opacity: 0; */
    /* --underlay-opacity: {{ backdrop_opacity }}; */
    /* --underlay-bg: {{ backdrop_bg }}; */
    /* --header-overlay-color: transparent; */

    /* === Custom Cursor ===*/
    /* --ICON-ZOOM-IN: url( "{{ 'icon-zoom-in.svg' | asset_url }}" ); */
    /* --ICON-ZOOM-OUT: url( "{{ 'icon-zoom-out.svg' | asset_url }}" ); */

    /* === Custom Icons ===*/

    /*
    --ICON-ADD-BAG: url( "{{ 'icon-add-bag-bold.svg' | asset_url }}" );
    --ICON-ADD-CART: url( "{{ 'icon-add-cart-bold.svg' | asset_url }}" );
    --ICON-ARROW-LEFT: url( "{{ 'icon-arrow-left-bold.svg' | asset_url }}" );
    --ICON-ARROW-RIGHT: url( "{{ 'icon-arrow-right-bold.svg' | asset_url }}" );
    --ICON-SELECT: url("{{ 'icon-select-bold.svg' | asset_url }}");
    */
    
    /* === Typography ===*/
    --FONT-HEADING-MINI: var(--font-size-h6);
    --FONT-HEADING-X-SMALL: var(--font-size-h5);
    --FONT-HEADING-SMALL: var(--font-size-h4);
    --FONT-HEADING-MEDIUM: var(--font-size-h3);
    --FONT-HEADING-LARGE: var(--font-size-h2);
    --FONT-HEADING-X-LARGE: var(--font-size-h1);
    
    --FONT-HEADING-MINI-MOBILE: var(--font-size-h6);
    --FONT-HEADING-X-SMALL-MOBILE: var(--font-size-h5);
    --FONT-HEADING-SMALL-MOBILE: var(--font-size-h4);
    --FONT-HEADING-MEDIUM-MOBILE: var(--font-size-h3);
    --FONT-HEADING-LARGE-MOBILE: var(--font-size-h2);
    --FONT-HEADING-X-LARGE-MOBILE: var(--font-size-h1);

    /*
    --FONT-STACK-BODY: {{ base_font.family }}, {{ base_font.fallback_families }};
    --FONT-STYLE-BODY: {{ base_font.style }};
    --FONT-WEIGHT-BODY: {{ base_font.weight }};
    --FONT-WEIGHT-BODY-BOLD: {{ base_font_bold.weight | default: 700 }};
    */

    --FONT-STACK-BODY: var(--font-family-body);
    --FONT-STYLE-BODY: normal;
    --FONT-WEIGHT-BODY: var(--font-weight-body);
    --FONT-WEIGHT-BODY-BOLD: var(--font-weight-body-bold);

    --LETTER-SPACING-BODY: var(--letter-spacing-text);
    --LETTER-SPACING-SUBHEADING: var(--letter-spacing-subheading);;
    --LETTER-SPACING-NAV: var(--letter-spacing-navigation);

    /*
    --FONT-STACK-HEADING: {{ heading_font.family }}, {{ heading_font.fallback_families }};
    --FONT-WEIGHT-HEADING: {{ heading_font.weight }};
    --FONT-STYLE-HEADING: {{ heading_font.style }};
    --FONT-UPPERCASE-HEADING: {{ heading_font_uppercase }};
    */

    --FONT-STACK-HEADING: var(--font-family-heading-1);
    --FONT-STACK-HEADING-2: var(--font-family-heading-2);

    /*
    --FONT-STACK-SUBHEADING: {{ subheading_font.family }}, {{ subheading_font.fallback_families }};
    --FONT-WEIGHT-SUBHEADING: {{ subheading_font.weight }};
    --FONT-STYLE-SUBHEADING: {{ subheading_font.style }};
    */

    /*
    --FONT-UPPERCASE-SUBHEADING: {{ subheading_font_uppercase }};
    */

    /*
    --FONT-STACK-NAV: {{ nav_font.family }}, {{ nav_font.fallback_families }};
    --FONT-WEIGHT-NAV: {{ nav_font.weight }};
    --FONT-WEIGHT-NAV-BOLD: {{ nav_font_bold.weight | default: 700 }};
    --FONT-STYLE-NAV: {{ nav_font.style }};
    
    */

    --FONT-STACK-NAV: var(--font-family-body);
    --FONT-SIZE-NAV: var(--font-size-navigation);
    
    --FONT-SIZE-SUBHEADING-DESKTOP: var(--font-size-subheading);
    --FONT-SIZE-SUBHEADING-MOBILE: var(--font-size-subheading);
    
    --FONT-SIZE-BASE: var(--font-size-text);

    --BTN-FONT-STACK: var(--font-family-button);
    --BTN-FONT-SIZE: var(--font-size-button-lg);
    --BTN-FONT-WEIGHT: var(--font-weight-button);

    --swatch-size: 12px;
    --swatch-size-product: 20px;
    --swatch-size-filters: 16px;

    --FREE-SHIPPING-GRADIENT: var(--bg-accent-lighten);

  }

  /* === Backdrop ===*/
  ::backdrop {
    /* --underlay-opacity: {{ backdrop_opacity }}; */
    /* --underlay-bg: {{ backdrop_bg }}; */
  }

  html:root {

    --font-body-x-large: var(--font-size-text-xl);
    --font-body-large: var(--font-size-text-lg);
    --font-body-medium: var(--font-size-text);
    --font-body-small: var(--font-size-text-sm);
    --font-body-x-small: var(--font-size-text-xs);
    --font-nav-large: 1;
    --font-nav-medium: 1;
    --font-nav-small: 1;

  }

  @media only screen and (min-width: 750px) and (max-width: 989px) {

    html:root {

      --font-body-x-large: var(--font-size-text-xl);
      --font-body-large: var(--font-size-text-lg);
      --font-body-medium: var(--font-size-text);
      --font-body-small: var(--font-size-text-sm);
      --font-body-x-small: var(--font-size-text-xs);
      --font-nav-large: 1;
      --font-nav-medium: 1;
      --font-nav-small: 1;

    }

  }

  @media only screen and (min-width: 990px) {

    html:root {

      --font-body-x-large: var(--font-size-text-xl);
      --font-body-large: var(--font-size-text-lg);
      --font-body-medium: var(--font-size-text);
      --font-body-small: var(--font-size-text-sm);
      --font-body-x-small: var(--font-size-text-xs);
      --font-nav-large: 1;
      --font-nav-medium: 1;
      --font-nav-small: 1;

    }

  }


  /* ============ THEME OVERRIDES ============ */

  @media only screen and (min-width: 990px) {

    html:root {
      --gap: calc(var(--gutter) / 2);
    }

  }



{% endstyle %}