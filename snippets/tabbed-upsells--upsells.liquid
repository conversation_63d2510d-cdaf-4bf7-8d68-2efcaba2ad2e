{%- liquid
  assign upsell_product = product.metafields.theme.upsell.value | default: block.settings.upsell_product
  assign upsell_product_list = product.metafields.theme.upsell_list.value | default: block.settings.upsell_product_list
  assign slider_enabled = false
  assign block_tag = 'div'

  if block.settings.layout == 'slider'
    assign slider_enabled = true
    assign block_tag = 'slider-component'
    assign dots_style = settings.dots_style
    assign autoplay_speed = false
    assign autoplay = block.settings.autoplay

    if autoplay
      assign autoplay_speed = block.settings.autoplay_speed | times: 1000
    endif
  endif
-%}

{%- if upsell_product != blank or upsell_product_list != blank or request.design_mode -%}

  {%- capture upsell_products -%}
    {%- if upsell_product == blank and upsell_product_list == blank -%}
      {%- render 'upsell-product', show_discounts: true -%}
    {%- else -%}
      {%- if upsell_product != blank -%}
        {%- render 'upsell-product', show_discounts: true, upsell_product: upsell_product, show_available_upsell_only: block.settings.show_available_upsell_only, slider_enabled: slider_enabled, is_product_block: true -%}
      {%- endif -%}

      {%- if upsell_product_list != blank -%}
        {%- for upsell_product in upsell_product_list -%}
          {%- render 'upsell-product', show_discounts: true, upsell_product: upsell_product, show_available_upsell_only: block.settings.show_available_upsell_only, slider_enabled: slider_enabled, is_product_block: true -%}
        {%- endfor -%}
      {%- endif -%}
    {%- endif -%}
  {%- endcapture -%}

  {%- if upsell_products != blank -%}
    <div
      class="product__block upsell-products block-padding"
      {{ block.shopify_attributes }}
      {{ block_style }}
    >
      <{{ block_tag }}
        class="product__upsell product__upsell--{{ block.settings.layout }}"
        {% if slider_enabled %}
          data-slider
          data-dots="{{ dots_style }}"
          data-options='{"autoPlay": {{ autoplay_speed }}, "prevNextButtons": false }'
        {% endif %}
        {{ block.shopify_attributes }}
        {{ block_style }}
      >
        {{ upsell_products }}
      </{{ block_tag }}>
    </div>
  {%- endif -%}
{%- endif -%}