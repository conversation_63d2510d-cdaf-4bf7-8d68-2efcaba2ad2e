{%- if block.settings.custom_liquid != blank -%}
  {{ block.settings.custom_liquid }}
{%- else -%}
  {%- capture icon -%}
    {%- if block.settings.icon != blank -%}
      {{- block.settings.icon -}}
    {%- elsif icon -%}
      {{ icon }}
    {%- elsif settings.loyalty_icon != '' -%}
      {%- render 'animated-icon', filename: settings.loyalty_icon -%}
    {%- endif -%}
  {%- endcapture -%}

  {%- capture text -%}
    {%- if points == blank and settings.loyalty_points_conversion != blank -%}
      {%- assign points = product.price | divided_by: 100 | times: settings.loyalty_points_conversion -%}
    {%- endif -%}
    {%- if points != blank -%}
      {{- 'loyalty.earn_html' | t: points: points -}}
    {%- endif -%}
  {%- endcapture -%}

  {%- if text != blank -%}
    <loyalty-points class="loyalty-points" 
      data-product-price=="{{ product.price }}" 
      data-loyalty-conversion-factor=="{{ settings.loyalty_points_conversion }}"
      data-loyalty-earnings-copy="{{- 'loyalty.earn_html' | t: points: points -}}">
      {%- if icon != blank -%}
        <span class="loyalty-points__icon">
          {{ icon }}
        </span>
      {%- endif -%}
      <span class="loyalty-points__text text-sm">{{ text }}</span>
    </loyalty-points>
  {%- endif -%}
{%- endif -%}