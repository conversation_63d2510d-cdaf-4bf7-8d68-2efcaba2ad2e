<!-- /snippets/product-column-item.liquid -->
{% comment %}
    Renders a product item in sidebar column

    Accepts:
    - product: {Object} product (required)

    Usage:
    {% render 'product-column-item', product: product %}
{% endcomment %}
{%- liquid
  if settings.currency_code_enable
    assign product_price_min = product.price_min | money_with_currency
    assign product_compare_at_price = product.compare_at_price | money_with_currency
  else
    assign product_price_min = product.price_min | money
    assign product_compare_at_price = product.compare_at_price | money
  endif
-%}

<a href="{{ product.url }}" class="widget__column">
  {%- if product.featured_media -%}
    <div class="widget__column__image">
      {%- render 'image', image: product.featured_media.preview_image, width: 210, widths: '105, 210, 360, 450', sizes: '105px', cover: true -%}
    </div>
  {%- endif -%}

  <div class="widget__column__contents">
    <p class="widget__column__title"><strong>{{ product.title | strip_html }}</strong></p>
    <p class="meta--light">
      <span class="price{% if product.compare_at_price > product.price %} sale{% endif %}">
      {% if product.available %}
        <span class="new-price">
          {% if product.price_varies %}
            <small>{{ 'products.general.from' | t }}</small>
          {% endif %}
          {%- if product.price_min == 0 -%}
            {{ 'general.money.free' | t }}
          {%- else -%}
            {{ product_price_min }}
          {%- endif -%}
        </span>
        {% if product.compare_at_price > product.price %}
          <span class="old-price">{{ product_compare_at_price }}</span>
        {% endif %}
      {% else %}
        <span class="sold-out">{{ 'products.product.sold_out' | t }}</span>
      {% endif %}
      </span>
    </p>
  </div>
</a>
