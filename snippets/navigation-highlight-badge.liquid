<!-- <PERSON>IGHLIGHT BADGES -->

{%- if link != blank -%}

  {%- for n in (1..10) -%}

    {%- capture setting_text -%}menu_highlight_{{ n }}_text{%- endcapture -%}  
    {% comment %} {%- capture setting_text_parent -%}menu_highlight_{{ n }}_text_parent{%- endcapture -%}   {% endcomment %}

    {%- if settings[setting_text] != blank -%}

      {%- if settings[setting_text] == link.title -%}

        {%- capture setting_highlight_color -%}menu_highlight_{{ n }}_highlight_color{%- endcapture -%}  
        {%- capture setting_highlight_text_color -%}menu_highlight_{{ n }}_highlight_text_color{%- endcapture -%}  
        {% comment %} {%- capture setting_badge_type -%}menu_highlight_{{ n }}_badge_type{%- endcapture -%}   {% endcomment %}
        {%- capture setting_badge_text -%}menu_highlight_{{ n }}_badge_text{%- endcapture -%}  

        {%- assign badge_text = settings[setting_text] -%}
        {% comment %} {%- assign badge_text_parent = settings[setting_text_parent] -%} {% endcomment %}
        {%- assign badge_highlight_color = settings[setting_highlight_color] -%}
        {%- assign badge_highlight_text_color = settings[setting_highlight_text_color] -%}
        {% comment %} {%- assign badge_type = settings[setting_badge_type] -%} {% endcomment %}
        {%- assign badge_text = settings[setting_badge_text] -%}
        
        {%- break -%}
        
      {%- endif -%}
      
    {%- endif -%}

  {%- endfor -%}

  {%- if badge_text != blank -%}

    {%- capture classes -%}
      {{ classes }}
      {%- comment -%}
      {% if badge_type != blank %}
        badge--{{ badge_type }}
      {% endif %}
      {%- endcomment -%}
    {%- endcapture -%}

    {%- capture style -%}
      {% if badge_highlight_color != blank and badge_highlight_color != 'rgba(0,0,0,0)' %}
        background: {{ badge_highlight_color }};
        border-color: {{ badge_highlight_color }};
      {% endif %}
      {% if badge_highlight_text_color != blank and badge_highlight_text_color != 'rgba(0,0,0,0)' %}
        color: {{ badge_highlight_text_color }};
      {% endif %}
    {%- endcapture -%}

    {%- capture style -%}
      {%- if style != blank -%}
        style="{{ style }}"
      {%- endif -%}
    {%- endcapture -%}

    {%- capture attributes -%}
      {{ style }}
    {%- endcapture -%}
    
    {%- render 'badge', text: badge_text, classes: classes, attributes: attributes -%}

  {%- endif -%}

{%- endif -%}