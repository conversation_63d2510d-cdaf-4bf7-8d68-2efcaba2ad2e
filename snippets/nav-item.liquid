{%- liquid
  assign highlight_item = highlight_item | downcase | strip_html | escape
  assign link_title_lowercase = link.title | downcase | strip_html | escape
  assign parent = false
  assign grandparent = false
  if link.levels == 1
    assign parent = true
  endif
  if link.levels == 2
    assign grandparent = true
  endif

  assign child_count = 0
  if grandparent
    assign child_count = link.links.size
    if child_count > 10
      assign child_count = 10
    endif
  endif

  if parent or grandparent
    assign key = link.url | append: link.title | append: link.levels | md5
  endif

  if parent and link.levels == 1
    assign child_count = child_count | plus: 1
  endif

  assign dropdown_wrapper_classes = blank
  assign dropdown_collection = blank
  assign dropdown_collection_limit = section.settings.dropdown_collection_limit

  for block in section.blocks
    if block.type == 'collection'
      if block.settings.link_title == link.title
        assign dropdown_collection = block.settings.collection
        assign dropdown_collection_limit = block.settings.collection_limit
        assign dropdown_collection_block = block
        break
      endif
      if dropdown_collection == blank and section.settings.use_link_collections == true
        assign dropdown_collection_handle = 'navigation-' | append: link.handle
        assign dropdown_collection = collections[dropdown_collection_handle]
      endif
      assign dropdown_collection_limit = block.settings.collection_limit
    endif
  endfor

  capture dropdown_wrapper_classes
    if dropdown_collection == blank
      echo 'header__dropdown__wrapper--no-collection'
    endif
  endcapture

  assign column_classes = 'dropdown__column'

  assign major_menus_string = section.settings.megamenu_major_menus
  assign major_menus = major_menus_string | newline_to_br | split: '<br />'

  assign megamenu_columns_max = 6
  assign megamenu_columns_free = section.settings.dropdown_collection_limit
  assign megamenu_collection = blank 
  # markup container for the mega menu, reset on every pass

  assign link_column_amount = 0
  
-%}

{%- capture dropdown_links -%}
  {%- if grandparent -%}
    {% comment %} <div class="header__grandparent__links"> {% endcomment %}
      {% if link.levels == 2 %}
        {% for link in link.links %}
          {%- assign link_title_lowercase = link.title | downcase | strip_html | escape -%}

          {%- assign is_major_menu = false -%}
          {%- assign hide_heading = false -%}

          {%- assign link_column_amount = link_column_amount | plus: 1 -%}

          {%- for major_menu in major_menus -%}

            {%- assign major_menu_stripped = major_menu | strip -%}
            {%- assign major_menu_display = major_menu | strip | replace: "*", "" -%}

            {%- if link.title == major_menu_display -%}

              {%- assign is_major_menu = true -%}
              {%- if major_menu_stripped contains '*' -%}
                {%- assign hide_heading = true -%}
              {%- endif -%}

              {%- break -%}

            {%- endif -%}

          {%- endfor -%}

          {%- capture links_column_classes -%}
            {{ column_classes }}
            {% if is_major_menu %}dropdown__family--major{% endif %}
          {%- endcapture -%}

          <div class="dropdown__family {{ links_column_classes }}">
            {% if hide_heading == false %}
              <a
                href="{{ link.url }}"
                data-stagger-first
                class="navlink navlink--child{% if highlight_item == link_title_lowercase %} navlink--highlight{% endif %}"
              >
                <span class="navtext">
                  {{ link.title | strip_html | escape }}

                  {%- render 'superscript', link_collection: link -%}
                </span>
              </a>
            {% endif %}
            {% for link in link.links %}

              {%- capture link_classes -%}
                {%- if is_major_menu == true -%}
                  text-badge-lg
                {%- else -%}
                  text-body
                {%- endif -%}
              {%- endcapture -%}

              {%- assign link_title_lowercase = link.title | downcase | strip_html | escape -%}

              <a
                href="{{ link.url }}"
                data-stagger-second
                class="navlink navlink--grandchild{% if highlight_item == link_title_lowercase %} navlink--highlight{% endif %}"
              >
                <span class="navtext {{ link_classes }}">
                  {{ link.title | strip_html | escape }}
                  {%- render 'superscript', link_collection: link -%}
                </span>

                {%- capture badge -%}
                  {%- capture classes -%}badge--xs{%- endcapture -%}
                  {%- render 'navigation-highlight-badge', link: link, classes: classes -%}
                {%- endcapture -%}

                {%- if badge != blank -%}
                  <span class="navbadge">
                    {{ badge }}
                  </span>
                {%- endif -%}

              </a>

            {% endfor %}
          </div>
        {% endfor %}
      {% elsif link.levels == 1 %}
        <div class="dropdown__family">
          {% for link in link.links %}
            {%- assign link_title_lowercase = link.title | downcase | strip_html | escape -%}
            <a
              href="{{ link.url }}"
              data-stagger
              class="navlink navlink--grandchild{% if highlight_item == link_title_lowercase %} navlink--highlight{% endif %}"
            >
              <span class="navtext">
                {{ link.title | strip_html | escape }}

                {%- render 'superscript', link_collection: link -%}
              </span>
            </a>
          {% endfor %}
        </div>
      {% endif %}
    {% comment %} </div> {% endcomment %}

  {%- else -%}
    {% comment %} Simple dropdown {% endcomment %}
    {% for link in link.links %}
      {%- assign link_title_lowercase = link.title | downcase | strip_html | escape -%}
      <a
        href="{{ link.url }}"
        data-stagger
        class="navlink navlink--child{% if highlight_item == link_title_lowercase %} navlink--highlight{% endif %}"
      >
        <span class="navtext">
          {{ link.title | strip_html | escape }}

          {%- render 'superscript', link_collection: link -%}
        </span>
      </a>
    {% endfor %}
  {%- endif -%}
{%- endcapture -%}

{%- capture megamenu_collection -%}
  
  {%- assign megamenu_link_columns = link_column_amount -%}
  {%- assign megamenu_columns_free = megamenu_columns_max | minus: megamenu_link_columns -%}
  {%- assign megamenu_collection_columns = 0 -%}

  {%- if dropdown_collection_limit < megamenu_columns_free -%}
    {%- assign product_limit = dropdown_collection_limit -%}
  {%- else -%}
    {%- assign product_limit = megamenu_columns_free -%}
  {%- endif -%}

  {%- if dropdown_collection != blank -%}
    {%- for product in dropdown_collection.products limit: product_limit -%}
      {%- render 'megamenu-item-product', product: product, classes: column_classes, block: dropdown_collection_block -%}
      {%- assign megamenu_collection_columns = megamenu_collection_columns | plus: 1 -%}
    {%- endfor -%}
  {%- endif -%}

{%- endcapture -%}

{% capture classes %}{% if grandparent %} grandparent kids-{{ child_count }}{% if image_count > 0 %} images-{{ image_count }}{% endif %} {% elsif parent %} parent{% else %} child{% endif %}{% endcapture %}

{%- if dropdown_links != blank or megamenu_collection != blank -%}
  {%- assign expands = true -%}
{%- endif -%}

{%- liquid 
  assign item_tag = 'div'
  if expands
    assign item_tag = 'hover-disclosure'
  endif
-%}

<{{ item_tag }}
  class="menu__item {% if megamenu_collection != blank %}grandparent{% endif %} {{ classes }}"
  {% if expands %}
    aria-haspopup="true"
    aria-expanded="false"
    aria-controls="{{ dropdown_id }}"
    role="button"
  {% endif %}
>
  <a
    href="{{ link.url }}"
    data-top-link
    class="navlink navlink--toplevel{% if highlight_item == link_title_lowercase %} navlink--highlight{% endif %}"
  >
    <span class="navtext">{{ link.title | strip_html | escape }}</span>
  </a>
  {% if expands %}
    <div
      class="header__dropdown"
      id="{{ dropdown_id }}"
    >

      {% capture style %}
        --megamenu-columns-max: {{ megamenu_columns_max }};
        --megamenu-collection-columns: {{ megamenu_collection_columns }};
        --megamenu-link-columns: {{ megamenu_link_columns }};
      {% endcapture %}

      <div class="header__dropdown__wrapper {{ dropdown_wrapper_classes }}" style="{{ style }}">

        <div class="header__dropdown__outer">
          {%- if dropdown_links != blank -%}
            <div class="header__dropdown__inner">
              {{ dropdown_links }}
            </div>
          {%- endif -%}
  
          {%- if megamenu_collection != blank -%}
            <div class="header__dropdown__collection">
              <div class="megamenu-products">
                {{ megamenu_collection }}
              </div>
            </div>
          {%- endif -%}

        </div>

        {% if link.url != '#' and link.url != '#!' %}
          <div class="header__dropdown__actions">
            <a href="{{ link.url }}" class="btn btn--large btn--secondary btn--full">
              {{ 'misc.shop_all' | t }} {{ link.title }}
            </a>
          </div>
        {% endif %}

      </div>
    </div>
  {% endif %}
</{{ item_tag }}>
