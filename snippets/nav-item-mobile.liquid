{%- liquid
  assign animation_name = 'drawer-items-fade'
  assign animation_duration = 500
  assign animation_delay_min = 50
  assign animation_delay_additional = 200
  assign highlight_item = highlight_item | downcase | strip_html | escape
  assign link_title_lowercase = link.title | downcase | strip_html | escape
  assign expands = false
  assign block_count = 0
  assign link_level = link_level | default: 0
  assign link_level_next = link_level | plus: 1
  assign forloop_index = forloop_index | default: 1

  assign text_font_size = text_font_size | default: block.settings.text_font_size
  assign text_font_size_secondary = text_font_size_secondary | default: block.settings.text_font_size_secondary

  assign dropdown_collection_handle = 'navigation-' | append: link.handle
  assign dropdown_collection = collections[dropdown_collection_handle]
  assign dropdown_collection_limit = block.settings.dropdown_collection_limit

  if section
    assign index = index | append: ""
    for block in section.blocks
      if block.settings.position == index
        assign block_count = block_count | plus: 1
      endif
    endfor
  endif

  if link.levels > 0 or block_count > 0 or dropdown_collection != blank
    assign expands = true
    assign key = link.url | append: link.title | append: link.levels | append: unique | append: block.type | append: index | md5
  endif

  if secondary_menu
    assign expands = false
  endif
-%}

{%- capture link_markup -%}
  {%- comment -%}
  {%- if link.url == "#" -%}
    <span class="sliderow__title {{ text_font_size }} {% if secondary_menu %} sliderow__title--secondary{% endif %}{% if highlight_item == link_title_lowercase %} sliderow__title--highlight{% endif %}" data-sliderule-close="sliderule-{{ key }}">{{ link.title | strip_html | escape }}</span>
  {%- else -%}
    <a class="sliderow__title {{ text_font_size }} {% if secondary_menu %} sliderow__title--secondary{% endif %}{% if highlight_item == link_title_lowercase %} sliderow__title--highlight{% endif %}" href="{{ link.url }}">
      <span>
        {{ link.title | strip_html | escape }}

        {%- if expands or link_level > 0 -%}
          {%- render 'superscript', link_collection: link -%}
        {%- endif -%}
      </span>
    </a>
  {%- endif -%}
  {%- endcomment -%}

  <!-- link - {{ link }} -->
  <!-- link.handle - {{ link.handle }} -->
  <!-- dropdown_collection_handle - {{ dropdown_collection_handle }} -->
  <!-- dropdown_collection - {{ dropdown_collection }} -->

  {%- if link.url == "#" or expands -%}
    <span class="sliderow__title {{ text_font_size }} {% if secondary_menu %} sliderow__title--secondary{% endif %}{% if highlight_item == link_title_lowercase %} sliderow__title--highlight{% endif %}" data-sliderule-close="sliderule-{{ key }}">
  {%- else -%}
    <a class="sliderow__title {{ text_font_size }} {% if secondary_menu %} sliderow__title--secondary{% endif %}{% if highlight_item == link_title_lowercase %} sliderow__title--highlight{% endif %}" href="{{ link.url }}">
  {%- endif -%}

    {{ link.title | strip_html | escape }}

    {%- capture highlight_badges -%}
      {%- capture classes -%}
        sliderow__highlight-badge
      {%- endcapture -%}
      {%- render 'navigation-highlight-badge', link: link, classes: classes -%}
    {%- endcapture -%}

    {%- if highlight_badges != blank -%}
      {{- highlight_badges -}}
    {%- endif -%}

  {%- if link.url == "#" or expands -%}
    </span>
  {%- else -%}
    </a>
  {%- endif -%}

{%- endcapture -%}

<div class="sliderule__wrapper{% if secondary_menu %} sliderule__wrapper--secondary{% endif %}">
  {%- if expands and block.type != 'menu-columns' -%}
    <mobile-sliderule id="sliderule-{{ key }}">
      <button class="sliderow" type="button"
        data-animates="{{ link_level }}"
        data-animation="{{ animation_name }}"
        data-animation-delay="{{ animation_delay | plus: animation_delay_additional }}"
        data-animation-duration="{{ animation_duration }}"
        data-sliderule-open="sliderule-{{ key }}">
        <span class="sliderow__title {{ text_font_size }} {% if secondary_menu %} sliderow__title--secondary{% endif %}{% if highlight_item == link_title_lowercase %} sliderow__title--highlight{% endif %}">
          <span>
            {{ link.title | strip_html | escape }}

            {%- if link_level > 0 -%}
              {%- render 'superscript', link_collection: link -%}
            {%- endif -%}
            
          </span>

          {%- capture highlight_badges -%}
            {%- capture classes -%}
              sliderow__highlight-badge
            {%- endcapture -%}
            {%- render 'navigation-highlight-badge', link: link, classes: classes -%}
          {%- endcapture -%}

          {%- if highlight_badges != blank -%}
            {{- highlight_badges -}}
          {%- endif -%}

          <span class="sliderule__chevron sliderule__chevron--right">
            {%- render 'icon-chevron-right' -%}
            <span class="visually-hidden">{{ 'general.accessibility.show_menu' | t }}</span>
          </span>
        </span>
      </button>

      <div class="mobile__menu__dropdown sliderule__panel"
        data-sliderule="{{ link_level_next }}"
        data-scroll-lock-scrollable>
        <div class="sliderow sliderow--back"
          data-animates="{{ link_level_next }}"
          data-animation="{{ animation_name }}"
          data-animation-delay="150"
          data-animation-duration="{{ animation_duration }}">

          <button class="sliderow__back-button" type="button" data-sliderule-close="sliderule-{{ key }}">
            <span class="sliderule__chevron sliderule__chevron--left">
              {%- render 'icon-chevron-left' -%}
              <span class="visually-hidden">{{ 'general.accessibility.exit_menu' | t }}</span>
            </span>
          </button>
          
          {{ link_markup }}

          {%- capture highlight_badges -%}
            {%- capture classes -%}
              sliderow__highlight-badge
            {%- endcapture -%}
            {%- render 'navigation-highlight-badge', link: link, classes: classes -%}
          {%- endcapture -%}

          {%- if highlight_badges != blank -%}
            {{- highlight_badges -}}
          {%- endif -%}

          {% if link.url == "#" %}
          {% elsif link.url != blank %}
            <a class="sliderow__shop-all-link btn btn--text" href="{{ link.url }}">
              {%- if link.type == "collection_link" -%}
                {{ 'misc.shop_all' | t }}  
              {%- else -%}
                {{ 'misc.learn_more' | t }}
              {%- endif -%}
            </a>
          {% endif %}

        </div>
        <div class="sliderow__links" data-links>
          {%- for link in link.links -%}
            {%- assign index_recursive = index | append: 'x' | append: forloop.index -%}
            {%- assign animation_delay = 50 | times: forloop.index -%}

            {%- render 'nav-item-mobile', link: link, index: index_recursive, forloop_index: forloop.index, link_level: link_level_next, highlight_item: highlight_item, animation_delay: animation_delay, unique: unique -%}
          {%- endfor -%}

          {% comment %} Render block content {% endcomment %}
          {%- if section and section.blocks.size > 0 -%}
            {%- assign header_blocks = '' -%}
            {%- assign animation_delay_next = 50 -%}

            {%- unless link.links.size > 0 -%}
              {%- assign animation_delay = animation_delay | divided_by: forloop_index | at_least: animation_delay_min | minus: animation_delay_next -%}
            {%- endunless -%}

            {%- assign animation_delay = animation_delay | plus: animation_delay_additional -%}

            {%- for block in section.blocks -%}
              {%- if block.settings.position == index -%}
                {%- assign animation_delay = animation_delay | plus: animation_delay_next -%}

                {%- capture header_blocks -%}
                  {{ header_blocks }}

                  {% render 'header-block', block: block, link_level_next: link_level_next, animation_delay: animation_delay, is_mobile: true %}
                {%- endcapture -%}
              {%- endif -%}
            {%- endfor -%}

            {%- if header_blocks != '' -%}
              <div class="sliderule-grid blocks-{{ block_count }}">
                {{ header_blocks }}
              </div>
            {%- endif -%}
          {%- endif -%}
        </div>


        {% comment %} Dropdown Filters {% endcomment %}

        {%- capture dropdown_filters -%}
          
          {%- if link.type == "collection_link" -%}
            {%- assign filters_string = 'Transfer-Free,Gradual,Color-correcting,Rapid' -%}
            {%- assign filters = filters_string | split: "," -%}
            {%- for filter in filters -%}
              <a class="sliderow__title {{ text_font_size_secondary }} " href="#">
                {{ filter | strip }}
              </a>
            {%- endfor -%}
          {%- endif -%}
          
        {%- endcapture -%}

        {%- if dropdown_filters != blank -%}

          {%- liquid
            assign animation_delay = animation_delay | plus: animation_delay_additional
            assign filter_name = 'Benefit'
          -%}

          <div class="sliderow__filters"
            data-animates="{{ link_level_next }}"
            data-animation="{{ animation_name }}"
            data-animation-delay="{{ animation_delay | plus: animation_delay_additional }}"
            data-animation-duration="{{ animation_duration }}"
          >
            <div class="dropdown-filters">
              <p class="dropdown-filters__title text-badge-lg">
                Shop by {{ filter_name }}
              </p>
              {{ dropdown_filters }}
            </div>
          </div>
        {%- endif -%}


        {% comment %} Dropdown Collection {% endcomment %}

        {%- capture dropdown_collection -%}
          {%- if dropdown_collection != blank -%}

            {%- for product in dropdown_collection.products limit: dropdown_collection_limit -%}
              {%- render 'nav-item-product', product: product -%}
            {%- endfor -%}
            
          {%- endif -%}
        {%- endcapture -%}

        {%- if dropdown_collection != blank -%}

          {%- liquid
            assign animation_delay = animation_delay | plus: animation_delay_additional
          -%}

          <div class="sliderow__collection"
            data-animates="{{ link_level_next }}"
            data-animation="{{ animation_name }}"
            data-animation-delay="{{ animation_delay | plus: animation_delay_additional }}"
            data-animation-duration="{{ animation_duration }}"
          >
            <div class="dropdown-collection">
              {{ dropdown_collection }}
            </div>
          </div>
        {%- endif -%}
      </div>
    </mobile-sliderule>
  {%- else -%}

    {% comment %} Single menu item, no nesting {% endcomment %}
    <div class="sliderow"
      role="button"
      data-animates="{{ link_level }}"
      data-animation="{{ animation_name }}"
      data-animation-delay="{{ animation_delay | plus: animation_delay_additional }}"
      data-animation-duration="{{ animation_duration }}">
      {{ link_markup }}
    </div>
  {%- endif -%}
</div>