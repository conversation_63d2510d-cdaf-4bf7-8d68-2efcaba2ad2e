{%- liquid
  assign animation_name = 'drawer-items-fade'
  assign animation_duration = 500
  assign animation_delay_min = 50
  assign animation_delay_additional = 200
  assign highlight_item = highlight_item | downcase | strip_html | escape
  assign link_title_lowercase = link.title | downcase | strip_html | escape
  assign expands = false
  assign block_count = 0
  assign link_level = link_level | default: 0
  assign link_level_next = link_level | plus: 1
  assign forloop_index = forloop_index | default: 1

  if section
    assign index = index | append: ""
    for block in section.blocks
      if block.settings.position == index
        assign block_count = block_count | plus: 1
      endif
    endfor
  endif

  if link.levels > 0 or block_count > 0
    assign expands = true
    assign key = link.url | append: link.title | append: link.levels | append: unique | append: block.type | append: index | md5
  endif

  if secondary_menu
    assign expands = false
  endif
-%}

{%- capture link_markup -%}
  {%- if link.url == "#" -%}
    <span class="sliderow__title{% if secondary_menu %} sliderow__title--secondary{% endif %}{% if highlight_item == link_title_lowercase %} sliderow__title--highlight{% endif %}" data-sliderule-close="sliderule-{{ key }}">{{ link.title | strip_html | escape }}</span>
  {%- else -%}
    <a class="sliderow__title{% if secondary_menu %} sliderow__title--secondary{% endif %}{% if highlight_item == link_title_lowercase %} sliderow__title--highlight{% endif %}" href="{{ link.url }}">
      <span>
        {{ link.title | strip_html | escape }}

        {%- if expands or link_level > 0 -%}
          {%- render 'superscript', link_collection: link -%}
        {%- endif -%}
      </span>
    </a>
  {%- endif -%}
{%- endcapture -%}

<div class="sliderule__wrapper{% if secondary_menu %} sliderule__wrapper--secondary{% endif %}">
  {%- if expands and block.type != 'menu-columns' -%}
    <mobile-sliderule id="sliderule-{{ key }}">
      <button class="sliderow" type="button"
        data-animates="{{ link_level }}"
        data-animation="{{ animation_name }}"
        data-animation-delay="{{ animation_delay | plus: animation_delay_additional }}"
        data-animation-duration="{{ animation_duration }}"
        data-sliderule-open="sliderule-{{ key }}">
        <span class="sliderow__title{% if secondary_menu %} sliderow__title--secondary{% endif %}{% if highlight_item == link_title_lowercase %} sliderow__title--highlight{% endif %}">
          <span>
            {{ link.title | strip_html | escape }}

            {%- if link_level > 0 -%}
              {%- render 'superscript', link_collection: link -%}
            {%- endif -%}
          </span>

          <span class="sliderule__chevron sliderule__chevron--right">
            {%- render 'icon-arrow-right' -%}
            <span class="visually-hidden">{{ 'general.accessibility.show_menu' | t }}</span>
          </span>
        </span>
      </button>

      <div class="mobile__menu__dropdown sliderule__panel"
        data-sliderule="{{ link_level_next }}"
        data-scroll-lock-scrollable>
        <div class="sliderow sliderow--back"
          data-animates="{{ link_level_next }}"
          data-animation="{{ animation_name }}"
          data-animation-delay="150"
          data-animation-duration="{{ animation_duration }}">
          <button class="sliderow__back-button" type="button" data-sliderule-close="sliderule-{{ key }}">
            <span class="sliderule__chevron sliderule__chevron--left">
              {%- render 'icon-arrow-left' -%}
              <span class="visually-hidden">{{ 'general.accessibility.exit_menu' | t }}</span>
            </span>
          </button>
          {{ link_markup }}
        </div>
        <div class="sliderow__links" data-links>
          {%- for link in link.links -%}
            {%- assign index_recursive = index | append: 'x' | append: forloop.index -%}
            {%- assign animation_delay = 50 | times: forloop.index -%}

            {%- render 'nav-item-mobile', link: link, index: index_recursive, forloop_index: forloop.index, link_level: link_level_next, highlight_item: highlight_item, animation_delay: animation_delay, unique: unique -%}
          {%- endfor -%}

          {% comment %} Render block content {% endcomment %}
          {%- if section and section.blocks.size > 0 -%}
            {%- assign header_blocks = '' -%}
            {%- assign animation_delay_next = 50 -%}

            {%- unless link.links.size > 0 -%}
              {%- assign animation_delay = animation_delay | divided_by: forloop_index | at_least: animation_delay_min | minus: animation_delay_next -%}
            {%- endunless -%}

            {%- assign animation_delay = animation_delay | plus: animation_delay_additional -%}

            {%- for block in section.blocks -%}
              {%- if block.settings.position == index -%}
                {%- assign animation_delay = animation_delay | plus: animation_delay_next -%}

                {%- capture header_blocks -%}
                  {{ header_blocks }}

                  {% render 'header-block', block: block, link_level_next: link_level_next, animation_delay: animation_delay, is_mobile: true %}
                {%- endcapture -%}
              {%- endif -%}
            {%- endfor -%}

            {%- if header_blocks != '' -%}
              <div class="sliderule-grid blocks-{{ block_count }}">
                {{ header_blocks }}
              </div>
            {%- endif -%}
          {%- endif -%}
        </div>
      </div>
    </mobile-sliderule>
  {%- else -%}
    {% comment %} Single menu item, no nesting {% endcomment %}
    <div class="sliderow"
      role="button"
      data-animates="{{ link_level }}"
      data-animation="{{ animation_name }}"
      data-animation-delay="{{ animation_delay | plus: animation_delay_additional }}"
      data-animation-duration="{{ animation_duration }}">
      {{ link_markup }}
    </div>
  {%- endif -%}
</div>