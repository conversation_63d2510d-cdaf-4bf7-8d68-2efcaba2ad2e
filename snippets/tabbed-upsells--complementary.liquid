{%- liquid
  assign slider_enabled = false

  if block.settings.layout == 'slider'
    assign slider_enabled = true
    assign dots_style = settings.dots_style
    assign autoplay_speed = false
    assign autoplay = block.settings.autoplay

    if autoplay
      assign autoplay_speed = block.settings.autoplay_speed | times: 1000
    endif
  endif
-%}
<div
  class="product__block product__complementary block-padding"
  {{ block.shopify_attributes }}
  {{ block_style }}
>
  <complementary-products
    class="complementary-products"
    data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit=10&intent=complementary"
  >
    {%- if slider_enabled -%}
      <slider-component
        data-dots="{{ dots_style }}"
        data-options='{"autoPlay": {{ autoplay_speed }}, "prevNextButtons": false }'
      >
    {%- endif -%}

    {%- if recommendations.performed and recommendations.products_count > 0 -%}
      {%- for product in recommendations.products limit: block.settings.complementary_limit -%}
        {%- render 'upsell-product', upsell_product: product, slider_enabled: slider_enabled -%}
      {%- endfor -%}
    {%- endif -%}

    {%- if slider_enabled -%}
      </slider-component>
    {%- endif -%}
  </complementary-products>
</div>

<script src="{{ 'complementary-products.js' | asset_url }}" defer="defer"></script>