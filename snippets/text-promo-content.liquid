{%- case block.type -%}

{%- when '@app' -%}
  <div
    class="hero__app"
    data-aos="hero"
    data-aos-anchor="{{ animation_anchor }}"
    data-aos-order="{{ animation_order }}"
    {{ block.shopify_attributes }}
  >
    {%- render block -%}
  </div>

{%- when 'button' -%}
  {%- if block.settings.button_text != blank -%}
    {%- liquid
      assign button_style = block.settings.button_style

      if button_style == 'btn--text' and block.settings.show_arrow
        assign button_style = button_style | append: ' btn--text-no-underline'
      endif
    -%}

    <div
      class="hero__button"
      data-aos="hero"
      data-aos-anchor="{{ animation_anchor }}"
      data-aos-order="{{ animation_order }}"
    >
      <a
        href="{{ block.settings.button_url | default: '#' }}"
        class="btn {{ button_style }} {{ block.settings.button_size }} {{ block.settings.button_type }}"
        {{ block.shopify_attributes }}
      >
        <span>{{ block.settings.button_text }}</span>

        {%- if block.settings.show_arrow -%}
          {%- render 'icon-arrow-right' -%}
        {%- endif -%}
      </a>
    </div>
  {%- endif -%}

{%- when 'heading' -%}
  {%- if block.settings.title != blank -%}
    {%- liquid
      assign heading_tag = 'h2'

      unless block.settings.heading_tag == 'automatic' or block.settings.heading_tag == blank
        assign heading_tag = block.settings.heading_tag
      endunless
    -%}

    <{{ heading_tag }}
      class="hero__title {{ block.settings.heading_font_size }}"
      data-aos="hero"
      data-aos-anchor="{{ animation_anchor }}"
      data-aos-order="{{ animation_order }}"
      {{ block.shopify_attributes }}
    >
      {{ block.settings.title }}
    </{{ heading_tag }}>
  {%- endif -%}

{%- when 'text' -%}
  {%- if block.settings.text != blank -%}
    <div
      class="hero__rte {{ block.settings.text_font_size }}"
      data-aos="hero"
      data-aos-anchor="{{ animation_anchor }}"
      data-aos-order="{{ animation_order }}"
      {{ block.shopify_attributes }}
    >
      {{ block.settings.text }}
    </div>
  {%- endif -%}
  
{%- endcase -%}