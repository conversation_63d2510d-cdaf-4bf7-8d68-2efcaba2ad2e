{%- if settings.testing_split_title_dash == true -%}
  {% assign product_title = product.title | split: "-" | first | strip_html | escape %}
{%- else -%}
  {% assign product_title = product.title | strip_html | escape %}
{%- endif -%}

<div class="product__block product__head block-padding"
  {{ block_style }}
  {% if animation_name %}
    data-animation="{{ animation_name }}"
    data-animation-duration="{{ animation_duration }}"
    data-animation-delay="{{ animation_delay }}"
  {% endif %}>
  {%- case block.settings.subheading_option -%}
    {%- when 'vendor' -%}
      {%- if product.vendor -%}
        <nav class="breadcrumbs breadcrumbs--no-padding product__breadcrumbs">
          <a href="{{ product.vendor | url_for_vendor }}">{{ product.vendor }}</a>
        </nav>
      {%- endif -%}
    {%- when 'collection' -%}
      {%- if collection or product.collections.size > 0 -%}
        {%- assign current_collection = collection | default: product.collections[0] -%}

        <nav class="breadcrumbs breadcrumbs--no-padding product__breadcrumbs">
          <a href="{{ current_collection.url }}">{{ current_collection.title }}</a>
        </nav>
      {%- endif -%}
    {%- when 'breadcrumb' -%}
      {% render 'breadcrumbs' breadcrumbs_modifier: 'breadcrumbs--no-padding product__breadcrumbs' %}
  {%- endcase -%}

  <div class="product__title__wrapper" {{ block.shopify_attributes }}>
    <h1 class="product__title {{ block.settings.heading_font_size | default: 'h3' }}">
      {%- unless is_title_linked -%}
        <span data-zoom-caption>{{ product_title | strip_html }}</span>
      {%- else -%}
        <a href="{{ product.url }}">
          <span data-zoom-caption>{{ product_title | strip_html }}</span>
        </a>
      {%- endunless -%}
    </h1>
  </div>
</div>
