{%- assign product_item_cutline = blank -%}
{% capture product_item_cutline %}
  {% if settings.show_cutline and product.metafields.theme.cutline != blank and product.metafields.theme.cutline.type == 'single_line_text_field' %}
    {% assign product_title_downcase = product_title | strip_html | escape | downcase %}
    {% assign cutline_downcase = product.metafields.theme.cutline.value | downcase %}
    {% unless product_title_downcase contains cutline_downcase %}
      {{ product.metafields.theme.cutline.value }}
    {% endunless %}
  {% endif %}
{% endcapture %}

{%- if product_item_cutline -%}
  <span
    class="product-item__cutline"
    data-product-cutline
    {% if style != blank %}
      style="{{ style }}"
    {% endif %}
  >
    {{- product_item_cutline -}}
  </span>
{%- endif -%}