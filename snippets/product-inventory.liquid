{% comment %} Product inventory {% endcomment %}

{%- liquid
  assign show_remaining_class = ''
  assign show_notice = block.settings.show_notice
  assign max_inventory = block.settings.max_inventory
  assign hide_inventory_count = block.settings.hide_inventory_counter
  assign current_inventory = 0
  assign has_unavailable_variants = false
  assign show_buy_buttons = false
  assign in_stock_color = block.settings.in_stock_color
  assign low_stock_color = block.settings.low_stock_color
  assign out_of_stock_color = block.settings.out_of_stock_color
  assign buy_buttons = section.blocks | where: 'type', 'buttons'
  if buy_buttons.size > 0
    assign show_buy_buttons = true
  endif

  comment
    Set a limit of the max inventory quantity to prevent a real quantity exposure
  endcomment
  assign max_inventory_quantity = max_inventory | plus: 1

  if current_variant.inventory_management and current_variant.inventory_policy == 'deny'
    if current_variant.inventory_quantity > 0 and current_variant.inventory_quantity <= max_inventory
      assign show_remaining_class = 'count-is-low'
      assign current_inventory = current_variant.inventory_quantity | at_most: max_inventory_quantity
    elsif current_variant.inventory_quantity > 0 and current_variant.inventory_quantity > max_inventory and show_notice == 'always'
      assign show_remaining_class = 'count-is-in'
    elsif current_variant.inventory_quantity < 1 and show_notice == 'always'
      assign show_remaining_class = 'count-is-out'
    endif
  endif

  assign quantity_tracking = false
  assign show_inventory = false
  assign has_low_inventory = false
  assign has_soldout = false
  assign variants_inventory = product.variants | map: 'inventory_quantity'
  for inventory_quantity in variants_inventory
    if inventory_quantity > 0 and inventory_quantity < max_inventory
      assign has_low_inventory = true
    endif

    if inventory_quantity < 1
      assign has_soldout = true
    endif
  endfor

  if has_low_inventory or has_soldout and show_notice == 'always'
    assign show_inventory = true
  endif

  assign variant_count = 1
  for option in product.options_with_values
    assign variant_count = variant_count | times: option.values.size
  endfor

  if variant_count > product.variants.size
    assign has_unavailable_variants = true
  endif

  if show_buy_buttons == false and has_unavailable_variants
    assign show_inventory = true
  endif

  assign inventory_management = product.variants | where: 'inventory_management', 'shopify'
  if inventory_management.size > 0
    assign quantity_tracking = true
  endif

  capture in_stock_icon_color
    if in_stock_color != blank and in_stock_color != 'rgba(0,0,0,0)'
      echo ' style="--icon-color: ' | append: in_stock_color | append: '"'
    endif
  endcapture

  capture low_stock_icon_color
    if low_stock_color != blank and low_stock_color != 'rgba(0,0,0,0)'
      echo ' style="--icon-color: ' | append: low_stock_color | append: '"'
    endif
  endcapture

  capture out_of_stock_icon_color
    if out_of_stock_color != blank and out_of_stock_color != 'rgba(0,0,0,0)'
      echo ' style="--icon-color: ' | append: out_of_stock_color | append: '"'
    endif
  endcapture
-%}

{%- if quantity_tracking and show_inventory or quantity_tracking and show_notice == 'always' -%}
  {%- capture inventory -%}
    <span data-remaining-count>{{ current_inventory | default: 0 }}</span>
  {%- endcapture -%}

  <div class="product__block variant__countdown {{ show_remaining_class }} block-padding"
    data-remaining-max="{{ max_inventory }}"
    data-remaining-show-notice="{{ show_notice }}"
    data-remaining-wrapper
    {{ block.shopify_attributes }}
    {{ block_style }}>
    <span class="variant__countdown--in"{{ in_stock_icon_color }}>
      {% render 'icon-in-stock' %}
      <span class="variant__countdown-text">{{ 'products.product.in_stock' | t }}</span>
    </span>
    {%- if hide_inventory_count -%}
      <span class="variant__countdown--low"{{ low_stock_icon_color }}>
        {% render 'icon-low-inventory' %}
        <span class="variant__countdown-text">{{ 'products.product.remaining_no_count' | t }}</span>
      </span>
    {%- else -%}
      <span class="variant__countdown--low"{{ low_stock_icon_color }}>
        {% render 'icon-low-inventory' %}
        <span class="variant__countdown-text">{{ 'products.product.remaining_html' | t: inventory: inventory }}</span>
      </span>
    {%- endif -%}

    <span class="variant__countdown--out"{{ out_of_stock_icon_color }}>
      {% render 'icon-out-of-stock' %}
      <span class="variant__countdown-text">{{ 'products.product.out_of_stock' | t }}</span>
    </span>
    <span class="variant__countdown--unavailable"{{ out_of_stock_icon_color }}>
      {% render 'icon-out-of-stock' %}
      <span class="variant__countdown-text">{{ 'products.product.item_unavailable' | t }}</span>
    </span>

    <script data-product-remaining-json type="application/json">
      {
        {%- liquid
          for variant in product.variants
            assign variant_id = variant.id | json

            if variant.inventory_management and variant.inventory_policy == 'deny'
              assign quantity = variant.inventory_quantity | at_most: max_inventory_quantity

              if hide_inventory_count
                if quantity < 1
                  assign remaining_value = 'out'
                elsif quantity > 0 and quantity < max_inventory
                  assign remaining_value = 'low'
                else
                  assign remaining_value = 'in'
                endif
              else
                assign remaining_value = quantity
              endif
            else
              assign remaining_value = 'unavailable'
            endif

            echo '"' | append: variant_id | append: '": "' | append: remaining_value | append: '"'

            unless forloop.last
              echo ','
            endunless
          endfor
        -%}
      }
    </script>
  </div>
{%- elsif request.design_mode -%}
  <div {{ block.shopify_attributes }}></div>
{%- endif -%}