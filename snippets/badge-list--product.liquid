{%- liquid 

  assign product_tags = product.tags | join: ',' | append: ','
  assign preorder = false
  assign is_preorder_meta = false
  assign on_sale = false
  assign sold_out = false
  assign badge = ''
  assign badge_new = ''
  assign badge_soldout = ''
  assign badge_alignment = settings.badge_alignment

  if product.metafields.theme.badge != blank and product.metafields.theme.badge.type == 'single_line_text_field'
    assign badge = product.metafields.theme.badge.value
  endif

  if badge == '' and product_tags contains '_badge_'
    assign badge = product_tags | split: '_badge_'
    assign badge = badge[1] | split: ',' | first | replace: '_', ' '
  endif

  if product.metafields.theme.preorder.type == 'boolean' and product.metafields.theme.preorder.value == true
    assign is_preorder_meta = true
  endif

  if product_tags contains '_preorder' or is_preorder_meta
    assign preorder = true
  endif

  if product.compare_at_price > product.price and settings.sale_tags_enable
    assign on_sale = true
  endif

  unless product.available
    assign sold_out = true
  endunless

  assign pre_order_text = 'products.product.pre_order' | t 

  if settings.show_automatic_new_badge
    assign product_created_timestamp = product.created_at | date: '%s' | plus: 0
    assign days_in_seconds = settings.badge_new_date_limit | times: 24 | times: 60 | times: 60
    assign current_date_timestamp = "now" | date: '%s'
    assign check_date_timestamp = current_date_timestamp | minus: days_in_seconds

    if product_created_timestamp > check_date_timestamp
      assign badge_new = 'products.product.new' | t
    endif
  endif

  if settings.show_sold_out_badge and sold_out
    assign badge_soldout = 'products.product.sold_out' | t
  endif

-%}

{%- capture badges -%}
    
  {%- if preorder and sold_out == false -%}
    {%- render 'badge', text: pre_order_text, classes: "badge--preorder" -%}
  {%- endif -%}
    
  {%- if badge_soldout != '' -%}
    {%- render 'badge', text: badge_soldout, classes: "badge--soldout" -%}
  {%- endif -%}

  {%- if badge_new != '' -%}
    {%- render 'badge', text: badge_new, classes: "badge--new" -%}
  {%- endif -%}

  {%- if on_sale and sold_out == false -%}
    
    {%- capture badge_text -%}
      
      {{ 'products.product.save' | t }}

      {%- assign display_savings = settings.product_item_savings_display -%}
      
      {%- if display_savings == "percent" -%}
        {{ product.price | divided_by: product.compare_at_price | times: 100 | round | append: "%"  }}
      {%- elsif display_savings == "amount" -%}
        {%- assign display_savings_value = product.compare_at_price | minus: product.price -%}
        {% if settings.currency_code_enable %}
          {{ display_savings_value | money_with_currency }}
        {% else %}
          {{ display_savings_value | money }}
        {% endif %}
      {%- endif -%}
        
    {%- endcapture -%}

    {%- render 'badge', text: badge_text, classes: "badge--sale color-scheme-3" -%}
    
  {%- endif -%}

  {%- if badge != '' -%}
    {%- render 'badge', text: badge, classes: "badge--white badge--border" -%}
  {%- endif -%}

  {%- if product.metafields.custom.custom_badges != '' -%}
    {%- for badge in product.metafields.custom.custom_badges.value -%}
      {%- capture badge_classes -%}
        badge--white badge--border
        badge--{{ badge | handle }}
      {%- endcapture -%}
      {%- render 'badge', text: badge, classes: badge_classes -%}  
    {%- endfor -%}
  {%- endif -%}

{%- endcapture -%}

{%- if badges != blank -%}
  <div class="badge-list {{ classes }}">
    {{ badges }}
  </div>
{%- endif -%}