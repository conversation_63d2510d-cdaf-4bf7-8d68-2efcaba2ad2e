{%- if product -%}

  {%- if settings.testing_split_title_dash == true -%}
    {% assign product_title = product.title | split: "-" | first | strip_html | escape %}
  {%- else -%}
    {% assign product_title = product.title | strip_html | escape %}
  {%- endif -%}

  {%- liquid

    assign animation_name = 'drawer-items-fade'
    assign animation_duration = 500
    assign animation_delay_additional = 200
    assign link_level = link_level | default: 0

    capture product_item_cutline
      if settings.show_cutline and product.metafields.theme.cutline != blank and product.metafields.theme.cutline.type == 'single_line_text_field'
        assign product_title_downcase = product_title | strip_html | escape | downcase
        assign cutline_downcase = product.metafields.theme.cutline.value | downcase
  
        unless product_title_downcase contains cutline_downcase
          echo product.metafields.theme.cutline.value
        endunless
      endif
    endcapture

    if block
      assign show_swatches = block.settings.collection_show_swatches
      assign show_prices = block.settings.collection_show_prices
    else
      assign show_swatches = section.settings.collection_show_swatches
      assign show_prices = section.settings.collection_show_prices
    endif

    -%}

  <a 
    class="megamenu-product {{ classes }}" 
    href="{{ product.url }}"
    {% comment %}
    data-animates="{{ link_level }}"
    data-animation="{{ animation_name }}"
    data-animation-delay="{{ animation_delay | plus: animation_delay_additional }}"
    data-animation-duration="{{ animation_duration }}"
    {% endcomment %}
  >

    <div class="megamenu-product__inner">

      <div class="megamenu-product__image">
        {{ product.featured_image | image_url: width: 400 | image_tag: 
          loading: loading,
          fetchpriority: fetchpriority,
          alt: alt,
          sizes: sizes,
          srcset: srcset_mobile,
          class: 'is-loading'
         }}
      </div>
  
      <div class="megamenu-product__info">
  
        <span class="megamenu-product__title text-product-title">
          {{ product_title }}
        </span>

        {%- if product_item_cutline != blank -%}
          <span class="megamenu-product__cutline text-product-description">
            {{ product_item_cutline }}
          </span>
        {%- endif -%}

        {%- if show_prices -%}
          <span class="megamenu-product__price">
            {%- render 'product-grid-price', product: product, current_variant: current_variant -%}
          </span>
        {%- endif -%}

        {%- if show_swatches -%}
          {%- capture swatches -%}
            {% render 'product-image-swatches', product: product %}
          {%- endcapture -%}
          {%- if swatches -%}
            <span class="megamenu-product__swatches">
              {{ swatches }}
            </span>
          {%- endif -%}
        {%- endif -%}
  
      </div>

    </div>

  </a>

{%- endif -%}