{% comment %}
    Renders "Before and after" images comparison

    Accepts:
    - image_1: {Image} Image left
    - image_2: {Image} Image right
    - aspect_ratio: {Number} Image aspect ratio (Optional)
    - animation_anchor: {String} ID of the element that will trigger animations
    - modifier: {String} Section class name
    - wrapper: {String} Section wrapper setting

    Usage:
    {% render 'compare-images', image_1: image_1, image_2: image_2, aspect_ratio: aspect_ratio, animation_anchor: animation_anchor, modifier: modifier, wrapper: wrapper %}
{% endcomment %}

{%- liquid
  assign modifier = modifier | default: ''

  unless aspect_ratio
    if image_1
      assign aspect_ratio = image_1.aspect_ratio
    elsif image_2
      assign aspect_ratio = image_2.aspect_ratio
    else
      assign aspect_ratio = 1
    endif
  endunless

  capture sizes
    if wrapper == 'wrapper--full'
      if compare_images_width == 'half'
        echo '(min-width: 750px) calc((100vw / 2), 100vw'
      else
        echo '100vw'
      endif
    elsif wrapper == 'wrapper--full-padded'
      if compare_images_width == 'half'
        echo '(min-width: 750px) calc((100vw - 100px) / 2), calc(100vw - 32px)'
      else
        echo '(min-width: 750px) calc(100vw - 100px), calc(100vw - 32px)'
      endif
    elsif wrapper == 'wrapper--narrow'
      if compare_images_width == 'half'
        echo '(min-width: 750px) calc((80vw - 100px) / 2), calc(100vw - 32px)'
      else
        echo '(min-width: 750px) calc(80vw - 100px), calc(100vw - 32px)'
      endif
    endif
  endcapture
-%}

<script src="{{ 'compare-images.js' | asset_url }}" defer="defer"></script>

<compare-images
  class="compare__outer"
  style="--aspect-ratio: {{ aspect_ratio }};"
  data-aos="img-in"
  data-aos-anchor="{{ animation_anchor }}"
  data-aos-duration="800"
  data-aos-easing="ease-out-quart">
  <div class="compare" data-images-container>
    <div class="compare__image">
      <div class="compare__image__holder">
        {%- liquid
            if image_1
              render 'image', image: image_1, sizes: sizes, cover: true
            else
              render 'image', placeholder: 'lifestyle-1', sizes: sizes, cover: true
            endif
          -%}
      </div>
    </div>

    <div class="compare__image compare__image--overlap" data-image-holder>
      <div class="compare__image__holder" data-image-element>
        {%- liquid
          if image_2
            render 'image', image: image_2, sizes: sizes, cover: true
          else
            render 'image', placeholder: 'lifestyle-2', sizes: sizes, cover: true
          endif
        -%}
      </div>
    </div>

    <div class="compare__btn-holder">
      <div class="compare__btn" data-range-button>
        {%- render 'icon-arrow-separate' -%}
      </div>

    </div>
    <input class="compare__input" type="range" min="0" max="100" value="50" step="1" data-range-input>
  </div>

</compare-images>
