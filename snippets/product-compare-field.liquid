{% comment %}

  single_line_text_field
  multi_line_text_field
  number_integer
  number_decimal
  boolean
  product_reference
  file_reference
  url
  date
  json
  color
  rating
  volume

{% endcomment %}

{%- if special == blank -%}
  <!-- RATING DOTS -->
  {%- assign rating_dots_fields = 'compare.formats.rating_dots' | t  -%}
  {%- assign rating_dots_fields_array = rating_dots_fields | split: ',' -%}
  <!-- rating_dots_fields - {{ rating_dots_fields }} -->
  <!-- rating_dots_fields_array - {{ rating_dots_fields_array }} -->
  {%- for field in rating_dots_fields_array -%}
    <!-- field - {{ field }} -->
    <!-- label - {{ label }} -->
    {%- if field == label -%}
      {%- assign special = 'rating-dots' -%}
      {%- break -%}
    {%- endif -%}
  {%- endfor -%}
{%- endif -%}

{%- if special == blank -%}
  <!-- BADGES -->
  {%- assign badges_fields = 'compare.formats.badges' | t  -%}
  {%- assign badges_fields_array = badges_fields | split: ',' -%}
  <!-- badges_fields - {{ badges_fields }} -->
  <!-- badges_fields_array - {{ badges_fields_array }} -->
  {%- for field in badges_fields_array -%}
    <!-- field - {{ field }} -->
    <!-- label - {{ label }} -->
    {%- if field == label -%}
      {%- assign special = 'badges' -%}
      {%- break -%}
    {%- endif -%}
  {%- endfor -%}
{%- endif -%}

<!--
  badges_fields - {{ badges_fields }}
  rating_dots_fields - {{ rating_dots_fields }}
  special - {{ special }}
-->

{%- capture field_value -%}

  {%- if value -%}
    {{ value }}
  {%- elsif metafield -%}
    {%- if metafield.type contains 'list' -%}

      {% render 'render-metafield-list', 
        metafield: metafield, 
        metafield_type: metafield.type, 
        metafield_value: metafield.value, 
        special: special %}
      
    {%- else -%}
      
      {% 
        render 'render-metafield', 
        metafield: metafield, 
        metafield_type: metafield.type, 
        metafield_value: metafield.value, 
        special: special %}

    {%- endif -%}
  {%- else -%}
    {{- 'compare.fields.empty' | t -}}
  {%- endif -%}

{%- endcapture -%}

<div class="product-compare-field compare-grid__item-field compare-grid__item-field--value">
  <span class="product-compare-field__label visually-hidden--medium-up text-badge">{{- label -}}</span>
  <span class="product-compare-field__value">{{ field_value }}</span>
</div>