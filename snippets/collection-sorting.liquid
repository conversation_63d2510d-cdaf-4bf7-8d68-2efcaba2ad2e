<!-- /snippets/collection-sorting.liquid -->

{% comment %}
    Renders a sorting dropdown for a collection

    Accepts:
    - collection: {Object} current collection

    Usage:
    {%- render 'collection-sorting' collection: collection -%}
{% endcomment %}

{%- assign sort_options = search.sort_options | default: collection.sort_options -%}
{%- assign sort_by = search.sort_by | default: collection.sort_by -%}
{%- assign sort_translation = 'collections.general.sort' | t -%}
{%- assign currently_selected = sort_options | where: "value", sort_by | first -%}
{%- assign select_options = '' -%}

<div class="popout--sort">
  <h2 class="visually-hidden" id="sort-heading">
    {{ sort_translation }}
  </h2>
  <popout-select class="popout{% if currently_selected.name %} is-active{% endif %}" data-sort-enabled="{{ sort_by }}" submit>
    <button type="button"
      class="popout__toggle"
      aria-expanded="false"
      aria-controls="sort-list"
      aria-describedby="sort-heading"
      data-popout-toggle>
      <span class="popout__toggle__text">{{ sort_translation }}</span>

      <span class="popout__toggle__text" data-sort-button-text>{{ currently_selected.name }}</span>

      {%- render 'icon-nav-arrow-down' -%}
    </button>
    <ul id="sort-list" class="popout-list" data-popout-list data-scroll-lock-scrollable>
      {% for option in sort_options %}
        {% assign active = false %}
        {%- if sort_by == option.value -%}
          {% assign active = true %}
        {%- endif -%}
        <li class="popout-list__item{% if active %} is-active{% endif %}">
          <a class="popout-list__option"
            data-value="{{ option.value }}"
            href="#"
            {% if active %}aria-current="true"{% endif %}
            data-popout-option
            data-sort-link>
            <span>{{ option.name }}</span>
          </a>
        </li>

        {%- capture select_options -%}
          {{ select_options }}

          <option
            {% if active %}selected="selected"{% endif %}
            value="{{ option.value }}">
              {{ option.name }}
          </option>
        {%- endcapture -%}
      {%- endfor -%}
    </ul>

    <input
      disabled
      type="hidden"
      name="sort_by"
      id="id-{{ section.id }}-collection-sorting"
      form="{{ section.id }}-form-filter"
      value="{{ currently_selected.value }}"
      class="js"
      data-popout-input>

    <noscript>
      <div class="popout__select__outer">
        <button type="submit"
          class="no-js btn btn--primary btn--outline"
          form="{{ section.id }}-form-filter">{{ sort_translation }}</button>

        <select
          name="sort_by"
          form="{{ section.id }}-form-filter"
          class="no-js popout__toggle">
          {{ select_options }}
        </select>
      </div>
    </noscript>
  </popout-select>
</div>
