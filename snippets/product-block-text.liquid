{% comment %} Text block for product {% endcomment %}
{%- if block.settings.title != blank or block.settings.text != blank -%}
  <div
    class="product__block product__text{% if block.settings.layout == "inline" %} product__text--inline{% endif %} block-padding {{ block.settings.text_alignment }}"
    {{ block.shopify_attributes }}
    {{ block_style }}
  >
    {%- if block.settings.title != blank -%}
      <p class="product__heading strong {{ block.settings.heading_font_size }}">
        {{- block.settings.title -}}
      </p>
    {%- endif -%}

    {%- if block.settings.text != blank -%}
      <div class="product__subheading {{ block.settings.text_font_size }}">
        {{- block.settings.text -}}
      </div>
    {%- endif -%}
  </div>
{%- endif -%}
