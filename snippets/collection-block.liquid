<!-- /snippets/collection-block.liquid -->

{% comment %}
    Renders a list of products from a collection

    Accepts:
    - collection: {Object} Image to render (required)
    - section: {Object} Image to render (required)
    - block: {Object} Image section settings (required)
    - limit: {Number} contains a value of products to render in grid (required)

    Usage:
    {%- render 'collection-block' collection: collection, section: section, block: block, limit: limit -%}
{% endcomment %}

{%- liquid
  assign collection_title = block.settings.collection_name
  assign collection_image = block.settings.image | default: collection.image

  assign mobile_slider = "grid--mobile-vertical"
  if section.settings.layout_mobile == 'slider'
    assign mobile_slider = "grid--mobile-slider"
  endif

  assign products_block_html = ''
  assign products_list_html = ''
  assign products_list_index = 0

  if collection_image == blank
    assign limit = limit | plus: 2
  endif

  if collection != blank and collection.products_count > 0
    for product in collection.products
      assign modifier_class = ''

      if product.tags contains 'hide'
        assign limit = limit | plus: 1
      endif

      if forloop.index == limit or forloop.last
        assign products_list_index = forloop.index
      endif

      if forloop.index0 <= 1 and collection_image == blank
        capture products_block_html
          echo products_block_html
          render 'product-grid-item', product: product, animation_delay: forloop.index0, collection: collection, index: forloop.index

          assign modifier_class = 'mobile'
        endcapture
      endif

      capture products_list_html
        echo products_list_html
        render 'product-grid-item', product: product, animation_delay: forloop.index0, collection: collection, index: forloop.index, modifier_class: modifier_class
      endcapture

      if forloop.index == limit
        break
      endif
    endfor
  endif

  if section.settings.display_type == 'selected' and block.settings.collection == blank
    for i in (1..limit)
      assign modifier_class = ''

      if forloop.last
        assign products_list_index = forloop.index
      endif

      if forloop.index0 <= 1 and collection_image == blank
        capture products_block_html
          echo products_block_html
          render 'onboarding-product-grid-item', index: forloop.index, animation_delay: forloop.index0

          assign modifier_class = 'mobile'
        endcapture
      endif

      capture products_list_html
        echo products_list_html
        render 'onboarding-product-grid-item', index: forloop.index, animation_delay: forloop.index0, modifier_class: modifier_class
      endcapture
    endfor
  endif
-%}

<div class="collection-block">
  <div class="collection-block__content">
    {%- if collection_title != blank or collection.title != blank -%}
      <h2 class="collection-block__title h3">
        {%- if collection_title != blank -%}
          {{ collection_title }}
        {%- else -%}
          {{ collection.title }}
        {%- endif -%}
      </h2>
    {%- endif -%}
    {% if collection.description != '' %}
      <p>{{ collection.description }}</p>
    {% endif %}
    <a class="collection-block__button btn btn--black btn--outline caps" href="{{ collection.url }}">
      <span>{{ 'collections.general.view_collection' | t }}</span>
    </a>
  </div>

  {%- if products_block_html != '' -%}
    <div class="collection-block__products desktop">
      {{ products_block_html }}
    </div>
  {%- elsif collection_image != blank -%}
    {%- capture sizes -%}
      (min-width: 750px) 50vw, 100vw
    {%- endcapture -%}

    <a href="{{ collection.url }}" class="collection-block__image" aria-label="{{ image_aria_label }}">
      <div class="collection-block__image-bg">
        {%- render 'image' image: collection_image, sizes: sizes, cover: true -%}
      </div>
    </a>
  {%- endif -%}
</div>

{%- if products_list_html != '' -%}
  <section class="index-products{% if products_list_index <= 2 and collection_image == blank %} mobile{% endif %}" data-section-id="{{ section.id }}" data-section-type="product-grid">
    <div class="grid-outer">
      <div class="grid {{ mobile_slider }}">
        {{ products_list_html }}
      </div>
    </div>
  </section>
{%- elsif products_list_index == 0 -%}
  <div class="collection-block__empty">
    <div class="no-results">
      <p><strong>{{ 'collections.general.no_matches' | t }}</strong></p>
    </div>
  </div>
{%- endif -%}
