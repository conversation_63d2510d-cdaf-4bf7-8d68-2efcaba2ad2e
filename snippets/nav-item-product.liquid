{%- if product -%}

  {%- liquid

    assign animation_name = 'drawer-items-fade'
    assign animation_duration = 500
    assign animation_delay_additional = 200
    assign link_level = link_level | default: 0

    capture product_item_cutline
      if settings.show_cutline and product.metafields.theme.cutline != blank and product.metafields.theme.cutline.type == 'single_line_text_field'
        assign product_title_downcase = product_title | strip_html | escape | downcase
        assign cutline_downcase = product.metafields.theme.cutline.value | downcase
  
        unless product_title_downcase contains cutline_downcase
          echo product.metafields.theme.cutline.value
        endunless
      endif
    endcapture
    -%}

  <a 
    class="nav-item-product" 
    href="{{ product.url }}"
    {% comment %}
    data-animates="{{ link_level }}"
    data-animation="{{ animation_name }}"
    data-animation-delay="{{ animation_delay | plus: animation_delay_additional }}"
    data-animation-duration="{{ animation_duration }}"
    {% endcomment %}
  >

    <div class="nav-item-product__inner">

      <div class="nav-item-product__image">
        {{ product.featured_image | image_url: width: 200 | image_tag: 
          loading: loading,
          fetchpriority: fetchpriority,
          alt: alt,
          sizes: sizes,
          srcset: srcset_mobile,
          class: 'is-loading'
         }}
      </div>
  
      <div class="nav-item-product__info">
  
        <span class="nav-item-product__title text-product-title">
          {{ product.title }}
        </span>
  
        {%- if product_item_cutline != blank -%}
          <span class="text-product-description">
            {{ product_item_cutline }}
          </span>
        {%- endif -%}
  
        <span class="nav-item-product__price">
          {%- render 'product-grid-price', product: product, current_variant: current_variant -%}
        </span>
  
      </div>

    </div>

  </a>

{%- endif -%}