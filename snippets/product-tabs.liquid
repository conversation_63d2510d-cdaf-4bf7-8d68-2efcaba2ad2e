<!-- /snippets/product-tabs.liquid -->
{% comment %}
  Renders product tabs

  Accepts:
  - section: {Object} Current section (required)
  - product: {Object} Current product (optional)

  Usage:
  {% render 'product-tabs', section: section, product: product  %}
{% endcomment %}

{%- liquid
  assign show_description = block.settings.show_description
  assign show_read_more = block.settings.show_read_more
  assign product_description_title = 'products.general.description' | t
  assign product_description_content = product.description | strip
-%}

{%- if block.type == 'tab_richtext' -%}
  {%- liquid
    assign tab_links_html = ''
    assign tab_contents_html = ''
    assign count = 0
  -%}

  {%- if show_description and product_description_content != blank -%}
    {%- capture tab_links_html -%}
      <li class="tab-link tab-link-0 current" data-tab="0" tabindex="0" data-attributes-placeholder>
        <span>{{ product_description_title }}</span>
      </li>
    {%- endcapture -%}

    {%- capture tab_contents_html -%}
      <div class="tab-content tab-content-0 current rte">
        {%- render 'toggle-ellipsis', content: product_description_content, show_read_more: show_read_more -%}
      </div>
    {%- endcapture -%}

    {%- assign count = 1 -%}
  {%- endif -%}

  {%- for i in (1..5) -%}
    {%- liquid
      assign title = 'title_' | append: forloop.index
      assign title = block.settings[title]
      assign content = 'raw_content_' | append: forloop.index
      assign content = block.settings[content]
    -%}

    {%- if title != '' and content != '' -%}
      {%- capture tab_links_html -%}
        {{ tab_links_html }}

        <li class="tab-link tab-link-{{ count }}{% if count == 0 %} current{% endif %}" data-tab="{{ count }}" tabindex="0">
          <span>{{ title }}</span>
        </li>
      {%- endcapture -%}

      {%- capture tab_contents_html -%}
        {{ tab_contents_html }}

        <div class="tab-content tab-content-{{ count }}{% if count == 0 %} current{% endif %} rte">
          {%- render 'toggle-ellipsis', content: content, show_read_more: show_read_more -%}
        </div>
      {%- endcapture -%}

      {%- assign count = count | plus: 1 -%}
    {%- endif -%}
  {%- endfor -%}

  {%- if tab_contents_html -%}
    <tabs-component class="product-tabs" {{ block.shopify_attributes }}>
      <native-scrollbar class="tabs__head product-tabs__head">
        <ul class="tabs product-tabs-title" data-scrollbar data-scrollbar-slider>
          {{ tab_links_html }}
        </ul>

        <button
          type="button"
          class="tabs__arrow tabs__arrow--prev product-tabs__arrow product-tabs__arrow--prev is-hidden"
          data-scrollbar-arrow-prev
        >
          {%- render 'icon-nav-arrow-left' -%}
          <span class="visually-hidden">{{ 'products.general.see_all' | t }}</span>
        </button>

        <button
          type="button"
          class="tabs__arrow tabs__arrow--next product-tabs__arrow product-tabs__arrow--next is-hidden"
          data-scrollbar-arrow-next
        >
          {%- render 'icon-nav-arrow-right' -%}
          <span class="visually-hidden">{{ 'products.general.see_all' | t }}</span>
        </button>
      </native-scrollbar>

      {{ tab_contents_html }}
    </tabs-component>
  {%- elsif request.design_mode -%}
    <span {{ block.shopify_attributes }}></span>
  {%- endif -%}
{%- elsif block.type == 'accordion' -%}
  {%- liquid
    assign accordion_heading = block.settings.heading
    assign accordion_text = block.settings.text
    assign accordion_open = block.settings.default_open
    assign accordion_content = accordion_text

    if show_description
      assign accordion_content = product_description_content | append: accordion_text
    endif
  -%}

  {%- if accordion_heading != blank and accordion_content != blank -%}
    <collapsible-elements single="true" {{ block.shopify_attributes }}>
      <variant-accordion class="product-accordion variant-accordion">
        <details
          class="accordion"
          data-collapsible
          {% if accordion_open %}
            open="true"
          {% endif %}
          {{ block.shopify_attributes }}
        >
          <summary class="accordion__title text-badge-lg" data-collapsible-trigger>
            {%- if block.settings.show_icon -%}
              {%- liquid
                assign icon_size = block.settings.icon_size
                assign icon_color = block.settings.icon_color
                assign icon_name = block.settings.icon_name
                assign image = block.settings.image

                capture icon_style
                  echo 'style="'
                  echo '--icon-size: ' | append: icon_size | append: 'px;'
                  if icon_color != blank and icon_color != 'rgba(0,0,0,0)'
                    echo '--icons: ' | append: icon_color | append: ';'
                  endif
                  echo '"'
                endcapture
              -%}

              <div
                class="icon__animated{% if image != blank %} icon__animated--image{% endif %}"
                {{ icon_style }}
              >
                {%- liquid
                  if image
                    assign icon_width = icon_size
                    assign icon_width_retina = icon_width | times: 2
                    assign icon_sizes = icon_width | append: 'px'
                    assign icon_widths = icon_width | append: ', ' | append: icon_width_retina
                    render 'image', image: image, width: icon_width_retina, sizes: icon_sizes, widths: icon_widths, show_backfill: false
                  else
                    render 'animated-icon', filename: block.settings.icon_name
                  endif
                -%}
              </div>
            {%- endif -%}

            {{ accordion_heading }}

            {%- render 'icon-plus' -%}
            {%- render 'icon-minus' -%}
          </summary>

          <div
            class="accordion__body rte"
            data-collapsible-body
            {% if accordion_open %}
              style="height: auto;"
            {% endif %}
          >
            <div class="accordion__content" data-collapsible-content>
              {%- render 'toggle-ellipsis', content: accordion_content, show_read_more: show_read_more -%}
            </div>
          </div>
        </details>
      </variant-accordion>
    </collapsible-elements>
  {%- endif -%}
{%- endif -%}
