{% comment %}
  Renders blog posts in Blog page and Featured blog posts section

  Accepts:
  - blog_articles: {Array} blog articles
  - is_blog_page: {Boolean} product (required)

  Usage:
  {% render 'blog-posts', blog_articles: blog.articles, is_blog_page: true %}
{% endcomment %}

{%- liquid
  assign articles_per_row = section.settings.grid
  assign article_limit = articles_per_row | times: section.settings.rows
  assign show_author = section.settings.show_author
  assign show_date = section.settings.show_date
  assign show_button = section.settings.show_button
  assign show_tags = section.settings.show_tags
  assign show_comments = section.settings.show_comments
  assign aspect_ratio = 1 | divided_by: section.settings.aspect_ratio
  assign onboarding = true
  assign animation_anchor = '#Blog--' | append: section.id
  assign animation_duration = 800
  assign animation_order = 0
  assign title_tag = 'h2'

  unless section.settings.title_tag == 'automatic'
    assign title_tag = section.settings.title_tag
  endunless

  if is_blog_page
    assign onboarding = false
  endif

  if section.settings.blog_name != blank and blogs[section.settings.blog_name].articles.size > 0
    assign onboarding = false
    assign blog_articles = blogs[section.settings.blog_name].articles
  endif

  assign eager_images_limit = articles_per_row
  if articles_per_row > 3
    assign eager_images_limit = articles_per_row | times: 2
  endif
-%}

<div class="grid-outer">
  <div class="grid blog-listing">
    {%- if onboarding -%}
      {%- for i in (1..article_limit) -%}
        {%- assign animation_order = animation_order | plus: 1 -%}

        <article class="article article--onboarding grid-item">
          <div class="article__image__outer">
            <div class="article__image svg-placeholder">
              <a
                class="article__image-link"
                href="#"
                aria-label="Article title"
                data-aos="img-in"
                data-aos-anchor="{{ animation_anchor }}"
                data-aos-order="{{ animation_order }}"
                data-aos-duration="{{ animation_duration }}"
                data-aos-easing="ease-out-quart"
              >
                <div class="image-wrapper" style="--aspect-ratio: {{ aspect_ratio }};">
                  {{ 'image' | placeholder_svg_tag }}
                </div>
              </a>

              {%- if show_tags -%}
                <div class="article__tags">
                  <span class="article__tag">tag</span><span class="article__tag">tag</span>
                </div>
              {%- endif -%}
            </div>
          </div>

          <div
            class="article__text-wrapper"
            data-aos="hero"
            data-aos-anchor="{{ animation_anchor }}"
            data-aos-order="{{ animation_order }}"
            data-aos-duration="{{ animation_duration }}"
          >
            <{{ title_tag }} class="article__title {{ section.settings.title_font_size }}"
              ><a href="#" title="Article title">Blog post title</a></{{ title_tag }}
            >

            {%- if show_date or show_author or show_comments -%}
              <p class="article__meta">
                {%- if show_date -%}
                  <time datetime="{{ 'now' | date: format: 'full_date' }}">
                    {{- 'now' | date: format: 'full_date' -}}
                  </time>
                {%- endif -%}

                {%- if show_author -%}
                  <span class="article__meta__author">by Author Name</span>
                {%- endif -%}

                {%- if show_comments and article.comments_count > 0 -%}
                  <span class="comment-count">
                    {{- 'blogs.comments.comments_with_count' | t: count: article.comments_count -}}
                  </span>
                {%- endif -%}
              </p>
            {%- endif -%}

            <p class="article__excerpt">
              Once you create a blog post, the content will appear here. It will display the first few lines of text
              here or a summary of the post that could be added from the Excerpt section in Blog posts editor.
            </p>

            {%- if show_button -%}
              <div class="article__link">
                <a href="#" class="btn btn--text" aria-label="{{ 'blogs.article.read_more' | t }}">
                  <span>{{ 'blogs.article.read_more' | t }}</span>
                </a>
              </div>
            {%- endif -%}
          </div>
        </article>
      {%- endfor -%}
    {%- else -%}
      {%- for article in blog_articles limit: article_limit -%}
        {%- liquid
          assign animation_order = animation_order | plus: 1

          if forloop.index > eager_images_limit
            assign loading = 'lazy'
          endif
        -%}

        {%- capture tags -%}
          {%- if article.tags.size > 0 and show_tags -%}
            <div class="article__tags">
              {%- for tag in article.tags limit: 3 -%}
                <a href="{{ blog.url }}/tagged/{{ tag | handle }}" title="{{ blog.title }} tagged {{ tag | escape }}" class="article__tag">{{ tag }}</a>
              {%- endfor -%}
            </div>
          {%- endif -%}
        {%- endcapture -%}

        <article class="article grid-item">
          {%- if article.image -%}
            <div class="article__image__outer">
              <div class="article__image">
                <a
                  class="article__image-link"
                  href="{{ article.url }}"
                  aria-label="{{ article.title | strip_html | escape }}"
                >
                  {%- capture sizes -%}
                    (min-width: 990px) calc((100vw - 100px) / {{ section.settings.grid }}), (min-width: 750px) calc((100vw - 60px) / 3), calc(100vw - 32px)
                  {%- endcapture -%}

                  {%- capture attributes -%}
                    data-aos="img-in"
                    data-aos-anchor="{{ animation_anchor }}"
                    data-aos-order="{{ animation_order }}"
                    data-aos-duration="{{ animation_duration }}"
                    data-aos-easing="ease-out-quart"
                  {%- endcapture -%}
                  {%- render 'image',
                    image: article.image,
                    widths: '250, 280, 360, 480, 560, 640, 720, 840, 960, 1280, 1440, 1600, 1840, 2100, 2400',
                    sizes: sizes,
                    aspect_ratio: aspect_ratio,
                    loading: loading,
                    attributes: attributes,
                    show_backfill: true
                  -%}
                </a>

                {{ tags }}
              </div>
            </div>
          {%- else -%}
            {{ tags }}
          {%- endif -%}

          <div
            class="article__text-wrapper"
            data-aos="hero"
            data-aos-anchor="{{ animation_anchor }}"
            data-aos-order="{{ animation_order }}"
            data-aos-duration="{{ animation_duration }}"
          >
            <{{ title_tag }} class="article__title {{ section.settings.title_font_size }}">
              <a href="{{ article.url }}" title="{{ article.title | strip_html | escape }}">
                {{ article.title }}
              </a>
            </{{ title_tag }}>

            {%- if show_date or show_author or show_comments -%}
              <p class="article__meta">
                {%- if show_date -%}
                  {%- assign published_at = article.published_at | date: format: 'full_date' -%}
                  <time datetime="{{ published_at }}">{{ published_at }}</time>
                {%- endif -%}

                {%- if show_author -%}
                  <span class="article__meta__author">
                    {{- 'blogs.article.by_author' | t: author: article.author }}
                  </span>
                {%- endif -%}

                {%- if show_comments and article.comments_count > 0 -%}
                  <span class="comment-count">
                    {{- 'blogs.comments.comments_with_count' | t: count: article.comments_count -}}
                  </span>
                {%- endif -%}
              </p>
            {%- endif -%}

            <p class="article__excerpt">{{ article.excerpt_or_content | strip_html | truncatewords: 30 }}</p>

            {%- if show_button -%}
              <div class="article__link">
                <a href="{{ article.url }}" class="btn btn--text" aria-label="{{ 'blogs.article.read_more' | t }}">
                  <span>{{ 'blogs.article.read_more' | t }}</span>
                </a>
              </div>
            {%- endif -%}
          </div>
        </article>
      {%- endfor -%}
    {%- endif -%}
  </div>
</div>
