{%- liquid 
  
  assign size = blank

  if block.settings.block_radius != "none"
    assign block_radius = block.settings.block_radius
  elsif section.settings.block_radius != "none"
    assign block_radius = section.settings.block_radius
  else
    assign block_radius = settings.block_radius
  endif

-%}

{%- capture classes -%}
  {%- if block_radius != "none" -%}
    {%- if block_radius != blank -%}
      {%- capture size -%}-{{ block_radius }}{%- endcapture -%}
    {%- endif -%}
    block-radius{{ size }}
  {%- endif -%}
{%- endcapture -%}

{%- if classes contains 'block-radius' -%}
  {%- assign classes = classes | append: " overflow-hidden" -%}
{%- endif -%}

{{ classes }}