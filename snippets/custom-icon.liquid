<!-- /snippets/custom-icon.liquid -->

{%- unless icon == blank -%}
  
  {%- case icon -%}
    {%- when 'custom-sparkle' -%}
      <svg width="21" height="20" viewBox="0 0 21 20" fill="none" aria-hidden="true" focusable="false" role="presentation" class="icon icon--fill icon-{{ icon }}">
        <path d="M16.5 1.9502C16.7095 1.95026 16.8933 2.08872 16.9512 2.29004L17.0508 2.62598C17.2806 3.36836 17.4945 3.83236 17.8311 4.16895C18.2158 4.55364 18.767 4.77785 19.71 5.04883L19.7822 5.07617C19.9436 5.15307 20.0498 5.31671 20.0498 5.5C20.0498 5.68336 19.9438 5.84698 19.7822 5.92383L19.71 5.95117C18.767 6.22228 18.2158 6.44642 17.8311 6.83105C17.4945 7.16761 17.2806 7.63166 17.0508 8.37402L16.9512 8.70996C16.8935 8.91106 16.7094 9.04974 16.5 9.0498C16.3166 9.0498 16.153 8.94365 16.0762 8.78223L16.0488 8.70996C15.778 7.76697 15.5536 7.21577 15.1689 6.83105C14.8324 6.49448 14.3684 6.28056 13.626 6.05078L13.29 5.95117C13.0885 5.89323 12.9502 5.70955 12.9502 5.5C12.9503 5.29055 13.0885 5.10676 13.29 5.04883L13.626 4.94922C14.3684 4.71943 14.8324 4.50556 15.1689 4.16895C15.5536 3.78423 15.7777 3.23297 16.0488 2.29004L16.0762 2.21777C16.1529 2.05632 16.3167 1.9502 16.5 1.9502ZM16.5 3.8291C16.3138 4.24044 16.1059 4.56687 15.8369 4.83594C15.5679 5.10496 15.2415 5.31379 14.8301 5.5C15.2413 5.6862 15.5669 5.89507 15.8359 6.16406C16.1048 6.43288 16.3139 6.75811 16.5 7.16895C16.6862 6.75805 16.8953 6.43281 17.1641 6.16406C17.433 5.89522 17.7588 5.68625 18.1699 5.5C17.7588 5.31385 17.433 5.10483 17.1641 4.83594C16.895 4.56684 16.6864 4.24071 16.5 3.8291Z" fill="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M4.15039 0C4.39185 0 4.6044 0.160577 4.6709 0.392578L4.79004 0.792969C5.06379 1.6771 5.31363 2.21387 5.7002 2.60059C6.14205 3.04252 6.78006 3.30547 7.9082 3.62988L7.99219 3.66211C8.1783 3.75069 8.30073 3.9394 8.30078 4.15039C8.30078 4.36173 8.17837 4.55104 7.99219 4.63965L7.9082 4.6709C6.77998 4.9953 6.14212 5.25834 5.7002 5.7002C5.3135 6.08688 5.06373 6.62373 4.79004 7.50781L4.6709 7.9082C4.6044 8.1402 4.39185 8.30078 4.15039 8.30078C3.93915 8.30078 3.75077 8.17811 3.66211 7.99219L3.62988 7.9082C3.30548 6.77997 3.04247 6.14211 2.60059 5.7002C2.21388 5.31348 1.67711 5.06373 0.792969 4.79004L0.392578 4.6709C0.160087 4.60437 5.74029e-05 4.39157 0 4.15039C0 3.90883 0.160228 3.69663 0.392578 3.62988L0.792969 3.51074C1.67711 3.23699 2.21383 2.98719 2.60059 2.60059C3.0425 2.15872 3.30562 1.52077 3.62988 0.392578L3.66211 0.308594C3.75063 0.122804 3.93933 0.000100758 4.15039 0ZM4.15039 2.1748C3.93222 2.65749 3.68461 3.04389 3.36426 3.36426C3.04389 3.68464 2.65753 3.93229 2.1748 4.15039C2.65758 4.36857 3.04387 4.61712 3.36426 4.9375C3.68441 5.25773 3.93232 5.64356 4.15039 6.12598C4.36853 5.64335 4.61724 5.25784 4.9375 4.9375C5.2578 4.61715 5.64341 4.36847 6.12598 4.15039C5.64345 3.93225 5.25778 3.68358 4.9375 3.36328C4.61723 3.04294 4.36853 2.65747 4.15039 2.1748Z" fill="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M9.5 7C9.73785 7 9.9468 7.158 10.0126 7.38656L10.2149 8.06424C10.6812 9.56428 11.1235 10.5057 11.8089 11.1911C12.5922 11.9745 13.7099 12.4403 15.6134 12.9874C15.8425 13.053 16 13.2623 16 13.5C16 13.7082 15.879 13.8939 15.6955 13.9811L15.6134 14.0126L14.9358 14.2149C13.4359 14.6813 12.4943 15.1234 11.8089 15.8089C11.0256 16.5923 10.5596 17.71 10.0126 19.6134C9.94704 19.8419 9.73776 20 9.5 20C9.26221 19.9999 9.05317 19.8419 8.98744 19.6134L8.7851 18.9358C8.31877 17.4357 7.87748 16.4934 7.19207 15.8079C6.40873 15.0245 5.29017 14.5597 3.38656 14.0126C3.15762 13.947 3.00013 13.7375 3 13.5C3 13.2621 3.15777 13.0532 3.38656 12.9874C5.29024 12.4403 6.40865 11.9745 7.19207 11.1911C7.97529 10.4078 8.44042 9.28978 8.98744 7.38656C9.05291 7.15815 9.26229 7.0001 9.5 7ZM9.5 9.28216C9.07558 10.4423 8.60864 11.2863 7.94802 11.9471C7.28724 12.6079 6.44261 13.0754 5.28216 13.5C6.4425 13.9246 7.28727 14.3913 7.94802 15.052C8.60877 15.7127 9.07552 16.5574 9.5 17.7178C9.92448 16.5575 10.3922 15.7137 11.0529 15.0529C11.7137 14.3922 12.5575 13.9246 13.7178 13.5C12.5574 13.0754 11.7137 12.6078 11.0529 11.9471C10.3922 11.2863 9.92448 10.4426 9.5 9.28216Z" fill="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'custom-plant' -%}
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" aria-hidden="true" focusable="false" role="presentation" class="icon icon-{{ icon }}">
        <path d="M12.3541 9.51917C12.693 7.03676 11.9078 4.42982 9.99947 2.52148C8.06892 4.45204 7.2879 7.09741 7.65734 9.60621" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M9.99883 14.7234C9.99883 10.9887 6.97105 7.96094 3.23633 7.96094C3.23633 11.6957 6.26411 14.7234 9.99883 14.7234ZM9.99883 14.7234C13.7336 14.7234 16.7613 11.6957 16.7613 7.96094C13.0266 7.96094 9.99883 10.9887 9.99883 14.7234Z" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M9.99883 14.7234C9.99883 10.9887 6.97105 7.96094 3.23633 7.96094C3.23633 11.6957 6.26411 14.7234 9.99883 14.7234ZM9.99883 14.7234C13.7336 14.7234 16.7613 11.6957 16.7613 7.96094C13.0266 7.96094 9.99883 10.9887 9.99883 14.7234Z" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M10 17.9264V14.7227" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'custom-vegan' -%}
      <svg width="21" height="20" viewBox="0 0 21 20" fill="none" aria-hidden="true" focusable="false" role="presentation" class="icon icon-{{ icon }}">
        <path d="M14.4349 6.97168L10.4196 17.0402L6.4043 6.97168" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.9198 13.2793C11.1032 11.0988 10.8772 8.59599 11.636 6.71544C12.3953 4.83396 14.0851 3.58118 16.1633 3.24414C16.805 4.36821 17.4046 6.80525 16.6393 8.88395C15.9004 10.8918 14.1643 12.2386 11.9198 13.2793Z" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M7.85953 10.6189C8.60212 9.24949 9.3248 7.50967 8.53175 5.70041C7.57388 3.51476 4.90675 2.87402 4.90675 2.87402C4.90675 2.87402 3.48869 5.24486 4.10397 7.30689C4.5674 8.85967 5.7498 9.88745 7.85953 10.6189Z" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'custom-cruelty-free' -%}
      <svg width="21" height="20" viewBox="0 0 21 20" fill="none" aria-hidden="true" focusable="false" role="presentation" class="icon icon-{{ icon }}">
        <path d="M14.4349 6.97168L10.4196 17.0402L6.4043 6.97168" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.9198 13.2793C11.1032 11.0988 10.8772 8.59599 11.636 6.71544C12.3953 4.83396 14.0851 3.58118 16.1633 3.24414C16.805 4.36821 17.4046 6.80525 16.6393 8.88395C15.9004 10.8918 14.1643 12.2386 11.9198 13.2793Z" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M7.85953 10.6189C8.60212 9.24949 9.3248 7.50967 8.53175 5.70041C7.57388 3.51476 4.90675 2.87402 4.90675 2.87402C4.90675 2.87402 3.48869 5.24486 4.10397 7.30689C4.5674 8.85967 5.7498 9.88745 7.85953 10.6189Z" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'custom-gluten-free' -%}
      <svg width="21" height="20" viewBox="0 0 21 20" fill="none" aria-hidden="true" focusable="false" role="presentation" class="icon icon-{{ icon }}">
        <path d="M5.21764 13.9246C5.74037 13.8315 6.2452 13.7491 6.74646 13.6453C6.82881 13.6274 6.92548 13.52 6.95412 13.4305C7.31574 12.3385 7.32648 11.225 7.14388 10.0793C5.83704 10.8777 5.05294 12.4387 5.21764 13.9246ZM14.3727 5.39612C14.9061 5.30303 15.4217 5.2171 15.9337 5.11685C15.9982 5.10611 16.0805 5.02734 16.102 4.96289C16.46 3.8494 16.5531 2.718 16.2917 1.52573C15.0494 2.48885 14.2474 3.65606 14.3762 5.39612M10.1979 7.24718C8.6548 8.1924 7.98527 10.1544 8.32541 11.0746C8.78011 10.9923 9.23482 10.9278 9.67879 10.824C9.80052 10.7953 9.96522 10.6915 10.001 10.5805C10.3662 9.49924 10.4128 8.38932 10.1944 7.24718M13.2377 4.37213C11.9631 5.33525 11.2076 6.52752 11.2971 8.24252C11.8342 8.15301 12.339 8.07066 12.8403 7.97399C12.9155 7.95967 13.0193 7.8809 13.0443 7.80572C13.4095 6.70296 13.4776 5.57514 13.2377 4.37213ZM19.6824 2.0055C18.9484 1.89093 18.286 2.03056 17.6774 2.43873C17.5735 2.50675 17.4446 2.63565 17.4303 2.7538C17.3301 3.54506 17.2549 4.34349 17.1725 5.14549C18.2216 5.47489 19.7432 3.63457 19.6824 2.00908M10.9892 10.7989C12.697 11.4255 14.1614 11.0245 15.3931 9.50998C14.1972 8.9765 13.0014 8.91921 11.7733 9.26293C11.4153 9.36318 11.1682 9.51714 11.1611 9.99333C11.1575 10.2547 11.0537 10.5161 10.9892 10.7989ZM7.94588 13.6596C9.83274 14.211 11.469 13.7133 12.3104 12.3385C11.0143 11.7799 9.71459 11.7227 8.40417 12.2275C8.31108 12.2633 8.20367 12.3886 8.17503 12.4924C8.08194 12.8648 8.02107 13.2443 7.9423 13.6561M18.4579 6.66716C17.1439 6.09429 15.8442 6.02627 14.5302 6.54184C14.4335 6.58123 14.319 6.68864 14.2903 6.79247C14.1936 7.17557 14.1292 7.56941 14.054 7.95609C15.7583 8.58624 17.2083 8.16733 18.4543 6.66716M9.2563 15.2171C7.60933 14.3148 5.21047 14.7337 4.12562 15.8258C5.6258 17.2901 7.91366 17.018 9.2563 15.2171ZM10.7744 14.9163C10.6312 15.1706 10.5202 15.4534 10.3412 15.6682C10.1228 15.9296 9.85065 16.1373 9.6036 16.37C7.80983 18.0492 6.23088 18.2855 4.08982 17.1756C3.15892 18.0384 2.21728 18.9121 1.27922 19.7821C1.19687 19.8573 1.11452 19.9933 1.02859 19.9969C0.82093 20.0076 0.563142 20.0291 0.416347 19.9074C0.194363 19.7284 0.215845 19.4205 0.416347 19.1985C0.645491 18.9479 0.903278 18.7259 1.15032 18.4967C1.85924 17.8344 2.57173 17.172 3.29139 16.5025C3.17682 16.3592 3.08373 16.2518 2.9978 16.1373C2.76866 15.8222 2.78298 15.525 3.06583 15.2815C3.363 15.0238 3.69597 14.8233 3.99315 14.5619C4.10772 14.4616 4.22945 14.2683 4.22229 14.1287C4.10772 12.2633 4.7307 10.7774 6.04112 9.62097C6.34904 9.35244 6.62472 9.04095 6.94696 8.79748C7.14388 8.6471 7.39809 8.58624 7.66303 8.5576C8.17861 7.48348 9.09877 6.80321 9.91867 6.00837C10.1693 5.76848 10.4199 5.60378 10.7457 5.76848C11.7733 4.04632 13.1267 2.78602 13.8392 2.9185C14.3404 1.70833 15.3358 0.999414 16.2166 0.172346C16.5352 -0.128406 16.8718 -0.0138334 17.0221 0.415812C17.1331 0.734466 17.2083 1.06744 17.3086 1.4219C18.2037 0.920646 19.1274 0.77027 20.1013 0.988673C20.4915 1.07818 20.6813 1.30017 20.6849 1.75487C20.7028 3.22283 20.2266 4.43658 19.2205 5.38896C19.1202 5.48205 19.02 5.57872 18.9018 5.68971C19.1417 5.83293 19.3601 5.9475 19.5642 6.08355C19.9437 6.34134 19.9867 6.71728 19.6466 7.03235C18.7694 7.84152 17.9745 8.78674 16.7966 9.13762C16.9004 9.62097 16.6319 9.86443 16.349 10.1151C15.5435 10.8383 14.8095 11.6761 13.7354 11.9769C13.832 12.4423 13.585 12.6965 13.3021 12.9507C12.4858 13.6811 11.7411 14.5261 10.6706 14.8268C10.7064 14.8591 10.7457 14.8913 10.7815 14.9271" fill="#4E3629" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    {%- when 'custom-paraben-free' -%}
      <svg width="16" height="19" viewBox="0 0 16 19" fill="none" aria-hidden="true" focusable="false" role="presentation" class="icon icon--fill icon-{{ icon }}">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M10.3826 0.812988L10.4851 0.83252C10.9862 0.945542 11.3559 1.33541 11.4412 1.8501L11.4539 1.94873C11.4952 2.44139 11.2313 2.91247 10.7781 3.13916L10.7625 3.14697V6.97998L10.7683 7.07861C10.7802 7.17455 10.8102 7.26284 10.8621 7.35596L15.1629 15.0034L15.2888 15.2524C15.8164 16.4241 15.5071 17.7031 14.5076 18.4536L14.283 18.606C14.054 18.746 13.7927 18.8228 13.5564 18.8931L13.5574 18.894C13.4499 18.9266 13.3428 18.958 13.242 18.9927L13.2254 18.9976H3.05153L3.03982 18.9946L2.98806 18.9819L2.98318 18.981C2.95543 18.9726 2.92993 18.9666 2.89919 18.9604C2.18252 18.83 1.6172 18.4747 1.22439 17.9106L1.30446 17.854L1.30349 17.853L1.22146 17.9106C0.90132 17.4512 0.728525 16.9738 0.707785 16.4839C0.687131 15.9941 0.819258 15.4986 1.09743 15.0034L5.43532 7.27979C5.4745 7.20744 5.49099 7.15075 5.49099 7.11768L5.49489 4.63232V3.14502L5.39821 3.09229C4.93365 2.80971 4.71116 2.30005 4.83278 1.76709L4.86208 1.66162C5.02584 1.14716 5.4789 0.802812 6.02224 0.799316H10.2332C10.2864 0.799316 10.3388 0.806735 10.3826 0.812988ZM5.81032 12.0972C5.19622 11.9964 4.58607 11.99 4.00173 12.0796L3.9988 12.0815C3.90922 12.2399 3.81944 12.398 3.72927 12.5552L3.45681 13.0317C3.09155 13.6728 2.71604 14.3291 2.35427 14.9878L1.99782 15.647C1.73642 16.1395 1.74677 16.705 2.02126 17.1636L2.13552 17.3296C2.42473 17.6944 2.86832 17.9075 3.36306 17.9077L8.03298 17.9155C9.62479 17.9155 11.2486 17.9125 12.8924 17.9077L13.1023 17.895C13.5813 17.8352 13.9961 17.5705 14.239 17.1665L14.3299 16.9897C14.4866 16.6276 14.4953 16.2182 14.3523 15.8374L14.2683 15.6499C14.106 15.3412 13.9362 15.0315 13.7644 14.7241L13.2478 13.812C13.1158 13.5832 12.9871 13.3539 12.8582 13.1255C12.2751 13.309 11.7087 13.4152 11.1297 13.4497L11.1228 13.353L11.1287 13.4526C9.95789 13.5212 8.83212 13.299 7.77907 12.7954C7.16816 12.4996 6.51095 12.214 5.81032 12.0972ZM6.10036 1.88916C6.0255 1.88922 5.97285 1.90898 5.94118 1.93408C5.91174 1.95759 5.89443 1.99004 5.89431 2.03369C5.89431 2.07695 5.90689 2.10483 5.92653 2.12451C5.95869 2.15662 6.0214 2.186 6.1072 2.18604H6.68337L6.80349 2.19678C6.92006 2.21713 7.02375 2.26445 7.10525 2.33545C7.21495 2.43115 7.27809 2.56648 7.28005 2.72314L7.27028 2.83838C7.24828 2.94896 7.19217 3.04552 7.10915 3.11963C6.99927 3.21754 6.84735 3.27126 6.67653 3.2749L6.67458 3.27588H6.58767V4.15576C6.58767 5.15821 6.58728 6.16461 6.58083 7.16748C6.57818 7.30759 6.5481 7.45588 6.49587 7.58936L6.4363 7.71729L5.02712 10.2358L4.64431 10.9155C4.68743 10.9177 4.72993 10.9209 4.77224 10.9224L5.26345 10.9458C5.42823 10.9559 5.59362 10.9689 5.75661 10.9868L6.00661 11.021C6.58722 11.1121 7.16077 11.2908 7.71364 11.5571L8.20193 11.7856C8.69737 12.0073 9.2177 12.2011 9.77907 12.2935L10.115 12.3384C10.8563 12.4173 11.5781 12.3534 12.3064 12.1479L11.739 11.1392C11.0976 10.0013 10.4524 8.85916 9.81716 7.71436L9.90212 7.66748L9.89821 7.65967L9.8113 7.70947C9.7446 7.59205 9.69758 7.4531 9.67849 7.31299L9.6697 7.17334C9.66647 6.17699 9.66579 5.18021 9.66579 4.18408V3.27197H9.532C9.21968 3.25672 8.98442 3.04379 8.97634 2.73975V2.73779C8.97256 2.42431 9.22066 2.19737 9.54372 2.18604H10.159C10.236 2.18604 10.2891 2.1659 10.3201 2.14111C10.3489 2.11799 10.366 2.08535 10.366 2.04053V2.03564L10.3582 1.98486C10.3526 1.97017 10.3443 1.95727 10.3338 1.94678C10.298 1.91421 10.2381 1.8892 10.1531 1.88916H6.10036Z" fill="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'custom-sulfate-free' -%}
      <svg width="23" height="20" viewBox="0 0 23 20" fill="none" aria-hidden="true" focusable="false" role="presentation" class="icon icon-{{ icon }}">
        <path d="M21.6955 18.3099C21.4306 18.6787 21.0367 18.9222 20.5892 18.9938C20.1416 19.0654 19.6941 18.9616 19.3253 18.6966C18.9565 18.4317 18.7131 18.0378 18.6415 17.5903C18.4911 16.6665 19.1212 15.7929 20.045 15.639C20.1381 15.6247 20.2312 15.6175 20.3207 15.6175C20.6715 15.6175 21.0153 15.7285 21.3088 15.9361C21.6776 16.2011 21.9175 16.5949 21.9927 17.0425C22.0643 17.49 21.9605 17.9412 21.6955 18.3064M14.9644 15.7679C14.6995 16.1366 14.3092 16.3801 13.8581 16.4517C13.4105 16.5233 12.963 16.4195 12.5942 16.1545C12.2254 15.8896 11.982 15.4958 11.9104 15.0482C11.76 14.1245 12.3901 13.2509 13.3139 13.1005C13.407 13.0862 13.5 13.079 13.5896 13.079C13.944 13.079 14.2841 13.19 14.5777 13.4012C14.9465 13.6662 15.19 14.06 15.2616 14.5076C15.3332 14.9551 15.2294 15.4027 14.9644 15.7714M7.21648 2.95369C7.0661 2.02995 7.69625 1.15634 8.61998 1.00596C9.54372 0.855586 10.4173 1.48573 10.5677 2.40947C10.6429 2.85702 10.5355 3.30815 10.2705 3.67334C10.0056 4.04212 9.61175 4.28559 9.1642 4.3572C8.24404 4.50757 7.36685 3.87743 7.21648 2.95369ZM17.2594 5.65329C17.5244 5.28452 17.9182 5.04105 18.3658 4.96944C19.2895 4.82265 20.1631 5.44921 20.3135 6.37295C20.4639 7.29669 19.8337 8.1703 18.91 8.32426C17.9898 8.47463 17.1126 7.84449 16.9623 6.92075C16.8871 6.4732 16.9945 6.02207 17.2594 5.65687M4.83194 17.4184C4.56699 17.7872 4.17315 18.0307 3.72561 18.1023C2.80545 18.2526 1.92825 17.6225 1.77788 16.6988C1.6275 15.775 2.25765 14.9014 3.18139 14.7475C3.27448 14.7331 3.36399 14.726 3.45708 14.726C4.2734 14.726 4.99664 15.3203 5.13269 16.151C5.2043 16.5985 5.10047 17.0496 4.83552 17.4148M22.9666 16.8885C22.852 16.1796 22.4689 15.5602 21.8853 15.1449C21.3053 14.726 20.5928 14.5577 19.8874 14.6759C19.1821 14.7904 18.5627 15.1735 18.1438 15.7571C18.0686 15.861 18.0149 15.9684 17.9576 16.0794L16.2068 15.3525C16.2784 15.0267 16.2927 14.6866 16.2355 14.3465C16.1495 13.8058 15.9025 13.3189 15.5337 12.9322L17.6533 9.13342C17.9612 9.25874 18.2942 9.33034 18.6415 9.33034C18.7847 9.33034 18.9279 9.3196 19.0747 9.29454C20.5319 9.05824 21.5273 7.67621 21.2909 6.21541C21.0511 4.75462 19.669 3.76285 18.2118 3.99916C17.5065 4.11373 16.8835 4.49683 16.4682 5.08043C16.0493 5.66045 15.881 6.37295 15.9991 7.07829C16.0994 7.69053 16.4037 8.21327 16.8262 8.60353L14.7353 12.3522C14.3593 12.1732 13.9512 12.0944 13.5323 12.1051L9.95547 5.13772C10.3994 4.94796 10.7861 4.65079 11.0761 4.24979C11.495 3.66976 11.6633 2.95727 11.5452 2.25193C11.2981 0.791139 9.92324 -0.204207 8.45887 0.0356787C6.99807 0.271984 6.00631 1.65401 6.24261 3.11481C6.45743 4.4288 7.59958 5.36328 8.88851 5.36328C8.91 5.36328 8.93148 5.3597 8.95296 5.3597L12.5262 12.32C12.0822 12.5097 11.6955 12.8069 11.4055 13.2079C11.0833 13.6554 10.915 14.1746 10.9043 14.7117L5.9705 15.5172C5.54086 14.325 4.30921 13.5695 3.01669 13.7808C1.5559 14.0171 0.56413 15.3991 0.800435 16.8599C1.01526 18.1739 2.1574 19.1084 3.44634 19.1084C3.58955 19.1084 3.73277 19.0976 3.87956 19.0726C4.5849 18.958 5.2043 18.5749 5.62321 17.9913C5.94544 17.5473 6.11372 17.0246 6.12446 16.4875L11.0582 15.6819C11.2408 16.1868 11.5666 16.6272 12.0106 16.9494C12.4725 17.2824 13.0131 17.4542 13.5716 17.4542C13.7184 17.4542 13.8617 17.4435 14.0085 17.4184C14.7138 17.3038 15.3332 16.9207 15.7521 16.3371C15.7736 16.3085 15.7843 16.2763 15.8058 16.2476L17.6569 17.0138C17.6282 17.2537 17.6246 17.5008 17.664 17.7442C17.7786 18.4496 18.1617 19.0726 18.7453 19.4915C19.2072 19.8244 19.7478 19.9963 20.3063 19.9963C20.4496 19.9963 20.5964 19.9856 20.7431 19.9605C21.4485 19.8459 22.0715 19.4628 22.4868 18.8792C22.9057 18.2992 23.074 17.5903 22.9558 16.8814" fill="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'custom-mineral-oil-free' -%}
      <svg width="21" height="20" viewBox="0 0 21 20" fill="none" aria-hidden="true" focusable="false" role="presentation" class="icon icon-{{ icon }}">
        <path d="M16.3648 13.1428C16.3648 10.8428 14.8652 8.45819 13.4108 6.15816C12.3284 4.44441 11.3081 2.82086 11.06 1.46226C11.0093 1.19167 10.7782 1 10.5076 1C10.237 1 10.0002 1.19731 9.95512 1.46226C9.70708 2.82086 8.68672 4.44441 7.60435 6.15816C6.15556 8.46383 4.65039 10.8428 4.65039 13.1428C4.65039 16.373 7.27739 19 10.5076 19C13.7378 19 16.3648 16.373 16.3648 13.1428ZM5.77786 13.1428C5.77786 11.1697 7.19283 8.92609 8.55706 6.75572C9.30119 5.57188 10.0171 4.43313 10.5076 3.35077C10.998 4.43877 11.714 5.57751 12.4581 6.75572C13.8223 8.92609 15.2373 11.1641 15.2373 13.1428C15.2373 15.7473 13.1177 17.8725 10.5076 17.8725C7.89749 17.8725 5.77786 15.7473 5.77786 13.1428Z" fill="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M14.1103 13.1428C14.1103 12.8328 13.8566 12.5791 13.5466 12.5791C13.2365 12.5791 12.9829 12.8328 12.9829 13.1428C12.9829 14.5071 11.8723 15.6176 10.5081 15.6176C10.198 15.6176 9.94434 15.8713 9.94434 16.1814C9.94434 16.4914 10.198 16.7451 10.5081 16.7451C12.4924 16.7451 14.1103 15.1272 14.1103 13.1428Z" fill="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'custom-heart' -%}
      <svg width="14" height="13" viewBox="0 0 14 13" fill="none" aria-hidden="true" focusable="false" role="presentation" class="icon icon-{{ icon }}">
        <path d="M7 13C10.9464 9.36838 14 6.04142 14 3.35764C14 1.41171 12.6502 0 10.7891 0C9.16394 0 7.8126 1.25991 7 2.70335C6.1874 1.25991 4.83556 0 3.21086 0C1.34983 0 0 1.41224 0 3.35764C0 6.04142 3.05364 9.36838 7 13Z" fill="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'custom-tick' -%}
      <svg width="16" height="14" viewBox="0 0 16 14" fill="none" aria-hidden="true" focusable="false" role="presentation" class="icon icon-{{ icon }}">
        <path d="M2 8.5L5.43566 12L14 2" stroke="#4E3629" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
        
    {%- else -%}
      
  {%- endcase -%}
    
{%- endunless -%}