{% comment %}
    Renders an empty product grid item

    Accepts:
    - last_element (optional)
    - index: element index in the loop
    - attributes: Accept html attributes
    - modifier_class: Accept custom class

    Usage:
    {% render 'onboarding-product-grid-item', last_element: lastElement, index: index, placeholder: 'product-3', placeholder_root: 'product-', title: 'Fancy title', animation_delay: animation_delay, index: index, attributes: '', modifier_class: '' %}
{% endcomment %}

{%- liquid
  assign attributes = attributes | default: ''
  assign animation_delay = animation_delay | default: 0
  assign aos_duration = 800
  assign aos_delay = animation_delay | times: 150
  assign product_card_class = ''
  assign modifier_class = modifier_class | default: ''

  assign index = index | default: 1
  assign index = index | modulo: 6 | floor | plus: 1
  assign placeholder_root = placeholder_root | default: 'collection-'
  assign svg_default =  placeholder_root | append: index
  assign placeholder = placeholder | default: svg_default

  if show_product_card
    assign product_card_class = ' product-item--card'
  endif

  if settings.product_grid_center
    assign alignment_class = ' product-item--centered'
  else
    assign alignment_class = ' product-item--left'
  endif

  assign overlay_text_class = ' product-item--outer-text'
  if settings.overlay_text
    assign overlay_text_class = ' product-item--overlay-text'
  endif

  if settings.currency_code_enable
    assign onboarding_price = 9900 | money_with_currency
  else
    assign onboarding_price = 9900 | money
  endif
-%}

{%- capture item_unique -%}product-item--{{ section.id }}-{{ index }}{%- endcapture -%}
{%- assign aos_anchor = '.' | append: item_unique -%}

<div class="grid-item product-item{{ alignment_class }}{{ overlay_text_class }}{{ product_card_class }}{% if modifier_class != '' %} {{ modifier_class }}{% endif %}"
  id="{{ item_unique }}"
  data-grid-item
  {{ attributes }}
>
  <div class="product-item__image" data-product-image>
    <div class="svg-placeholder"
      data-aos="img-in"
      data-aos-delay="{{ aos_delay }}"
      data-aos-duration="{{ aos_duration }}"
      data-aos-anchor="{{ aos_anchor }}"
      data-aos-easing="ease-out-quart">
      {{ placeholder | placeholder_svg_tag }}
    </div>
  </div>
  <div class="product-information">
    <a
      class="product-link product-item__info {{ settings.product_grid_font_size }}"
      href="#"
      data-aos="fade"
      data-aos-delay="{{ aos_delay }}"
      data-aos-duration="{{ aos_duration }}"
      data-aos-anchor="{{ aos_anchor }}">
      {%- assign default_title = 'home_page.onboarding.product_title' | t -%}
      <p class="product-item__title">{{ title | default: default_title }}</p>
      <span class="price">{{ onboarding_price }}</span>
    </a>
  </div>
</div>
