{%- liquid 

  if image == blank
    assign image = block.settings.image
  endif

-%}

{%- if image != blank -%}

  {%- assign image_aspect_ratio = image.aspect_ratio -%}
  {%- assign aspect_ratio = aspect_ratio | default: image_aspect_ratio | default: 1 -%}

  {%- assign desktop_height = desktop_height | default: image.height -%}
  {%- assign desktop_width = desktop_height | times: aspect_ratio -%}

  {%- assign mobile_height = mobile_height | default: width -%}
  {%- assign mobile_width = mobile_height | times: aspect_ratio -%}

  {%- capture classes -%}
    overflow-hidden
    block-radius
    {% if block.settings.link != blank %}tabbed-gallery-image--link{% endif %}
    {{ classes }}
  {%- endcapture -%}

  {%- if block.settings.link != blank -%}
    {%- assign tag = 'a' -%}
  {%- else -%}
    {%- assign tag = 'div' -%}
  {%- endif -%}

  {%- capture style -%}

    --item-height: {{ desktop_height }}px;
    --item-height--mobile: {{ mobile_height }}px;

    --item-width: {{ desktop_width }}px;
    --item-width--mobile: {{ mobile_width }}px;

  {%- endcapture -%}

  {%- capture attributes -%}
    {{ attributes }}
    data-image-id="{{ block.id }}"
    data-grid-item
    {{ block.shopify_attributes }}
    {% if style -%}
      style="{{ style }}"
    {%- endif %}
  {%- endcapture -%}

  {%- capture id -%}
    {%- if id -%}
      id="{{- id -}}"
    {%- endif -%}
  {%- endcapture -%}

  {%- capture content -%}

    {%- capture image_content -%}
      {%- render 'image', image: image, widths: '365, 550, 730', cover: true -%}
    {%- endcapture -%}

    {% if block.settings.video != blank and block.settings.enable_video == true %}
      <script src="{{ 'video-background.js' | asset_url }}" defer="defer"></script>
      <div
        class="video-background {{ desktop_height }} {{ mobile_height }}"
        style="--aspect-ratio: {{ video.aspect_ratio | default: 1 }};"
      >
        <div class="video__poster">
          {{ image_content }}
        </div>
        <video-background
          class="video__player is-loading"
          data-video-player
          data-video-id="{{ section.id }}-video-background"
        >
          <template data-video-template>
            {{ block.settings.video | video_tag: autoplay: true, image_size: '1085x', loop: true, muted: true, controls: false }}
          </template>
        </video-background>
      </div>
    {% else %}
      {{ image_content }}
    {% endif %}

  {%- endcapture -%}

  <div class="tabbed-gallery-image {{ classes }}" {{ attributes }} {{ id }}>
    {%- if content != blank -%}
      {{ content }}
    {%- endif -%}
  </div>
  
{%- endif -%}