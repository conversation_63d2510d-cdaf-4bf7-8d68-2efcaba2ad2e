{%- liquid
  assign current_variant = product.selected_or_first_available_variant
  assign metafield_namespace = block.settings.metafield_namespace | default: 'custom'
  assign metafield_key = block.settings.metafield_key | default: 'what_s_to'
  assign accordion_heading = block.settings.heading | default: 'What\'s To'
  assign accordion_open = block.settings.default_open | default: false
  assign show_icon = block.settings.show_icon | default: false
  assign icon_name = block.settings.icon_name | default: 'icon-award'
  assign icon_image = block.settings.image
  assign icon_size = block.settings.icon_size | default: 20
  assign icon_color = block.settings.icon_color

  comment
    Get the current variant's metafield content
  endcomment
  assign current_content = current_variant.metafields[metafield_namespace][metafield_key].value | default: ''
  
  comment
    Generate metafield data for all variants for JavaScript updates
  endcomment
  capture variant_metafields_data
    assign metafields_data = '['
    for variant in product.variants
      assign metafield_value = variant.metafields[metafield_namespace][metafield_key].value | default: '' | replace: '"', '\"' | replace: newline, '\n'
      assign metafields_data = metafields_data | append: '{"variant_id":"' | append: variant.id | append: '","metafield_value":"' | append: metafield_value | append: '"}'
      unless forloop.last
        assign metafields_data = metafields_data | append: ','
      endunless
    endfor
    echo metafields_data | append: ']'
  endcapture
-%}

{%- if accordion_heading != blank -%}
  <div class="product__block product__block--variant-accordion{% if settings.show_lines %} product__block--lines{% endif %} block-padding" {{ block_style }} {{ block.shopify_attributes }}>
    <collapsible-elements single="true">
      <div class="product-accordion">
        <details
          class="accordion"
          data-collapsible
          data-variant-accordion
          {% if accordion_open %}
            open="true"
          {% endif %}
        >
          <summary class="accordion__title text-badge-lg" data-collapsible-trigger>
            {%- if show_icon -%}
              {%- liquid
                capture icon_style
                  echo 'style="'
                  if icon_size != blank
                    echo '--icon-size: ' | append: icon_size | append: 'px;'
                  endif
                  if icon_color != blank
                    echo ' color: ' | append: icon_color | append: ';'
                  endif
                  echo '"'
                endcapture
              -%}

              <span class="accordion__icon" {{ icon_style }}>
                {%- if icon_image != blank -%}
                  <img src="{{ icon_image | image_url: width: 160 }}" alt="{{ accordion_heading }}" width="{{ icon_size }}" height="{{ icon_size }}" loading="lazy">
                {%- else -%}
                  {%- render icon_name -%}
                {%- endif -%}
              </span>
            {%- endif -%}

            {{ accordion_heading }}

            {%- render 'icon-plus' -%}
            {%- render 'icon-minus' -%}
          </summary>

          <div
            class="accordion__body rte"
            {% if accordion_open != blank %}
              style="height: auto;"
            {% endif %}
            data-collapsible-body
          >
            <div class="accordion__content" data-collapsible-content data-variant-accordion-content>
              {{ current_content }}
            </div>
          </div>
        </details>
      </div>
    </collapsible-elements>

    {%- comment -%} Store variant metafield data for JavaScript {%- endcomment -%}
    <script type="application/json" data-variant-accordion-metafields>
      {{ variant_metafields_data }}
    </script>
  </div>
{%- endif -%}
