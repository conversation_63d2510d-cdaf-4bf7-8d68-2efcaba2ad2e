<div class="product__block product__block--meta block-padding" {{ block_style }}

  {% if animation_name %}
    data-animation="{{ animation_name }}"
    data-animation-duration="{{ animation_duration }}"
    data-animation-delay="{{ animation_delay }}"
  {% endif %}>

  <div class="product__block__wrapper" {{ block.shopify_attributes }}>

    {%- capture meta_top -%}

      {%- if block.settings.byline_show -%}

        {%- capture byline -%}
          {%- if block.settings.byline != blank -%}
            {{- block.settings.byline -}}
          {%- elsif settings.show_cutline and product.metafields.theme.cutline != blank and product.metafields.theme.cutline.type == 'single_line_text_field' -%}
            {%- assign product_title_downcase = product_title | strip_html | escape | downcase -%}
            {%- assign cutline_downcase = product.metafields.theme.cutline.value | downcase -%}
            {%- unless product_title_downcase contains cutline_downcase -%}
              {{- product.metafields.theme.cutline.value -}}
            {%- endunless -%}
          {%- endif -%}
        {%- endcapture -%}

        {%- if byline != blank -%}
          <span class="meta-byline">
            <span class="color--text-light">{{ byline }}</span>
          </span>
        {%- endif -%}

      {%- endif -%}

      {%- if block.settings.volume_show -%}

        {%- capture volume -%}
          {%- if product.metafields.custom.product_size != blank -%}
            <span class="meta-volume__field color--text-light">
              {{ product.metafields.custom.product_size }}
            </span>
          {%- else -%}
            {%- if product.metafields.volume.metric_ml -%}
              <span class="meta-volume__field meta-volume__metric color--text-light">
                {% render 'round-value', value: product.metafields.volume.metric_ml %}ml
              </span>
            {%- endif -%}
            {%- if product.metafields.volume.imperial_floz -%}
              <span class="meta-volume__field meta-volume__imperial color--text-light">
                {% render 'round-value', value: product.metafields.volume.imperial_floz %}Fl.Oz
              </span>
            {%- endif -%}
          {%- endif -%}
        {%- endcapture -%}

        {%- if volume != blank -%}
          <span class="meta-volume">
            {{ volume }}
          </span>
        {%- endif -%}

      {%- endif -%}
    {%- endcapture -%}

    {%- capture meta_bottom -%}
      {%- if block.settings.rating_show -%}
        {%- if block.settings.rating_liquid -%}
          <div class="note note--sm note--inline">
            <p>Stamped / Rating Widget Placeholder</p>
          </div>
          {% comment %} {{ block.settings.rating_liquid }} {% endcomment %}
        {%- else -%}
          <div class="note note--sm">
            <p>Stamped / Rating Widget Placeholder</p>
          </div>
        {%- endif -%}
      {%- endif -%}
    {%- endcapture -%}

    <div class="product-meta">

      {%- if meta_top != blank -%}
        <div class="product-meta__top">
          {{ meta_top }}
        </div>
      {%- endif -%}
      {%- if meta_top != blank and meta_bottom != blank -%}
        <hr class="hr--small">
      {%- endif -%}
      {%- if meta_bottom != blank -%}
        <div class="product-meta__bottom">
          {{ meta_bottom }}
        </div>
      {%- endif -%}
    </div>

  </div>

</div>