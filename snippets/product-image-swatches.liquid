{%- if settings.swatches_type != 'disabled' and settings.show_grid_swatches -%}
  {%- assign has_colors = false -%}
  {%- assign swatch_translation = 'general.swatches.color' | t -%}
  {%- assign swatch_labels = swatch_translation | append: ',' | split: ',' -%}

  {%- capture swatches -%}
    {%- for label in swatch_labels -%}
      {%- assign sanitized_label = label | lstrip | rstrip -%}

      {%- if settings.swatches_type == 'theme' -%}
        {%- if product.options_by_name[sanitized_label].values.size > 0 -%}
          {%- assign has_colors = true -%}

          {%- capture swatch_values -%}
            {%- for value in product.options_by_name[sanitized_label].values -%}
              {{- value -}}: {{- value | handle -}}
              {%- unless forloop.last -%}, {%- endunless -%}
            {%- endfor -%}
          {%- endcapture -%}
        {%- endif -%}
      {%- elsif settings.swatches_type == 'native' -%}
        {%- capture swatch_values -%}
          {%- for value in product.options_by_name[sanitized_label].values -%}
            {%- if value.swatch -%}
              {%- assign has_colors = value -%}
            {%- endif -%}

            {%- liquid
              if value.swatch.image
                assign image_url = value.swatch.image | image_url: width: 48
                assign swatch_value = 'url(' | append: image_url | append: ')'
              elsif value.swatch.color
                assign swatch_value = value.swatch.color
              else
                assign swatch_value = nil
              endif
            -%}
            {{- value -}}: {{- swatch_value -}}
            {%- unless forloop.last -%}, {%- endunless -%}
          {%- endfor -%}
        {%- endcapture -%}
      {% endif %}

      {%- if has_colors -%}
        <native-scrollbar class="radio__fieldset radio__fieldset--swatches radio__fieldset--pgi" data-grid-swatch-fieldset>
          <grid-swatch class="selector-wrapper__scrollbar"
            data-scrollbar
            data-swatch-handle="{{ product.handle }}"
            data-swatch-label="{{ label }}"
            data-swatch-values="{{ swatch_values | strip_html | escape }}">
          </grid-swatch>

          {%- if settings.collection_swatch_style == 'text-slider' or settings.collection_swatch_style == 'slider' -%}
            <div class="selector-wrapper__actions">
              <button type="button" class="radio__fieldset__arrow radio__fieldset__arrow--prev is-hidden" data-scrollbar-arrow-prev>
                {%- render 'icon-nav-arrow-left' -%}
                <span class="visually-hidden">{{ 'products.general.see_all' | t }}</span>
              </button>
              <button type="button" class="radio__fieldset__arrow radio__fieldset__arrow--next is-hidden" data-scrollbar-arrow-next>
                {% render 'icon-nav-arrow-right' %}
                <span class="visually-hidden">{{ 'products.general.see_all' | t }}</span>
              </button>
            </div>
          {%- endif -%}
        </native-scrollbar>

        {%- break -%}
      {%- endif -%}
    {%- endfor -%}
  {%- endcapture -%}
{%- endif -%}

{%- if swatches -%}
  <div class="product-item__swatches__holder product-item__swatches__holder--{{ settings.swatch_style }} product-item__swatches__holder--{{ settings.collection_swatch_style }}">
    {{ swatches }}
  
    <span class="product-item__swatches__count">
      <span data-swatch-count>&nbsp;</span>
    </span>
  </div>
{%- endif -%}