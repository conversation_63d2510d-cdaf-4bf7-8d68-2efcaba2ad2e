{%- liquid
  assign upsell_modifier = upsell_modifier | default: ''
  assign upsell_button_text = 'products.general.upsell_add_to_cart' | t
  assign upsell_quick_add_hide = upsell_quick_add_hide | default: false
  assign upsell_description = upsell_description | default: ''
  assign upsell_description_font_size = upsell_description_font_size | default: ''
  assign is_product_block = is_product_block | default: false

  if upsell_product == blank
    assign show_upsell = false
    assign upsell_error = true
  endif

  if request.design_mode or upsell_product != blank
    assign show_upsell = true
  endif

  assign upsell_variant = upsell_product.selected_or_first_available_variant
  assign upsell_variant_image = upsell_variant.featured_media | default: upsell_product.featured_media.preview_image

  if settings.currency_code_enable
    assign upsell_variant_price = upsell_variant.price | money_with_currency
    assign upsell_variant_compare_at_price = upsell_variant.compare_at_price | money_with_currency
  else
    assign upsell_variant_price = upsell_variant.price | money
    assign upsell_variant_compare_at_price = upsell_variant.compare_at_price | money
  endif

  assign unique = section.id | append: '-' | append: upsell_product.id
  assign product_form_id = 'ProductFormUpsell--' | append: unique

  assign sibling_color = upsell_product.metafields.theme.sibling_color.value | default: upsell_product.metafields.theme.sibling_colour.value | default: upsell_product.metafields.theme.siblings_color.value | default: upsell_product.metafields.theme.siblings_colour.value | default: upsell_product.metafields.theme.siblings_colors.value | default: upsell_product.metafields.theme.siblings_colours.value

  assign is_product_list = is_product_list | default: false
  assign show_available_upsell_only = show_available_upsell_only | default: false

  unless product.available
    assign sold_out = true
  endunless

  assign all_variants_soldout = true
  for variant in upsell_product.variants
    if variant.available
      assign all_variants_soldout = false
      break
    endif
  endfor

  if all_variants_soldout
    assign upsell_quick_add_hide = true
  endif

  if all_variants_soldout and settings.show_sold_out_badge
    assign badge_soldout = 'products.product.sold_out' | t
  endif

  if request.design_mode == false and upsell_variant.available == false and show_available_upsell_only and is_product_block
    assign show_upsell = false
  endif

  assign product_title = 'products.general.upsell_error_title' | t
  assign product_info = 'products.general.upsell_error_info' | t

  if onboarding
    assign show_upsell = true
    assign upsell_error = true
    assign product_title = 'products.general.choose_product' | t
    assign product_info = 'products.general.product_info' | t
  endif

  if upsell_color_scheme
    assign color_scheme = 'color-' | append: upsell_color_scheme
  endif

-%}

{%- if show_upsell -%}
  <quick-add-product
    {% if slider_enabled %}
      data-slide
    {% endif %}
  >
    {{ color_scheme }}
    <div
      class="product-upsell__holder{% if upsell_error %} product-upsell__holder--onboarding{% endif %}{% if is_cart %} product-upsell__holder--cart{% elsif upsell_quick_add_hide == false %} product-upsell__holder--button{% endif %}"
      data-quick-add-holder="{{ upsell_product.id }}"
    >
      <div data-cart-errors-container class="product-upsell__errors"></div>

      <div class="product-upsell {{ color_scheme }} {% if upsell_modifier != '' %} {{ upsell_modifier }}{% endif %}">
        <div class="product-upsell__image">
          {%- if upsell_error -%}
            <div class="product-upsell__image__thumb svg-placeholder" role="img">
              {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg' }}
            </div>
          {%- else -%}
            <a
              href="{{ upsell_product.url }}"
              class="product-upsell__image__link"
              aria-label="{{ upsell_product.title | strip_html | escape }}"
            >
              <div class="product-upsell__image__thumb">
                {%- render 'image',
                  image: upsell_variant_image,
                  widths: '120, 180, 240, 300, 360',
                  width: '240',
                  sizes: '120px',
                  cover: true
                -%}
              </div>
            </a>
          {%- endif -%}
        </div>

        <div class="product-upsell__content">
          {%- if upsell_error -%}
            <p class="product-upsell__title text-product-title">{{ product_title }}</p>

            {%- if sibling_color != blank -%}
              <p class="product-upsell__variant-title text-badge-lg">{{ 'general.siblings.label' | t }}: {{ sibling_color }}</p>
            {%- endif -%}

            <p class="product-upsell__variant-title h4">{{ product_info }}</p>

            {%- if onboarding and upsell_quick_add_hide == false -%}
              <div class="product-upsell__actions">
                <div class="btn__outer">
                  <button
                    class="product-upsell__btn"
                    type="button"
                  >
                    <span class="btn__plus"></span>
                    <span class="btn__text">{{ upsell_button_text }}</span>
                  </button>
                </div>
              </div>
            {%- endif -%}
          {%- else -%}
            <a href="{{ upsell_product.url }}" class="product-upsell__link">

              <div class="product-upsell__content-top">

                <p class="product-upsell__title text-product-title">{{ upsell_product.title | strip_html }}</p>

                {%- assign product_item_cutline = blank -%}
                {% capture product_item_cutline %}
                  {% if settings.show_cutline and upsell_product.metafields.theme.cutline != blank and upsell_product.metafields.theme.cutline.type == 'single_line_text_field' %}
                    {% assign product_title_downcase = product_title | strip_html | escape | downcase %}
                    {% assign cutline_downcase = upsell_product.metafields.theme.cutline.value | downcase %}
                    {% unless product_title_downcase contains cutline_downcase %}
                      {{ upsell_product.metafields.theme.cutline.value }}
                    {% endunless %}
                  {% endif %}
                {% endcapture %}

                {%- if product_item_cutline -%}
                  <span
                    class="product-item__cutline text-sm"
                    data-product-cutline
                    {% if style != blank %}
                      style="{{ style }}"
                    {% endif %}
                  >
                    {{- product_item_cutline -}}
                  </span>
                {%- endif -%}

                {%- unless sibling_color == blank or upsell_product.title contains sibling_color -%}
                  <p class="product-upsell__variant-title h4">{{ 'general.siblings.label' | t }}: {{ sibling_color }}</p>
                {%- endunless -%}

                {%- if upsell_product.variants.size == 1 and upsell_variant.title != 'Default Title' -%}
                  <p class="product-upsell__variant-title text-badge-lg">
                    {{ upsell_variant.title | replace: '/', '<span>&nbsp;</span>' }}
                  </p>
                {%- endif -%}

              </div>

              <div class="product-upsell__content-bottom price">

                {%- liquid 

                  # "Pair and Save" Discount display
                  # This displays a discount based on a metafield value on the upsell product. 
                  # The discount needs to be applied via automatic discount.
                  
                  assign metafield_discount_percent = upsell_product.metafields.custom.pair_save_percent
                  
                  if metafield_discount_percent != blank and metafield_discount_percent <= 100
                    
                    assign discount_percent = metafield_discount_percent
                    assign discount_percent_multiplier = 100 | minus: metafield_discount_percent | divided_by: 100.00

                    assign upsell_variant_price = upsell_variant.price | times: discount_percent_multiplier
                    assign upsell_variant_compare_at_price = upsell_variant.price
                    
                    if settings.currency_code_enable
                      assign upsell_variant_price_display = upsell_variant_price | money_with_currency
                      assign upsell_variant_compare_at_price_display = upsell_variant_compare_at_price | money_with_currency
                    else
                      assign upsell_variant_price_display = upsell_variant_price | money
                      assign upsell_variant_compare_at_price_display = upsell_variant_compare_at_price | money
                    endif

                  else

                    assign upsell_variant_price = upsell_variant.price
                    assign upsell_variant_compare_at_price = upsell_variant.compare_at_price

                    if settings.currency_code_enable
                      assign upsell_variant_price_display = upsell_variant.compare_at_price | money_with_currency
                      assign upsell_variant_compare_at_price_display = upsell_variant.compare_at_price | money_with_currency
                    else
                      assign upsell_variant_price_display = upsell_variant.compare_at_price | money
                      assign upsell_variant_compare_at_price_display = upsell_variant.compare_at_price | money
                    endif

                  endif
                  
                -%}

                {%- capture mock_discounts -%}
                  
                  {%- capture discount_text -%}
                    {%- if discount_percent -%}
                      {%- assign savings = discount_percent | append: "%" -%}
                      {{ 'misc.pair_and_save' | t: savings: savings }}
                    {%- endif -%}
                  {%- endcapture -%}

                  {%- if discount_text != blank-%}
                    {%- render 'badge', text: discount_text, classes: "badge--xs badge--lighten badge--border" -%}
                  {%- endif -%}

                {%- endcapture -%}

                {%- if mock_discounts != blank and show_discounts == true -%}
                  <div class="product-upsell__discounts">
                    {{ mock_discounts }}
                  </div>
                {%- endif -%}

                <p class="product-upsell__price price {% if upsell_variant_compare_at_price > upsell_variant.price %} sale{% endif %}">

                  <span class="new-price">
                    {%- if upsell_variant.price == 0 -%}
                      {{ 'general.money.free' | t }}
                    {%- else -%}
                      {{ upsell_variant_price_display }}
                    {%- endif -%}
                  </span>

                  {%- if upsell_variant_compare_at_price > upsell_variant_price -%}
                    <span class="old-price">{{ upsell_variant_compare_at_price_display }}</span>
                  {%- endif -%}

                  {%- if upsell_variant.unit_price -%}
                    {%- capture unit_price_separator -%}
                      <span aria-hidden="true">/</span>
                      <span class="visually-hidden">{{ 'general.accessibility.unit_price_separator' | t }}&nbsp;</span>
                    {%- endcapture -%}

                    {%- capture unit_price_base_unit -%}
                      {% if upsell_variant.unit_price_measurement.reference_value != 1 %}
                        {{ upsell_variant.unit_price_measurement.reference_value }}
                      {% endif %}
                      {{ upsell_variant.unit_price_measurement.reference_unit }}
                    {%- endcapture -%}

                    <br>

                    <span class="visually-hidden visually-hidden--inline">
                      {{- 'products.product.unit_price_label' | t -}}
                    </span>

                    <span class="unit">
                      {{ upsell_variant.unit_price | money }}
                      {{ unit_price_separator }}
                      {{ unit_price_base_unit }}
                    </span>
                  {% endif -%}

                  {%- if all_variants_soldout -%}
                    {%- render 'badge', text: badge_soldout, classes: "badge--small badge--soldout" -%}
                  {%- endif -%}

                </p>

              </div>
              
            </a>

            {%- if is_cart and is_product_list == false and show_close == true -%}
              <button
                type="button"
                class="product-upsell__skip"
                title="{{ 'cart.general.remove' | t }}"
                data-skip-upsell-product
              >
                {%- render 'icon-cancel' -%}

                {{- 'cart.general.remove' | t -}}
              </button>
            {%- endif -%}

            {%- if upsell_description != '' -%}
              <div class="product-upsell__description{% if upsell_description_font_size != '' %} {{ upsell_description_font_size }}{% endif %}">
                {{ upsell_description }}
              </div>
            {%- endif -%}

            {%- unless upsell_quick_add_hide -%}
              <div class="product-upsell__actions">
                {%- if upsell_product.variants.size == 1 -%}
                  {%- form 'product', upsell_product, id: product_form_id, class: 'product-upsell__form' -%}
                    <input type="hidden" name="id" value="{{ upsell_variant.id }}">

                    {% comment %} Form ID must match value in product-form.liquid {% endcomment %}
                    <input
                      type="hidden"
                      form="{{ product_form_id }}"
                      name="properties[{{ 'general.siblings.label' | t }}]"
                      value="{{ sibling_color }}"
                    >

                    <div class="btn__outer">
                      <button
                        class="product-upsell__btn"
                        type="submit"
                        name="add"
                        data-quick-add-btn
                        data-add-to-cart
                        {% unless upsell_variant.available %}
                          disabled="disabled"
                        {% endunless %}
                      >
                        <span class="btn__plus"></span>
                        <span class="btn__text">{{ upsell_button_text }}</span>

                        <span class="btn__loader">
                          <svg height="18" width="18" class="svg-loader">
                            <circle r="7" cx="9" cy="9" />
                            <circle stroke-dasharray="87.96459430051421 87.96459430051421" r="7" cx="9" cy="9" />
                          </svg>
                        </span>

                        <span class="btn__added">&nbsp;</span>
                      </button>
                    </div>
                  {%- endform -%}
                {%- else -%}
                  <div class="btn__outer">
                    <button
                      class="product-upsell__btn"
                      type="button"
                      data-quick-add-btn
                      data-quick-add-modal-handle="{{ upsell_product.handle }}"
                    >
                      <span class="btn__plus"></span>
                      <span class="btn__text">{{ upsell_button_text }}</span>

                      <span class="btn__loader">
                        <svg height="18" width="18" class="svg-loader">
                          <circle r="7" cx="9" cy="9" />
                          <circle stroke-dasharray="87.96459430051421 87.96459430051421" r="7" cx="9" cy="9" />
                        </svg>
                      </span>

                      <span class="btn__added">&nbsp;</span>
                    </button>
                  </div>
                {%- endif -%}
              </div>
            {%- endunless -%}
          {%- endif -%}
        </div>
      </div>

      {%- unless upsell_product.has_only_default_variant -%}
        {%- render 'product-quick-add-modal-template', product_id: upsell_product.id, unique: unique -%}
      {%- endunless -%}
    </div>
  </quick-add-product>
{%- endif -%}
