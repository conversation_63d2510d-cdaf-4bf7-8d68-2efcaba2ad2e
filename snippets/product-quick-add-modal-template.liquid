
{% comment %}
    Renders script template for product quick add modal used on Product grid items, Product siblings and Product upsell

    Accepts:
    - product_id: {String} Product ID (required)

    Usage:
    {% render 'product-quick-add-modal-template', product_id: product.id %}
{% endcomment %}

<template data-quick-add-modal-template>
  <dialog class="drawer product-quick-add" data-product-id="{{ product_id }}" data-section-id="{{ unique }}" inert data-quick-add-modal data-scroll-lock-required>
    <form method="dialog">
      <button class="visually-hidden no-js" aria-label="{{ 'general.accessibility.close' | t }}"></button>
    </form>

    <div class="drawer__inner product-quick-add__content" data-product-upsell-container data-scroll-lock-scrollable style="--swatch-size: var(--swatch-size-product)">
      <div class="product-quick-add__close-outer">
        <button class="product-quick-add__close drawer__close" aria-label="{{ 'general.accessibility.close' | t }}" data-quick-add-modal-close autofocus>
          {%- render 'icon-cancel' -%}
        </button>
      </div>

      <div class="product-quick-add__inner" data-product-upsell-ajax></div>

      <div class="loader loader--top"><div class="loader-indeterminate"></div></div>
    </div>
  </dialog>
</template>