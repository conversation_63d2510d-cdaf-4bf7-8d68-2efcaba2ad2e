{%- liquid 

  assign image_aspect_ratio = block.settings.image.aspect_ratio
  assign aspect_ratio = aspect_ratio | default: image_aspect_ratio | default: 1
  
  assign desktop_height = desktop_height | default: block.settings.image.height
  assign desktop_width = desktop_height | times: aspect_ratio
  
  assign mobile_height = mobile_height | default: width
  assign mobile_width = mobile_height | times: aspect_ratio

-%}

{%- capture compare_id -%}
  ProductCompareItem--{{ section.id }}--{{ product.id }}
{%- endcapture -%}

{%- capture classes -%}
  overflow-hidden
  block-radius
  {% if block.settings.link != blank %}product-compare-item--link{% endif %}
  {{ classes }}
{%- endcapture -%}

{%- if block.settings.link != blank -%}
  {%- assign tag = 'a' -%}
{%- else -%}
  {%- assign tag = 'div' -%}
{%- endif -%}

{%- capture attributes -%}
  data-product-compare-item="{{ compare_id }}"
  {% if block -%}
    {{ block.shopify_attributes }}
  {%- endif %}
  {% if style -%}
    style="{{ style }}"
  {%- endif %}
  {{ attributes }}
{%- endcapture -%}

{%- capture id -%}
  {%- if id -%}
    id="{{- id -}}"
  {%- endif -%}
{%- endcapture -%}

{%- capture content -%}

  {%- assign animation_delay = forloop.index0 | modulo: grid_int | times: 1 -%}

  <div class="product-compare-item__product compare-grid__item-image">

    {% comment %}
    {%- render 'image',
      image: product.featured_image,
      widths: '365, 550, 730, 1100, 1460',
      loading: 'eager',
      cover: true
    -%}
    {% endcomment %}

    {%- render 'product-grid-item',
      product: product,
      animation_delay: animation_delay,
      index: index,
      hide_badges: true,
      hide_prices: true,
      sizes: image_sizes
    -%}
  </div>

  {%- capture product_compare_table -%}

    {%- for n in (1..10) -%}

      {%- capture compare_field_label_setting -%}compare_field_{{ n }}_label{%- endcapture -%}
      {%- capture compare_field_value_setting -%}compare_field_{{ n }}_keyvalue{%- endcapture -%}

      {%- assign namespacekey = section.settings[compare_field_value_setting] -%}
      {%- assign namespacekey_array = namespacekey | strip | split: "." -%}
      {%- assign metafield_namespace = namespacekey_array | first -%}
      {%- assign metafield_key = namespacekey_array | last -%}
      {%- assign metafield = product.metafields[metafield_namespace][metafield_key] -%}

      {%- capture compare_field_label -%}{{ section.settings[compare_field_label_setting] }}{%- endcapture -%}

      {%- if compare_field_label != blank -%}
        {% render 'product-compare-field', 
          metafield: metafield,
          label: compare_field_label,
          hide_label: true
        %}
      {%- endif -%}

      
      {% comment %} keyvalue: compare_field_metafield_keyvalue,  {% endcomment %}
    {%- endfor -%}
    
  {%- endcapture -%}

  <div class="product-compare-item__table compare-grid__item-fields">
    {{ product_compare_table }}
  </div>

{%- endcapture -%}

<div class="product-compare-item compare-table__product compare-grid__item {{ classes }}" {{ attributes }} {{ id }}>
  {%- if content != blank -%}
    {{ content }}
  {%- endif -%}
</div>