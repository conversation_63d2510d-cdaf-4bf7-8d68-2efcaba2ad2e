{%- liquid
  assign color_scheme = 'color-' | append: block.settings.color_scheme
-%}

{%- capture classes -%}
  overflow-hidden
  block-radius
  {% if block.settings.link != blank %}product-carousel-item--link{% endif %}
  {{ classes }}
{%- endcapture -%}

{%- if block.settings.link != blank -%}
  {%- assign tag = 'a' -%}
{%- else -%}
  {%- assign tag = 'div' -%}
{%- endif -%}

{%- capture attributes -%}
  {{ attributes }}
  data-review-id
  data-grid-item
  {{ block.shopify_attributes }}
{%- endcapture -%}

{%- capture id -%}
  section-{{ section.id }}--ProductCarousel--block-{{ block.id }}
{%- endcapture -%}

<div class="product-carousel-item {{ classes }}" {{ attributes }} {{ id }}>

  <div class="product-carousel-item__overlay">

    {%- if block.settings.product != blank -%}
      
      {%- assign product = block.settings.product -%}

      {%- if settings.testing_split_title_dash == true -%}
        {% assign product_title = product.title | split: "-" | first | strip_html | escape %}
      {%- else -%}
        {% assign product_title = product.title | strip_html | escape %}
      {%- endif -%}

      {%- capture overlay_content_inner -%}

        {% comment %} Image {% endcomment %}

        {%- capture overlay_content_image -%}
          {%- if block.settings.image_thumbnail -%}
            {%- render 'image' image: block.settings.image_thumbnail, sizes: '200px', loading: 'lazy', preload: 'false', fetchpriority: 'low' -%}
          {%- else -%}
            {%- render 'image' image: product.featured_image, sizes: '200px', loading: 'lazy', preload: 'false', fetchpriority: 'low' -%}
          {%- endif -%}
        {%- endcapture -%}

        {% comment %} Content {% endcomment %}

        {%- capture product_title -%}
          {%- if block.settings.title != blank -%}
            {{- block.settings.title -}}
          {%- else -%}
            {{- product_title -}}
          {%- endif -%}
        {%- endcapture -%}

        {%- capture product_byline -%}
          {%- if block.settings.byline != blank -%}
            {{- block.settings.byline -}}
          {%- elsif settings.show_cutline and product.metafields.theme.cutline != blank and product.metafields.theme.cutline.type == 'single_line_text_field' -%}
            {%- assign product_title_downcase = product_title | strip_html | escape | downcase -%}
            {%- assign cutline_downcase = product.metafields.theme.cutline.value | downcase -%}

            {%- unless product_title_downcase contains cutline_downcase -%}
              {{- product.metafields.theme.cutline.value -}}
            {%- endunless -%}
          {%- endif -%}
        {%- endcapture -%}

        {%- capture product_price -%}
          {%- if block.settings.price != blank -%}
            {{- block.settings.price -}}
          {%- elsif product.price -%}
            {%- render 'product-grid-price', product: product -%}
          {%- endif -%}
        {%- endcapture -%}

        {%- capture overlay_content_text -%}

          {% if product_title != blank %}
            <h3 class="product-carousel-item__title text-product-title">
              {%- if product.url -%}
                <a href="{{ product.url }}">
                  {{ product_title }}
                </a>
              {%- else -%}
                {{ product_title }}
              {%- endif -%}
            </h3>
          {% endif %}
  
          {% if product_byline != blank %}
            <p class="product-carousel-item__byline">{{ product_byline }}</p>
          {% endif %}
  
          {% if product_price != blank %}
            <p class="product-carousel-item__price">{{ product_price }}</p>
          {% endif %}

        {%- endcapture -%}

        {% comment %} DISPLAY {% endcomment %}

        {% if overlay_content_image != blank %}
          <div class="product-carousel-item__overlay-thumbnail">
            {%- if product.url -%}
              <a href="{{ product.url }}">
                {{ overlay_content_image }}
              </a>
            {%- else -%}
              {{ overlay_content_image }}
            {%- endif -%}
          </div>
        {% endif %}

        {% if overlay_content_text != blank %}
          <div class="product-carousel-item__overlay-text">
            {{ overlay_content_text }}
          </div>
        {% endif %}

        {% if block.settings.show_quick_add_button != blank %}

          {%- liquid
            
            if current_variant.available and product.metafields.theme.preorder.type == 'boolean' and product.metafields.theme.preorder.value == true
              assign has_preorder_meta = true
            endif

            if current_variant.available and product.tags contains '_preorder'
              assign has_preorder_tag = true
            endif

            if has_preorder_meta or has_preorder_tag
              assign preorder_name = 'products.product.sale_type' | t | strip_html | escape
              assign preorder_value = 'products.product.pre_order' | t | strip_html | escape
            endif

            -%}

          {%- capture overlay_button_icon -%}
            
            {%- if preorder_name -%}
              {%- assign quick_add_btn_type = 'preorder' -%}
              {%- assign quick_add_btn_text = 'products.product.pre_order' | t -%}
            {%- elsif product.variants.size == 1 -%}
              {%- assign quick_add_btn_type = 'add' -%}
              {%- assign quick_add_btn_text = 'products.product.add_to_cart' | t -%}
            {%- else -%}
              {%- assign quick_add_btn_type = 'quick-add' -%}
              {%- assign quick_add_btn_text = 'products.general.quick_add' | t -%}
            {%- endif -%}

            <span class="btn__plus btn__plus--{{ quick_add_btn_type }}"></span>
            <span class="btn__text">{{ quick_add_btn_text }}</span>

            <span class="btn__loader">
              <svg height="18" width="18" class="svg-loader">
                <circle r="7" cx="9" cy="9" />
                <circle stroke-dasharray="87.96459430051421 87.96459430051421" r="7" cx="9" cy="9" />
              </svg>
            </span>

            <span class="btn__added">&nbsp;</span>

          {%- endcapture -%}

          <div class="product-carousel-item__overlay-button-container">
            <quick-add-product>
              <div class="" data-quick-add-holder="{{ product.id }}" data-tooltip="{{ 'products.product.add_to_cart' | t }}">
                {%- if product.variants.size == 1 -%}
                  {%- assign product_form_id = 'ProductForm--' | append: unique -%}
                  
                  {%- form 'product', product, id: product_form_id -%}
                    <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">

                    {%- if preorder_value != blank -%}
                      <input
                        type="hidden"
                        name="properties[{{ preorder_name }}]"
                        value="{{ preorder_value }}">
                    {%- endif -%}

                    <div class="btn__outer">
                      <button class="product-upsell__btn"
                        type="submit"
                        name="add"
                        aria-label="{{ 'products.product.add_to_cart' | t }}"
                        data-add-to-cart
                        title="{{ 'products.product.add_to_cart' | t }}"
                        data-tooltip="{{ 'products.product.add_to_cart' | t }}"
                        {% unless product.available %}
                          disabled="disabled"
                        {% endunless %}
                      >
                        {{ overlay_button_icon }}
                        {% comment %} <span class="btn__error" data-message-error>&nbsp;</span> {% endcomment %}
                      </button>
                    </div>
                  {%- endform -%}

                {%- else -%}
                  <div class="btn__outer">
                    <button 
                      class="product-upsell__btn"
                      type="button"
                      aria-label="{{ 'products.general.quick_add' | t }}"
                      data-quick-add-btn
                      data-quick-add-modal-handle="{{ product.handle }}"
                      title="{{ 'products.general.quick_add' | t }}"
                      data-tooltip="{{ 'products.general.quick_add' | t }}"
                      {% unless product.available %}
                        disabled="disabled"
                      {% endunless %}
                    >
                      {{ overlay_button_icon }}
                    </button>
                  </div>

                {%- endif -%}

                {%- unless product.has_only_default_variant -%}
                  {%- render 'product-quick-add-modal-template', product_id: product.id, unique: unique -%}
                {%- endunless -%}
              </div>
            </quick-add-product>
          </div>
        {% endif %}

      {%- endcapture -%}

      {%- if overlay_content_inner != blank -%}
        <div class="product-carousel-item__overlay-content block-radius overflow-hidden {{ color_scheme }}">
          {{ overlay_content_inner }}
        </div>
      {%- endif -%}

      {% if block.settings.link != blank %}

        {%- capture link_attributes -%}
          {%- if tag == "a" -%}
            href="{{ block.settings.link }}"
          {%- endif -%}
        {%- endcapture -%}

        <{{ tag }} class="product-carousel-item__link" {{ link_attributes }}>

        </{{ tag }}>

      {% endif %}

      {% comment %}

      ELSE

      {% if block.settings.image_thumbnail != blank %}
        <div class="product-carousel-item__image-thumbnail">
          {%- render 'image' image: block.settings.image_thumbnail, sizes: '200px', loading: 'lazy', preload: 'false', fetchpriority: 'low' -%}
        </div>
      {% endif %}

      {%- capture overlay_content -%}

        {% if block.settings.title != blank %}
          <h3 class="product-carousel-item__title text-product-title">{{ block.settings.title }}</h3>
        {% endif %}

        {% if block.settings.byline != blank %}
          <p class="product-carousel-item__byline text-sm">{{ block.settings.byline }}</p>
        {% endif %}

        {% if block.settings.price != blank %}
          <p class="product-carousel-item__price">{{ block.settings.price }}</p>
        {% endif %}

      {%- endcapture -%}

      {%- if overlay_content != blank -%}
        <div class="product-carousel-item__overlay">
          <div class="product-carousel-item__overlay-content">
            {{ overlay_content }}
          </div>
        </div>
      {%- endif -%}

      {% endcomment %}

    {%- endif -%}

  </div>
  
  {%- capture background -%}

    {%- capture image_content -%}
      {%- render 'image-hero', image: block.settings.image, desktop_height: desktop_height, mobile_height: mobile_height, sizes: image_sizes -%}
    {%- endcapture -%}

    {% if block.settings.video != blank and block.settings.enable_video == true %}
      <script src="{{ 'video-background.js' | asset_url }}" defer="defer"></script>
      <div
        class="video-background {{ desktop_height }} {{ mobile_height }}"
        style="--aspect-ratio: {{ video.aspect_ratio | default: 1 }};"
      >
        <div class="video__poster">
          {{ image_content }}
        </div>
        <video-background
          class="video__player is-loading"
          data-video-player
          data-video-id="{{ section.id }}-video-background"
        >
          <template data-video-template>
            {{ block.settings.video | video_tag: autoplay: true, image_size: '1085x', loop: true, muted: true, controls: false }}
          </template>
        </video-background>
      </div>
    {% else %}
      {{ image_content }}
    {% endif %}

  {%- endcapture -%}

  {%- if background != blank -%}
    <div class="product-carousel-item__background">
      {{ background }}
    </div>
  {%- endif -%}

</div>