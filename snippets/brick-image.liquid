{% comment %}
  Renders image block for custom content or newsletter sections

  Accepts:
  - block: {Object} Block object
  - animation_anchor: {String} ID of the element that will trigger animations

  Usage:
  {% render 'brick-image', block: block, animation_anchor: animation_anchor %}
{% endcomment %}

{%- liquid
  assign title = block.settings.title
  assign text = block.settings.text
  assign link = block.settings.link
  assign link_text = block.settings.link_text

  assign selected_color_scheme = section.settings.color_scheme | strip
  assign scheme_bg_color = settings.color_schemes[selected_color_scheme].settings.section_bg
  assign color_scheme = 'color-' | append: block.settings.color_scheme

  assign bg_color = block.settings.bg_color | default: scheme_bg_color
  assign text_color = block.settings.color
  assign overlay_opacity = block.settings.overlay_opacity | times: 0.01
  assign overlay_color = block.settings.overlay_color
  assign show_overlay_text = block.settings.show_overlay_text
  assign show_text_background = block.settings.show_text_background
  assign desktop_height = section.settings.height
  assign mobile_height = block.settings.mobile_height | default: section.settings.mobile_height
  assign wrapper_width = section.settings.width

  assign hero_transparent = true
  if show_text_background and scheme_bg_color.alpha != 0.0 or scheme_bg_color != blank or bg_color.alpha != 0.0 or bg_color != blank
    assign hero_transparent = false
  endif

  assign show_header_backdrop = false
  if hero_transparent and show_overlay_text
    assign show_header_backdrop = true
  endif

  assign button_style = block.settings.button_style
  if button_style == 'btn--text' and block.settings.show_arrow
    assign button_style = button_style | append: ' btn--text-no-underline'
  endif

  assign animation_order = 0
-%}

{%- capture style -%}
  
  {%- unless bg_color.alpha == 0.0 or bg_color == blank -%}
    --bg: {{ bg_color }};
  {%- endunless -%}

  {%- unless text_color.alpha == 0.0 or text_color == blank -%}
    --text: {{ text_color }};
    --text-light: {{ text_color | color_mix: bg_color, 70 }};
    --text-dark: {{ text_color | color_saturate: 10 | color_darken: 15 }};
  {%- endunless -%}

  {%- unless overlay_color.alpha == 0.0 or overlay_color == blank -%}
    --overlay-bg: {{ overlay_color }};
    --overlay-bg--rgb: {{ overlay_color.red }}, {{ overlay_color.green }}, {{ overlay_color.blue }};
  {%- else -%}
  {%- endunless -%}

  {%- if block.settings.text_max_width != blank -%}
    --content-max-width: {{ block.settings.text_max_width }}%;
  {%- endif -%}

{%- endcapture -%}

{%- capture block_classes -%}
  {%- render 'block-classes' -%}
{%- endcapture -%}

{%- if block_classes contains 'block-radius' and section.settings.width == "wrapper--full" -%}
  {% assign block_classes = block_classes | replace: "block-radius", "" %}
{%- endif -%}

{%- capture image_overlay_content -%}
  
  {%- if block.settings.overlay_title != blank or block.settings.overlay_subheading != blank or block.settings.overlay_content != blank -%}
    <div class="image-overlay__content text-align--{{ block.settings.overlay_text_align }}">

      {%- if block.settings.overlay_subheading != blank -%}
        <p class="image-overlay__subheading {{ block.settings.overlay_subheading_font_size }}">
          {{ block.settings.overlay_subheading }}
        </p>
      {%- endif -%}

      {%- if block.settings.overlay_title != blank -%}

        {%- liquid
          assign heading_tag = 'h3'
          unless section.settings.heading_tag == 'automatic' or section.settings.overlay_heading_tag == blank
            assign heading_tag = section.settings.overlay_heading_tag
          endunless
        -%}

        <{{ heading_tag }} class="image-overlay__title {{ block.settings.overlay_title_font_size }}">
          {{ block.settings.overlay_title }}
        </{{ heading_tag }}>

        {%- if block.settings.overlay_content != blank -%}
          <div class="image-overlay__text {{ block.settings.overlay_content_font_size }}">
            {{ block.settings.overlay_content }}
          </div>
        {%- endif -%}

      {%- endif -%}
      
    </div>
  {%- endif -%}

  {%- if block.settings.overlay_product != blank -%}
    <div class="image-overlay__product">
      {%- render 'product-grid-item', product: block.settings.overlay_product -%}
    </div>
  {%- endif -%}

  {%- if block.settings.overlay_button_text != blank -%}
    <div class="image-overlay__actions">
      <a href="{{ block.settings.overlay_button_url | default: '#!' }}" class="btn btn--full {{ block.settings.overlay_button_style }} {{ block.settings.overlay_button_size }} {{ block.settings.overlay_button_type }}">
        <span>{{ block.settings.overlay_button_text }}</span>
      </a>
    </div>
  {%- endif -%}

{%- endcapture -%}

<div
  class="brick__block {% if section.settings.block_radius == true %}block-radius overflow-hidden{% endif %} {{ block_classes }} {{ color_scheme }}"
  {% if style != blank %}
    style="{{ style }}"
  {% endif %}
  {{ block.shopify_attributes }}
>
  
  <div class="brick__block__image frame">

    {%- if link != blank and link_text == blank -%}
      <a class="frame__item" href="{{ link }}">
    {%- else -%}
      <div class="frame__item">
    {%- endif -%}

    {%- if image_overlay_content != blank or show_overlay_text == true and overlay_opacity != 0.0 -%}

      {% if image_overlay_content != blank %}
        {%- assign overlay_opacity = 1 -%}
      {% endif %}

      <div class="image-overlay" style="--overlay-opacity: {{ overlay_opacity }};">
        {%- if image_overlay_content != blank -%}
          {{ image_overlay_content }}
        {%- endif -%}
      </div>
    {%- endif -%}

    {%- liquid
      capture image_sizes
        if section.blocks.size > 1
          if wrapper_width == 'wrapper--full'
            echo '(min-width: 750px) 50vw, 100vw'
          elsif wrapper_width == 'wrapper--full-padded'
            echo '(min-width: 990px) calc((100vw - 100px) / 2), (min-width: 750px) calc((100vw - 60px) / 2), calc(100vw - 32px - 50px)'
          else
            echo '(min-width: 990px) calc(1100px / 2), (min-width: 750px) calc((100vw - 60px) / 2), calc(100vw - 32px - 50px)'
          endif
        else
          if wrapper_width == 'wrapper--full'
            echo '100vw'
          elsif wrapper_width == 'wrapper--full-padded'
            echo '(min-width: 990px) calc(100vw - 100px), (min-width: 750px) calc(100vw - 60px), calc(100vw - 32px)'
          else
            echo '(min-width: 990px) calc(1100px / 2), (min-width: 750px) calc((100vw - 60px) / 2), calc(100vw - 32px - 50px)'
          endif
        endif
      endcapture
    -%}

    {%- capture image_content -%}
      {%- render 'image-hero', image: block.settings.image, desktop_height: desktop_height, mobile_height: mobile_height, sizes: image_sizes -%}
    {%- endcapture -%}

    {% if block.settings.video != blank and block.settings.enable_video == true %}
      <script src="{{ 'video-background.js' | asset_url }}" defer="defer"></script>
      <div
        class="video-background {{ desktop_height }} {{ mobile_height }}"
        style="--aspect-ratio: {{ video.aspect_ratio | default: 1 }};"
      >
        <div class="video__poster">
          {{ image_content }}
        </div>
        <video-background
          class="video__player is-loading"
          data-video-player
          data-video-id="{{ section.id }}-video-background"
        >
          <template data-video-template>
            {{ block.settings.video | video_tag: autoplay: true, image_size: '1085x', loop: true, muted: true, controls: false }}
          </template>
        </video-background>
      </div>
    {% else %}
      {{ image_content }}
    {% endif %}

    {%- if link != blank and link_text == blank -%}
      </a>
    {%- else -%}
      </div>
    {%- endif -%}

    {%- if title != blank or text != blank or link_text != blank -%}
      <div
        class="hero__content__wrapper frame__item {{ block.settings.flex_align }}{% if show_header_backdrop %} backdrop--linear{% endif %}"
        {% if show_header_backdrop %}
          style="--header-overlay-color: var(--overlay-bg); --header-overlay-opacity: {{ overlay_opacity }};"
        {% endif %}
      >
        <div
          class="hero__content{% if hero_transparent %} hero__content--transparent{% endif %}{% if show_overlay_text %} backdrop--radial{% endif %}"
          {% if overlay_opacity != 0.0 %}
            style="--overlay-opacity: {{ overlay_opacity }};"
          {% endif %}
        >

          {%- if block.settings.spacer == "top" -%}
            <hr class="hero__spacer">
          {%- endif -%}

          {%- if block.settings.subheading != blank -%}
            {%- assign animation_order = animation_order | plus: 1 -%}
            <p class="hero__subheading {{ block.settings.subheading_font_size }}"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}">{{ block.settings.subheading }}</p>
          {%- endif -%}

          {%- if block.settings.spacer == "subheading" -%}
            <hr class="hero__spacer">
          {%- endif -%}

          {%- if title != blank -%}
            {%- liquid
              assign animation_order = animation_order | plus: 1
              assign heading_tag = 'h2'

              unless block.settings.heading_tag == 'automatic' or block.settings.heading_tag == blank
                assign heading_tag = block.settings.heading_tag
              endunless
            -%}

            <{{ heading_tag }}
              class="hero__title {{ block.settings.heading_font_size }}"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
            >
              {{- title | escape -}}
            </{{ heading_tag }}>
          {%- endif -%}

          {%- if block.settings.spacer == "heading" -%}
            <hr class="hero__spacer">
          {%- endif -%}

          {%- if text != blank -%}
            {%- assign animation_order = animation_order | plus: 1 -%}
            <div
              class="hero__description {{ block.settings.text_font_size }}"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
            >
              {{ text }}
            </div>
          {%- endif -%}

          {%- if block.settings.spacer == "text" -%}
            <hr class="hero__spacer">
          {%- endif -%}

          {%- if link_text != blank -%}
            {%- assign animation_order = animation_order | plus: 1 -%}
            <div
              class="hero__button"
              data-aos="hero"
              data-aos-anchor="{{ animation_anchor }}"
              data-aos-order="{{ animation_order }}"
            >
              <a
                class="btn {{ button_style }} {{ block.settings.button_size }} {{ block.settings.button_type }} {% if block.settings.button_full == true %}btn--full{% endif %}"
                href="{{ link | default: '#!' }}"
              >
                <span>{{ link_text | escape }}</span>

                {%- if block.settings.show_arrow -%}
                  {%- render 'icon-arrow-right' -%}
                {%- endif -%}
              </a>
            </div>
          {%- endif -%}

          {%- if block.settings.spacer == "button" -%}
            <hr class="hero__spacer">
          {%- endif -%}

          {%- if block.settings.spacer == "bottom" -%}
            <hr class="hero__spacer">
          {%- endif -%}

        </div>
      </div>
    {%- endif -%}
  </div>
</div>
