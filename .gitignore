# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

# =========================
# Operating System Files
# =========================

# OSX
# =========================

.DS_Store
.AppleDouble
.LSOverride

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# =========================
# Gulp Workflow Exclusions
# =========================

# assets/vendor.js
# assets/vendor.min.js
# assets/vendor.js.map
# assets/vendor.min.js.map

# assets/theme.js
# assets/theme.min.js
# assets/theme.js.map
# assets/theme.min.js.map

# assets/theme.css
# assets/theme.min.css
# assets/theme.css.map
# assets/theme.min.css.map

# assets/custom.js
# assets/custom.min.js
# assets/custom.js.map
# assets/custom.min.js.map

# assets/custom.css
# assets/custom.min.css
# assets/custom.css.map
# assets/custom.min.css.map

# assets/account.css
# assets/account.min.css
# assets/account.css.map
# assets/account.min.css.map

# assets/style-guide.css
# assets/style-guide.css.map


_templates

# =========================
# Shopify Themekit
# =========================

theme.lock
config.yml
*/node_modules
package-lock.json